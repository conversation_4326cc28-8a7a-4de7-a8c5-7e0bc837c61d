package com.lubansoft.msg.common.model;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 消息发送响应模型
 */
@Data
public class MessageResponse {

    /**
     * 状态
     */
    private String status;

    /**
     * 消息ID
     */
    private String messageId;
    
    /**
     * 请求ID
     */
    private String requestId;
    
    /**
     * 预计送达时间
     */
    private LocalDateTime deliveryTime;
    
    /**
     * 响应时间
     */
    private LocalDateTime timestamp;
    
    // Constructors
    public MessageResponse() {
        this.timestamp = LocalDateTime.now();
    }
    
    public MessageResponse(String messageId, String requestId) {
        this.messageId = messageId;
        this.requestId = requestId;
    }
    
    public static MessageResponse success() {
        MessageResponse response = new MessageResponse();
        response.setStatus("SUCCESS");
        return response;
    }
    
    public static MessageResponse success(String messageId, String requestId) {
        MessageResponse response = new MessageResponse(messageId, requestId);
        response.setStatus("ACCEPTED");
        return response;
    }
    
}