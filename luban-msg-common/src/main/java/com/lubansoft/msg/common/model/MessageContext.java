package com.lubansoft.msg.common.model;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 消息上下文模型
 * 包含消息处理过程中的完整上下文信息
 */
public class MessageContext {
    
    /**
     * 消息ID
     */
    private String messageId;
    
    /**
     * 请求ID
     */
    private String requestId;
    
    /**
     * 应用ID
     */
    private String appId;
    
    /**
     * 模板ID
     */
    private String templateId;
    
    /**
     * 接收者信息
     */
    private MessageReceiver receiver;
    
    /**
     * 渲染后的消息内容
     */
    private String renderedContent;
    
    /**
     * 消息类型
     */
    private String messageType;
    
    /**
     * 优先级
     */
    private Integer priority;
    
    /**
     * 定时发送时间
     */
    private LocalDateTime scheduleTime;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 处理时间
     */
    private LocalDateTime processTime;
    
    /**
     * 最大重试次数
     */
    private Integer maxRetryCount;
    
    /**
     * 当前重试次数
     */
    private Integer currentRetryCount = 0;
    
    /**
     * 超时时间（秒）
     */
    private Integer timeoutSeconds;
    
    /**
     * 扩展属性
     */
    private Map<String, Object> attributes;
    
    /**
     * 路由信息
     */
    private String routingKey;
    
    /**
     * 渠道信息
     */
    private String channelCode;
    
    // Constructors
    public MessageContext() {
        this.createTime = LocalDateTime.now();
    }
    
    public MessageContext(String messageId, String templateId, MessageReceiver receiver) {
        this();
        this.messageId = messageId;
        this.templateId = templateId;
        this.receiver = receiver;
    }
    
    // Getters and Setters
    public String getMessageId() {
        return messageId;
    }
    
    public void setMessageId(String messageId) {
        this.messageId = messageId;
    }
    
    public String getRequestId() {
        return requestId;
    }
    
    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }
    
    public String getAppId() {
        return appId;
    }
    
    public void setAppId(String appId) {
        this.appId = appId;
    }
    
    public String getTemplateId() {
        return templateId;
    }
    
    public void setTemplateId(String templateId) {
        this.templateId = templateId;
    }
    
    public MessageReceiver getReceiver() {
        return receiver;
    }
    
    public void setReceiver(MessageReceiver receiver) {
        this.receiver = receiver;
    }
    
    public String getRenderedContent() {
        return renderedContent;
    }
    
    public void setRenderedContent(String renderedContent) {
        this.renderedContent = renderedContent;
    }
    
    public String getMessageType() {
        return messageType;
    }
    
    public void setMessageType(String messageType) {
        this.messageType = messageType;
    }
    
    public Integer getPriority() {
        return priority;
    }
    
    public void setPriority(Integer priority) {
        this.priority = priority;
    }
    
    public LocalDateTime getScheduleTime() {
        return scheduleTime;
    }
    
    public void setScheduleTime(LocalDateTime scheduleTime) {
        this.scheduleTime = scheduleTime;
    }
    
    public LocalDateTime getCreateTime() {
        return createTime;
    }
    
    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }
    
    public LocalDateTime getProcessTime() {
        return processTime;
    }
    
    public void setProcessTime(LocalDateTime processTime) {
        this.processTime = processTime;
    }
    
    public Integer getMaxRetryCount() {
        return maxRetryCount;
    }
    
    public void setMaxRetryCount(Integer maxRetryCount) {
        this.maxRetryCount = maxRetryCount;
    }
    
    public Integer getCurrentRetryCount() {
        return currentRetryCount;
    }
    
    public void setCurrentRetryCount(Integer currentRetryCount) {
        this.currentRetryCount = currentRetryCount;
    }
    
    public Integer getTimeoutSeconds() {
        return timeoutSeconds;
    }
    
    public void setTimeoutSeconds(Integer timeoutSeconds) {
        this.timeoutSeconds = timeoutSeconds;
    }
    
    public Map<String, Object> getAttributes() {
        return attributes;
    }
    
    public void setAttributes(Map<String, Object> attributes) {
        this.attributes = attributes;
    }
    
    public String getRoutingKey() {
        return routingKey;
    }
    
    public void setRoutingKey(String routingKey) {
        this.routingKey = routingKey;
    }
    
    public String getChannelCode() {
        return channelCode;
    }
    
    public void setChannelCode(String channelCode) {
        this.channelCode = channelCode;
    }
    
    /**
     * 增加重试次数
     */
    public void incrementRetryCount() {
        this.currentRetryCount++;
    }
    
    /**
     * 是否可以重试
     */
    public boolean canRetry() {
        return this.currentRetryCount < this.maxRetryCount;
    }
    
    /**
     * 是否为定时消息
     */
    public boolean isScheduled() {
        return this.scheduleTime != null && this.scheduleTime.isAfter(LocalDateTime.now());
    }
    
    /**
     * 添加属性
     */
    public void addAttribute(String key, Object value) {
        if (this.attributes == null) {
            this.attributes = new java.util.HashMap<>();
        }
        this.attributes.put(key, value);
    }
    
    /**
     * 获取属性
     */
    public Object getAttribute(String key) {
        return this.attributes != null ? this.attributes.get(key) : null;
    }
    
    @Override
    public String toString() {
        return "MessageContext{" +
                "messageId='" + messageId + '\'' +
                ", requestId='" + requestId + '\'' +
                ", appId='" + appId + '\'' +
                ", templateId='" + templateId + '\'' +
                ", receiver=" + receiver +
                ", renderedContent='" + renderedContent + '\'' +
                ", messageType='" + messageType + '\'' +
                ", priority=" + priority +
                ", scheduleTime=" + scheduleTime +
                ", createTime=" + createTime +
                ", processTime=" + processTime +
                ", maxRetryCount=" + maxRetryCount +
                ", currentRetryCount=" + currentRetryCount +
                ", timeoutSeconds=" + timeoutSeconds +
                ", attributes=" + attributes +
                ", routingKey='" + routingKey + '\'' +
                ", channelCode='" + channelCode + '\'' +
                '}';
    }
}