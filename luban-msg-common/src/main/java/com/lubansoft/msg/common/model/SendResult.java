package com.lubansoft.msg.common.model;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 消息发送结果模型
 */
public class SendResult {
    
    /**
     * 是否发送成功
     */
    private boolean success;
    
    /**
     * 消息ID
     */
    private String messageId;
    
    /**
     * 请求ID
     */
    private String requestId;
    
    /**
     * 接收者
     */
    private String receiver;
    
    /**
     * 发送渠道
     */
    private String channel;
    
    /**
     * 第三方消息ID（如短信平台返回的ID）
     */
    private String thirdPartyMessageId;
    
    /**
     * 发送时间
     */
    private LocalDateTime sendTime;
    
    /**
     * 错误码
     */
    private String errorCode;
    
    /**
     * 错误信息
     */
    private String errorMessage;
    
    /**
     * 发送耗时（毫秒）
     */
    private long executionTimeMs;
    
    /**
     * 重试次数
     */
    private int retryCount;
    
    /**
     * 扩展属性
     */
    private Map<String, Object> attributes;
    
    /**
     * 费用（分）
     */
    private Integer cost;
    
    /**
     * 渠道响应原始数据
     */
    private String rawResponse;
    
    // Constructors
    public SendResult() {
        this.sendTime = LocalDateTime.now();
    }
    
    public SendResult(boolean success, String messageId, String receiver, String channel) {
        this();
        this.success = success;
        this.messageId = messageId;
        this.receiver = receiver;
        this.channel = channel;
    }
    
    // 静态工厂方法
    public static SendResult success(String messageId) {
        SendResult result = new SendResult();
        result.success = true;
        result.messageId = messageId;
        return result;
    }
    
    public static SendResult success(String messageId, String receiver, String channel) {
        return new SendResult(true, messageId, receiver, channel);
    }
    
    public static SendResult failure(String errorCode, String errorMessage) {
        SendResult result = new SendResult();
        result.success = false;
        result.errorCode = errorCode;
        result.errorMessage = errorMessage;
        return result;
    }
    
    public static SendResult failure(String messageId, String receiver, String channel, String errorCode, String errorMessage) {
        SendResult result = new SendResult(false, messageId, receiver, channel);
        result.errorCode = errorCode;
        result.errorMessage = errorMessage;
        return result;
    }
    
    public static SendResult partialSuccess(String messageId, int successCount, int totalCount) {
        SendResult result = new SendResult();
        result.success = successCount > 0;
        result.messageId = messageId;
        result.addAttribute("successCount", successCount);
        result.addAttribute("totalCount", totalCount);
        result.addAttribute("failedCount", totalCount - successCount);
        return result;
    }
    
    // Getters and Setters
    public boolean isSuccess() {
        return success;
    }
    
    public void setSuccess(boolean success) {
        this.success = success;
    }
    
    public String getMessageId() {
        return messageId;
    }
    
    public void setMessageId(String messageId) {
        this.messageId = messageId;
    }
    
    public String getRequestId() {
        return requestId;
    }
    
    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }
    
    public String getReceiver() {
        return receiver;
    }
    
    public void setReceiver(String receiver) {
        this.receiver = receiver;
    }
    
    public String getChannel() {
        return channel;
    }
    
    public void setChannel(String channel) {
        this.channel = channel;
    }
    
    public String getThirdPartyMessageId() {
        return thirdPartyMessageId;
    }
    
    public void setThirdPartyMessageId(String thirdPartyMessageId) {
        this.thirdPartyMessageId = thirdPartyMessageId;
    }
    
    public LocalDateTime getSendTime() {
        return sendTime;
    }
    
    public void setSendTime(LocalDateTime sendTime) {
        this.sendTime = sendTime;
    }
    
    public String getErrorCode() {
        return errorCode;
    }
    
    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }
    
    public String getErrorMessage() {
        return errorMessage;
    }
    
    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }
    
    public long getExecutionTimeMs() {
        return executionTimeMs;
    }
    
    public void setExecutionTimeMs(long executionTimeMs) {
        this.executionTimeMs = executionTimeMs;
    }
    
    public int getRetryCount() {
        return retryCount;
    }
    
    public void setRetryCount(int retryCount) {
        this.retryCount = retryCount;
    }
    
    public Map<String, Object> getAttributes() {
        return attributes;
    }
    
    public void setAttributes(Map<String, Object> attributes) {
        this.attributes = attributes;
    }
    
    public Integer getCost() {
        return cost;
    }
    
    public void setCost(Integer cost) {
        this.cost = cost;
    }
    
    public String getRawResponse() {
        return rawResponse;
    }
    
    public void setRawResponse(String rawResponse) {
        this.rawResponse = rawResponse;
    }
    
    /**
     * 是否需要重试
     */
    public boolean needRetry() {
        return !success && isRetryableError();
    }
    
    /**
     * 是否为可重试的错误
     */
    public boolean isRetryableError() {
        if (errorCode == null) {
            return false;
        }
        // 网络错误、超时错误、服务器错误等可以重试
        return errorCode.startsWith("NETWORK_") || 
               errorCode.startsWith("TIMEOUT_") || 
               errorCode.startsWith("SERVER_") ||
               "RATE_LIMIT".equals(errorCode);
    }
    
    /**
     * 添加属性
     */
    public void addAttribute(String key, Object value) {
        if (this.attributes == null) {
            this.attributes = new java.util.HashMap<>();
        }
        this.attributes.put(key, value);
    }
    
    /**
     * 获取属性
     */
    public Object getAttribute(String key) {
        return this.attributes != null ? this.attributes.get(key) : null;
    }
    
    /**
     * 增加重试次数
     */
    public void incrementRetryCount() {
        this.retryCount++;
    }
    
    /**
     * 设置错误信息
     */
    public void setError(String errorCode, String errorMessage) {
        this.success = false;
        this.errorCode = errorCode;
        this.errorMessage = errorMessage;
    }
    
    /**
     * 获取简短的状态描述
     */
    public String getStatusDescription() {
        if (success) {
            return "发送成功";
        } else {
            return errorMessage != null ? errorMessage : "发送失败";
        }
    }
    
    @Override
    public String toString() {
        return "SendResult{" +
                "success=" + success +
                ", messageId='" + messageId + '\'' +
                ", requestId='" + requestId + '\'' +
                ", receiver='" + receiver + '\'' +
                ", channel='" + channel + '\'' +
                ", thirdPartyMessageId='" + thirdPartyMessageId + '\'' +
                ", sendTime=" + sendTime +
                ", errorCode='" + errorCode + '\'' +
                ", errorMessage='" + errorMessage + '\'' +
                ", executionTimeMs=" + executionTimeMs +
                ", retryCount=" + retryCount +
                ", attributes=" + attributes +
                ", cost=" + cost +
                ", rawResponse='" + rawResponse + '\'' +
                '}';
    }
}