package com.lubansoft.msg.common.model;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 路由匹配结果
 */
public class RoutingResult {
    
    /**
     * 是否匹配成功
     */
    private boolean matched;
    
    /**
     * 规则ID
     */
    private String ruleId;
    
    /**
     * 规则名称
     */
    private String ruleName;
    
    /**
     * 目标渠道列表
     */
    private List<String> targetChannels;
    
    /**
     * 优先级
     */
    private int priority;
    
    /**
     * 匹配时间
     */
    private LocalDateTime matchTime;
    
    /**
     * 匹配条件
     */
    private String condition;
    
    /**
     * 上下文信息
     */
    private Map<String, Object> context;
    
    /**
     * 执行时间（毫秒）
     */
    private long executionTimeMs;
    
    /**
     * 错误消息
     */
    private String errorMessage;
    
    // 构造函数
    public RoutingResult() {
        this.matched = false;
        this.targetChannels = new ArrayList<>();
        this.context = new HashMap<>();
        this.matchTime = LocalDateTime.now();
        this.executionTimeMs = 0;
    }
    
    public RoutingResult(boolean matched) {
        this();
        this.matched = matched;
    }
    
    // 静态工厂方法
    public static RoutingResult success(String ruleId, String ruleName, List<String> targetChannels, int priority) {
        RoutingResult result = new RoutingResult(true);
        result.setRuleId(ruleId);
        result.setRuleName(ruleName);
        result.setTargetChannels(targetChannels);
        result.setPriority(priority);
        return result;
    }
    
    public static RoutingResult failure(String reason) {
        RoutingResult result = new RoutingResult(false);
        result.addContext("failureReason", reason);
        return result;
    }
    
    public static RoutingResult noMatch() {
        return new RoutingResult(false);
    }
    
    // Getter and Setter methods
    public boolean isMatched() {
        return matched;
    }
    
    public void setMatched(boolean matched) {
        this.matched = matched;
    }
    
    public String getRuleId() {
        return ruleId;
    }
    
    public void setRuleId(String ruleId) {
        this.ruleId = ruleId;
    }
    
    public String getRuleName() {
        return ruleName;
    }
    
    public void setRuleName(String ruleName) {
        this.ruleName = ruleName;
    }
    
    public List<String> getTargetChannels() {
        return targetChannels;
    }
    
    public void setTargetChannels(List<String> targetChannels) {
        this.targetChannels = targetChannels != null ? targetChannels : new ArrayList<>();
    }
    
    public int getPriority() {
        return priority;
    }
    
    public void setPriority(int priority) {
        this.priority = priority;
    }
    
    public LocalDateTime getMatchTime() {
        return matchTime;
    }
    
    public void setMatchTime(LocalDateTime matchTime) {
        this.matchTime = matchTime;
    }
    
    public String getCondition() {
        return condition;
    }
    
    public void setCondition(String condition) {
        this.condition = condition;
    }
    
    public Map<String, Object> getContext() {
        return context;
    }
    
    public void setContext(Map<String, Object> context) {
        this.context = context != null ? context : new HashMap<>();
    }
    
    public long getExecutionTimeMs() {
        return executionTimeMs;
    }
    
    public void setExecutionTimeMs(long executionTimeMs) {
        this.executionTimeMs = executionTimeMs;
    }
    
    public String getErrorMessage() {
        return errorMessage;
    }
    
    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }
    
    /**
     * 添加上下文信息
     */
    public void addContext(String key, Object value) {
        if (this.context == null) {
            this.context = new HashMap<>();
        }
        this.context.put(key, value);
    }
    
    /**
     * 获取上下文信息
     */
    public Object getContext(String key) {
        return this.context != null ? this.context.get(key) : null;
    }
    
    /**
     * 添加目标渠道
     */
    public void addTargetChannel(String channelId) {
        if (channelId != null && !this.targetChannels.contains(channelId)) {
            this.targetChannels.add(channelId);
        }
    }
    
    /**
     * 是否有目标渠道
     */
    public boolean hasTargetChannels() {
        return targetChannels != null && !targetChannels.isEmpty();
    }
    
    /**
     * 获取第一个目标渠道
     */
    public String getFirstTargetChannel() {
        return hasTargetChannels() ? targetChannels.get(0) : null;
    }
    
    /**
     * 获取目标渠道数量
     */
    public int getTargetChannelCount() {
        return targetChannels != null ? targetChannels.size() : 0;
    }
    
    @Override
    public String toString() {
        return "RoutingResult{" +
                "matched=" + matched +
                ", ruleId='" + ruleId + '\'' +
                ", ruleName='" + ruleName + '\'' +
                ", targetChannels=" + targetChannels +
                ", priority=" + priority +
                ", matchTime=" + matchTime +
                ", condition='" + condition + '\'' +
                ", context=" + context +
                ", executionTimeMs=" + executionTimeMs +
                '}';
    }
}