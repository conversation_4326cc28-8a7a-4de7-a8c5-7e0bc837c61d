package com.lubansoft.msg.common.model;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 消息发送请求模型
 */
@Data
public class MessageRequest {

    @Schema(description = "消息编码")
    @NotBlank(message = "消息编码不能为空")
    private String templateId;

    @Schema(description = "全局模板参数")
    private Map<String, Object> globalParam;

    @Schema(description = "接受者用户ids")
    @NotEmpty(message = "接收者列表不能为空")
    private List<String> receivers;
    
    @Schema(description = "发送配置信息")
    private SendConfig sendConfig;

}