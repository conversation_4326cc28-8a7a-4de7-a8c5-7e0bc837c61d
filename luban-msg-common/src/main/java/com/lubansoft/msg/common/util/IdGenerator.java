package com.lubansoft.msg.common.util;

import org.springframework.stereotype.Component;

import java.security.SecureRandom;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicLong;

/**
 * ID生成器工具类
 */
@Component
public class IdGenerator {
    
    private static final AtomicLong SEQUENCE = new AtomicLong(0);
    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
    private static final String CHARACTERS = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
    private static final SecureRandom RANDOM = new SecureRandom();
    
    /**
     * 生成消息ID
     * 格式：MSG_yyyyMMddHHmmss_序号
     */
    public static String generateMessageId() {
        String timestamp = LocalDateTime.now().format(FORMATTER);
        long seq = SEQUENCE.incrementAndGet() % 10000;
        return String.format("MSG_%s_%04d", timestamp, seq);
    }
    
    /**
     * 生成请求ID
     * 格式：REQ_yyyyMMddHHmmss_序号
     */
    public static String generateRequestId() {
        String timestamp = LocalDateTime.now().format(FORMATTER);
        long seq = SEQUENCE.incrementAndGet() % 10000;
        return String.format("REQ_%s_%04d", timestamp, seq);
    }
    
    /**
     * 生成模板ID
     * 格式：TPL_类型_序号
     */
    public static String generateTemplateId(String type) {
        long seq = SEQUENCE.incrementAndGet() % 1000;
        return String.format("TPL_%s_%03d", type.toUpperCase(), seq);
    }
    
    /**
     * 生成规则ID
     * 格式：RULE_yyyyMMddHHmmss_序号
     */
    public static String generateRuleId() {
        String timestamp = LocalDateTime.now().format(FORMATTER);
        long seq = SEQUENCE.incrementAndGet() % 1000;
        return String.format("RULE_%s_%03d", timestamp, seq);
    }
    
    /**
     * 生成通用随机ID
     * 默认长度为16位
     */
    public static String generateId() {
        return generateId(16);
    }
    
    /**
     * 生成指定长度的随机ID
     * 
     * @param length ID长度
     * @return 随机ID字符串
     */
    public static String generateId(int length) {
        if (length <= 0) {
            throw new IllegalArgumentException("ID长度必须大于0");
        }
        
        StringBuilder sb = new StringBuilder(length);
        for (int i = 0; i < length; i++) {
            sb.append(CHARACTERS.charAt(RANDOM.nextInt(CHARACTERS.length())));
        }
        return sb.toString();
    }
    
    /**
     * 生成UUID（去除连字符）
     * 
     * @return UUID字符串
     */
    public static String generateUUID() {
        return UUID.randomUUID().toString().replace("-", "");
    }
}