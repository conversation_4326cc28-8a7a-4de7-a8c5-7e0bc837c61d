package com.lubansoft.msg.common.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.util.Map;

/**
 * 消息接收者模型
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class MessageReceiver {
    
    @Schema(description = "接收人用户id")
    @NotBlank(message = "接收人用户id不能为空")
    private String receiver;
    
    @Schema(description = "接受者模板参数")
    private Map<String, Object> params;
    
}