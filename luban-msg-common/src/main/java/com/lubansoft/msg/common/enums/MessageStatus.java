package com.lubansoft.msg.common.enums;

/**
 * 消息状态枚举
 */
public enum MessageStatus {
    
    /**
     * 待发送
     */
    PENDING("PENDING", "待发送"),
    
    /**
     * 发送中
     */
    SENDING("SENDING", "发送中"),
    
    /**
     * 处理中
     */
    PROCESSING("PROCESSING", "处理中"),
    
    /**
     * 发送成功
     */
    SENT("SENT", "发送成功"),
    
    /**
     * 发送失败
     */
    FAILED("FAILED", "发送失败"),
    
    /**
     * 被过滤
     */
    FILTERED("FILTERED", "被过滤"),
    
    /**
     * 已取消
     */
    CANCELLED("CANCELLED", "已取消");
    
    private final String code;
    private final String description;
    
    MessageStatus(String code, String description) {
        this.code = code;
        this.description = description;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getDescription() {
        return description;
    }
    
    public static MessageStatus fromCode(String code) {
        for (MessageStatus status : values()) {
            if (status.code.equals(code)) {
                return status;
            }
        }
        throw new IllegalArgumentException("Unknown message status code: " + code);
    }
}