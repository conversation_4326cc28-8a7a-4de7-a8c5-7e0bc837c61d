package com.lubansoft.msg.common.model;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;

import java.time.LocalDateTime;

@Tag(name = "发送配置", description = "发送消息的配置信息")
@Data
public class SendConfig {

    @Schema(description = "强制发送标志，若为true则忽略用户订阅配置，强制发送给所有接收者；若为false或不设置，则根据用户订阅配置过滤接收者")
    private Boolean forceSend = false;

    @Schema(description = "定时发送时间")
    private LocalDateTime scheduleTime;
}
