//package com.lubansoft.msg.config.rabbitmq;
//
//import com.alibaba.nacos.api.config.annotation.NacosValue;
//import com.alibaba.nacos.spring.context.annotation.config.NacosPropertySource;
//
//import com.lubansoft.base.core.helper.annotation.ValidationValues;
//import org.springframework.context.annotation.Configuration;
//
//
//@Configuration
//@NacosPropertySource(dataId = "application-rabbitmq.yml", autoRefreshed = true)
//public class RabbitMQConfig {
//
//    @NacosValue(value = "${spring.rabbitmq.host}", autoRefreshed = true)
//    private String host;
//
//    @NacosValue(value = "${spring.rabbitmq.port}", autoRefreshed = true)
//    private Integer port;
//
//    @NacosValue(value = "${spring.rabbitmq.username}", autoRefreshed = true)
//    private String username;
//
//    @NacosValue(value = "${spring.rabbitmq.password}", autoRefreshed = true)
//    private String password;
//
//    @NacosValue(value = "${spring.rabbitmq.virtual-host}", autoRefreshed = true)
//    private String virtualHost;
//
//    public String getHost() {
//        return host;
//    }
//
//    public void setHost(String host) {
//        this.host = host;
//    }
//
//    public Integer getPort() {
//        return port;
//    }
//
//    public void setPort(Integer port) {
//        this.port = port;
//    }
//
//    public String getUsername() {
//        return username;
//    }
//
//    public void setUsername(String username) {
//        this.username = username;
//    }
//
//    public String getPassword() {
//        return password;
//    }
//
//    public void setPassword(String password) {
//        this.password = password;
//    }
//
//    public String getVirtualHost() {
//        return virtualHost;
//    }
//
//    public void setVirtualHost(String virtualHost) {
//        this.virtualHost = virtualHost;
//    }
//
//}