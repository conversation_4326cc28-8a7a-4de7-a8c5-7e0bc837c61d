package com.lubansoft.msg.config.web;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.lubansoft.base.core.context.UserContext;
import com.lubansoft.base.core.util.LubanContextHolder;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

@Component(value = "DefaultLubanMetaObjectHandle")
public class LubanMetaObjectHandle implements MetaObjectHandler {

    @Override
    public void insertFill(MetaObject metaObject) {
        LocalDateTime now = LocalDateTime.now();
        this.strictInsertFill(metaObject, "createdAt", LocalDateTime.class, now);
        this.strictInsertFill(metaObject, "updatedAt", LocalDateTime.class, now);
        this.strictInsertFill(metaObject, "isDeleted", Integer.class, 0);
    }

    @Override
    public void updateFill(MetaObject metaObject) {
        LocalDateTime now = LocalDateTime.now();
        UserContext userContext = LubanContextHolder.getLubanContext().getUserContext();
        if(userContext != null) {
            this.setFieldValByName("updatedBy", userContext.getUserId(), metaObject);
        }
        this.setFieldValByName("updatedAt", now, metaObject);
    }
}
