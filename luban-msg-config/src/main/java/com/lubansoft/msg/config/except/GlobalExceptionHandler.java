package com.lubansoft.msg.config.except;

import com.lubansoft.base.core.helper.handle.LubanGlobalExceptionHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.ControllerAdvice;

/**
 * 全局异常处理
 */
@ControllerAdvice
public class GlobalExceptionHandler extends LubanGlobalExceptionHandler {

    private static final Logger logger = LoggerFactory.getLogger(GlobalExceptionHandler.class);

//    @ExceptionHandler(value = Exception.class)
//    @ResponseBody
//    public ResponseEntity<?> handle(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) {
//        String className = GlobalExceptionHandler.class.getName();
//        String methodName = "afterCompletion";
//        if (handler instanceof HandlerMethod) {
//            HandlerMethod m = (HandlerMethod) handler;
//            className = m.getBeanType().getName();
//            methodName = m.getMethod().getName();
//        }
//
//        logger.error(MessageFormat.format("{0}.{1} RESTFUL({2}) METHOD({3}) Error, Exception : {4}", className, methodName, request.getRequestURI(), request.getMethod(), ex.getMessage()));
//
//        try {
//            if (ex instanceof AuthenticationException) {
//                ResponseEntity<?> responseEntity = ResponseEntity.fail(((AuthenticationException) ex).getCode(), ex.getMessage());
//                ResponseEntity.fail(AuthenticationErrorCode.NOT_LOGIN.getCode(), "错我");
//            }
//        } catch (Exception e) {
//            logger.error("OverallExceptionResolver Handler Exception Error." + e);
//        }
//
//        return null;
//    }
}

