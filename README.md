# 鲁班-脚手架服务

## 技术选型

- 服务端：Spring Boot 3.1.0
- 数据库：PostgreSQL 13.12
- ORM框架：mybatis-plus 3.5.3.2
- 数据库连接池：druid 1.2.18
- 配置中心：Nacos 2.2.2
- 注册中心：Nacos 2.2.2
- 缓存：Redis 6.2.14
- 消息队列：RabbitMQ 3.5.3
- 权限认证：luban-base
- 日志管理：log4j
- API文档：knife4j-openapi3-jakarta 4.3.0

## 脚手架目录结构
~~~
服务
├─builder-base-api
├─builder-base-bootstrap    （程序启动入口，依赖builder-base-config、builder-base-model-starter）
├─builder-base-common       （公共方法区）
├─builder-base-config       （全局配置）
├─builder-base-model-starter（业务逻辑，依赖builder-base-common、builder-base-api）
│  ├─controller             （rest接口）
│  ├─service                （业务逻辑实现）
│  ├─model                  （数据模型）
│  ├─repository             （数据持久化）
│  ├─adapter                （事件处理）
│  │  ├─api                 （对外提供的rpc接口）
│  │  └─dep                 （调用外部的api接口）
│  └─util                   （工具类）
├─docker                    （容器化部署脚本）
└─sql                       （数据库脚本）
~~~

## 脚手架使用方式
http://doc.luban.fit/pages/viewpage.action?pageId=70465032

修改target目录中的pom.xml
添加仓库地址

```xml
<distributionManagement>
    <snapshotRepository>
        <id>luban-nexus-snapshots</id>
        <url>http://nexus.luban.fit/repository/snapshots/</url>
    </snapshotRepository>
    <repository>
        <id>luban-nexus-releases</id>
        <url>http://nexus.luban.fit/repository/releases/</url>
    </repository>
</distributionManagement>
```

## 构建镜像并发布到仓库

mvn clean install


## 此项目使用方式

https://cloud.tencent.com/developer/article/1875305