#!/bin/bash
#
# builder springboot 项目的启动文件,一般不需要修改, 唯一可能需要修改的可能是jdk的路径
# zhouerqin 20230920

set -e
cd $(dirname $0)

module_name=$(basename $(pwd))
logfile_name="${module_name}.log"

jarfile_list="$(find $(realpath .) -maxdepth 1 -iname '*.jar')"
jarfile_count=$(find $(realpath .) -maxdepth 1 -iname '*.jar'|wc -l)

if [[ $jarfile_count -eq 0 ]]; then
  echo "[ERROR] 没有找到JAR包" >&2
  echo "[ERROR] $module_name 退出" >&2
  exit 1
elif [[ $jarfile_count -eq 1 ]]; then
  jarfile=$(echo "$jarfile_list"|head -n1)
else
  echo "[ERROR] 发现多个JAR包,进程退出" >&2
  exit 1
fi

function init_config(){
  echo "初始化${module_name}配置文件"
  mkdir -p config
  # source /luban/ops/load_app_env.sh
  if ! dockerize  -no-overwrite -template tmpl:config; then
    echo "[ERROR] 初始化配置文件失败!" >&2
    exit 1
  fi
}

if [[ -d tmpl && ! -d config ]]; then
  # 在子shell中执行,避免加载的env污染全局变量
  (init_config)
fi

# 设置jdk路径
if [[ -f "setenv.sh" ]]; then
  source setenv.sh
fi

echo "start $module_name "
java -version
java \
  -Xms256m -Xmx512m \
  -Dfile.encoding=utf-8 \
  -Duser.timezone=Asia/Shanghai \
  -jar "$jarfile" 2>&1

