FROM harbor.luban.fit:8384/builder-d/jdk-17
RUN mkdir -p /luban && \
    cd /luban && \
    wget http://download.zhouerqin.com/public/x86_64/flyway-commandline-9.22.2-linux-x64.tar.gz && \
    tar -zxf flyway-commandline-9.22.2-linux-x64.tar.gz && \
    mv flyway-9.22.2 flyway && \
    rm ./flyway-commandline-9.22.2-linux-x64.tar.gz
ARG REVISION
ARG VERSION
ARG TIME
ENV BUILD_REVISION=${REVISION}
ENV BUILD_VERSION=${VERSION}
ENV BUILD_TIME=${TIME}
ENV PATH="$PATH:/luban/flyway"
COPY pkg/* /luban/app/
COPY rootfs /
RUN chmod uog+x /luban/app/start.sh
CMD ["/luban/app/start.sh"]