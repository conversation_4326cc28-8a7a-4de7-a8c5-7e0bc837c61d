# 消息中心系统设计文档

## 1. 系统概述

消息中心系统是一个集中管理和分发各类消息的平台，支持多渠道消息发送、条件路由、批量发送和定时发送等功能。系统旨在提供统一的消息处理接口，简化应用系统的消息发送流程，提高消息送达率和系统可靠性。


系统计划支持功能：
*   提供统一发信功能
*   消息渠道注册、健康检查
*   支持文本消息类型定义，提供变量占位符功能，支持动态内容替换
*   支持消息模板版本管理，提供模板预览和测试功能
*   消息记录查询
*   配额控制
*   动态路由配置匹配消息渠道

TODO LIST：
*   用户接收规则设置
*   定时发送
*   渠道状态监控
*   智能降级
*   支持富文本，多媒体消息类型定义
*   模板审批、版本控制

## 2. 系统架构
### 2.1 核心流程架构图

```mermaid
graph TD
    subgraph business[业务层]
        BS[业务服务]
    end

    subgraph gateway[消息中心]
        subgraph api["接口层"]
            GW[发送消息]
        end

        subgraph valid[验证拦截层]
            VI1[参数验证拦截器]
            VI2[权限验证拦截器]
        end

        subgraph match[路由匹配层]
            RE[路由匹配]
            RM[渠道计算]
        end
    
        subgraph provide[发信器]
            QS[队列选择器]
        end
    end

        subgraph queue[消息队列层]
            MQ1[普通队列]
            MQ2[高优先级队列]
            MQ3[定时队列]
            MQ4[其他队列]
        end

    subgraph consumer[消费层]
        sender[消息服务调用]
    end

    subgraph msg[消息服务]
        SMS[短信服务商]
        EMAIL[邮件服务器]
        PUSH[推送服务]
        OTHER[其他服务商]
    end

    %% 核心流程连接
    business --> gateway
    api --> valid
    VI1 --> VI2
    valid --> match
    RE --> RM
    match --> provide
    provide --> MQ1
    provide --> MQ2
    provide --> MQ3
    provide --> MQ4

    queue --> consumer

    sender --> SMS
    sender --> EMAIL
    sender --> PUSH
    sender --> OTHER

    %% 样式定义
    style BS fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    style GW fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    style VI1 fill:#fff3e0,stroke:#e65100,stroke-width:1px
    style VI2 fill:#fff3e0,stroke:#e65100,stroke-width:1px
    style RE fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px
    style RM fill:#e8f5e8,stroke:#2e7d32,stroke-width:1px
    style QS fill:#e8f5e8,stroke:#2e7d32,stroke-width:1px
    style MQ1 fill:#fff8e1,stroke:#f57f17,stroke-width:1px
    style MQ2 fill:#fff8e1,stroke:#f57f17,stroke-width:1px
    style MQ3 fill:#fff8e1,stroke:#f57f17,stroke-width:1px
    style MQ4 fill:#fff8e1,stroke:#f57f17,stroke-width:1px
```

### 2.2 核心组件详细设计

#### 2.2.1 消息网关接入层

*   **功能职责**：作为系统的统一入口，接收来自业务服务的消息请求
*   **核心能力**：
   *   Rest Api：
      *   发送消息
      *   查询消息状态
      *   取消定时消息
      *   消息模板管理
      *   消息路由规则
      *   消息发送记录

#### 2.2.2 验证拦截层

**格式验证拦截器**

*   检查必填字段和数据类型
*   验证字段长度和格式规范
*   支持自定义验证规则配置

**权限验证拦截器**

*   验证调用方身份和权限
*   检查x-auth appId API访问权限和配额
*   支持动态权限策略更新

#### 2.2.3 路由匹配层

*   支持复杂条件表达式匹配路由
*   实现路由策略的动态加载

#### 2.2.4 队列选择层

*   根据消息属性智能选择合适的队列类型
*   支持延时消息队列
*   支持高优先级消息队列
*   支持特殊队列处理

#### 2.2.5 消息消费层

*   监听队列处理

#### 2.2.6 渠道管理层

*   消息渠道管理(注册、健康检查)
*   统一消息渠道接口定义，支持任意渠道扩展

## 3. 业务流程

### 3.1 核心消息处理流程

```mermaid
sequenceDiagram
    participant BS as 业务服务
    participant GW as 消息网关接入
    participant VI as 验证拦截层
    participant RE as 路由引擎
    participant QS as 队列选择器
    participant MQ as 消息队列
    participant CC as 渠道消费者
    participant ES as 外部服务

    Note over BS,ES: 核心消息处理流程

    BS->>GW: 1. 发送消息请求
    GW->>GW: 2. 请求预处理
    GW->>VI: 3. 进入验证拦截链

    Note over VI: 验证拦截链处理
    VI->>VI: 3.1 格式验证
    VI->>VI: 3.2 权限验证

    alt 验证通过
        VI->>RE: 4. 传递到路由引擎
        RE->>RE: 5. 解析路由规则
        RE->>RE: 6. 匹配目标渠道
        RE->>QS: 7. 传递到队列选择器
        QS->>QS: 8. 选择合适的队列类型
        QS->>MQ: 9. 分发到对应队列
        RE->>BS: 10. 返回消息投递成功

        Note over MQ: 消息队列处理
        MQ->>CC: 11. 通知消费者
        MQ-->>GW: 死信队列(失败消息)
        GW->>GW: 失败记录入库

        Note over CC: 渠道消费处理
        CC->>CC: 12. 匹配外部供应商
        CC->>ES: 13. 调用外部服务
        ES->>CC: 14. 返回调用结果
        CC->>GW: 15. 确认消息已消费
        GW->>CC: 16. 查询消息是否抵达
    else 验证失败
        VI->>GW: 验证失败响应
        GW->>BS: 返回错误信息
    end
```

### 3.2 验证拦截链详细流程

```mermaid
graph TD
    A[消息请求] --> B{格式验证}
    B -->|通过| C{权限验证}
    B -->|失败| E[返回格式错误]
    C -->|失败| F[返回权限错误]
 
    style A fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    style B fill:#fff3e0,stroke:#e65100,stroke-width:2px
    style C fill:#fff3e0,stroke:#e65100,stroke-width:2px
```

### 3.3 路由匹配决策流程

```mermaid
graph TD
    A[消息进入路由层] --> B[加载路由规则]
    B --> C[解析消息属性]
    C --> D[执行条件匹配]
    D --> E{匹配到路由规则?}
    E -->|是| F[获取目标渠道列表]
    E -->|否| G[返回路由失败错误]
    F -->|是| J[分发到队列选择器]
    J --> K[选择合适的队列类型]
    K --> L[分发到目标队列]
    L --> M[记录路由日志]

    style A fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px
    style F fill:#fff8e1,stroke:#f57f17,stroke-width:2px
    style J fill:#fff8e1,stroke:#f57f17,stroke-width:2px
    style K fill:#fff8e1,stroke:#f57f17,stroke-width:2px
    style L fill:#fff8e1,stroke:#f57f17,stroke-width:2px
```

## 4. 关键功能实现

### 4.1 验证拦截层实现

#### 4.1.1 验证逻辑

略

#### 4.1.2 验证拦截器实现

**格式验证拦截器**

*   JSON Schema验证消息体结构
*   正则表达式验证字段格式
*   数据类型和长度校验
*   自定义验证规则扩展

**权限验证拦截器**

*   JWT Token验证
*   API Key验证

### 4.2 路由匹配层实现

#### 4.2.1 路由引擎设计

略

#### 4.2.2 路由策略实现

**条件路由**

*   基于消息内容的路由
*   基于用户属性的路由
*   基于时间条件的路由
*   基于地理位置的路由

### 4.3 队列选择层实现

#### 4.3.1 队列类型选择策略

**延时队列**

*   用于定时发送消息
*   支持任意时间点发送

**高优先级队列**

*   用于紧急消息发送
*   优先级高于普通队列

**死信队列**

*   处理发送失败的消息
*   支持消息重试机制

### 4.4 消息队列层

\*主要使用rabbitmq完成

#### 4.4.1 队列分发策略

**分区策略**

*   按路由键分发

**持久化策略**

*   消息持久化存储
*   消息状态跟踪
*   消息TTL管理

**可靠性保障**

*   消息确认机制
*   失败重试策略
*   死信队列处理

### 4.5 渠道消费层实现

#### 4.5.1 统一渠道适配器

**渠道配置**

*   通过ChannelConfigService获取渠道配置
*   从ChannelConfig的configData中获取接口字段（msgApi/healthApi）
*   构造完整的请求URL（baseUrl + 接口字段）

**渠道处理**

*   采用统一处理流程
*   保持类职责单一性
*   提高代码可维护性

**渠道健康检查**

*   首先检查渠道是否启用
*   通过ChannelConfigService获取渠道配置
*   从ChannelConfig的configData中获取healthApi字段
*   构造完整的健康检查URL（baseUrl + healthApi）
*   发送HTTP GET请求进行健康检查
*   根据请求结果判断渠道是否可用

## 5. 接口设计

### 5.1 核心API接口

#### 5.1.1 统一消息发送接口

```json
POST /messages/send
Content-Type: application/json
Authorization: JWT{token}

{
  "templateId": "template_001",     // 消息模板
  "receivers": [                    // 接收者参数
    {
      "receiver": "13800138000",    // 接收对象
      "params": {                   // 模板参数
        "name": "张三",
        "code": "9568"
      }
    },
    {
      "receiver": "13800138001",    // 接收对象
      "params": {                   // 模板参数
        "name": "李四",
        "code": "6672"
      }
    }
  ],
  "priority": 5,                    // 优先级 1-10 可选
  "scheduleTime": "2024-07-04T10:00:00Z",  // 定时发送时间 可选
  "globalParams": {                 // 全局模板参数
    "company": "鲁班科技"
  }
}
```

**响应示例：**

```json
{
  "status": "SUCCESS",
  "messageId": "msg_20240704_001",
  "requestId": "req_20240704_001",
  "deliveryTime": "2024-07-04T10:01:00Z",
  "timestamp": "2024-07-04T10:00:00Z"
}
```

## 6. 系统扩展性

### 6.1 渠道扩展

系统支持通过插件机制扩展新的消息渠道：

*   提供标准的渠道接口定义
*   通过统一渠道注册管理，无需修改代码即可支持新渠道

### 6.2 条件规则扩展

系统支持扩展条件规则的判断逻辑：

*   支持自定义条件表达式
*   提供规则测试和验证工具

## 7. 总结

本消息网关系统设计文档基于"业务服务 → 消息网关 → 验证拦截 → 路由匹配 → 队列选择 → 消息队列 → 渠道消费者"的核心流程，提供了完整的技术架构和实现方案。

### 7.1 核心特性

*   **高可靠性**：多级验证、消息持久化、消息确认机制
*   **高性能**：异步处理、批量操作
*   **高扩展性**：微服务架构、水平扩展
*   **易维护性**：详细日志、统一渠道适配器

### 7.2 技术亮点

*   基于责任链模式的验证拦截层
*   智能化的路由匹配引擎
*   灵活的队列选择机制
*   统一的渠道适配器架构
*   完善的消息状态跟踪

### 7.3 应用价值

*   统一消息发送入口，简化业务系统集成
*   提供多渠道消息发送能力，提升用户体验
*   支持大规模消息处理，满足企业级需求
*   完善的监控和运维体系，保障系统稳定运行