<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.lubansoft.msg</groupId>
    <artifactId>luban-msg</artifactId>
    <version>1.0-SNAPSHOT</version>
    <packaging>pom</packaging>
    <properties>
        <encoding>UTF-8</encoding>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <java.version>17</java.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <luban-base.version>1.3.11</luban-base.version>
    </properties>
    <scm>
        <connection>scm:git:http://git.luban.fit/jishuyunying/infra/devTools/java-scaffold.git</connection>
        <developerConnection>scm:git:ssh://git@_git.luban.fit:2222/jishuyunying/infra/devTools/java-scaffold.git
        </developerConnection>
        <tag>HEAD</tag>
        <url>https://git.luban.fit/jishuyunying/infra/devTools/java-scaffold</url>
    </scm>
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.lubansoft.base</groupId>
                <artifactId>luban-base-bom</artifactId>
                <version>${luban-base.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.lubansoft.base</groupId>
                        <artifactId>luban-logger</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>cn.dev33</groupId>
                        <artifactId>sa-token-core</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>cn.dev33</groupId>
                        <artifactId>sa-token-spring-boot3-starter</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>cn.dev33</groupId>
                        <artifactId>sa-token-redis-jackson</artifactId>
                    </exclusion>
                </exclusions>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.lubansoft.msg</groupId>
                <artifactId>luban-msg-config</artifactId>
                <version>1.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.lubansoft.msg</groupId>
                <artifactId>luban-msg-api</artifactId>
                <version>1.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.lubansoft.msg</groupId>
                <artifactId>luban-msg-common</artifactId>
                <version>1.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.lubansoft.msg</groupId>
                <artifactId>luban-msg-model-starter</artifactId>
                <version>1.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.github.java-json-tools</groupId>
                <artifactId>json-schema-validator</artifactId>
                <version>2.2.14</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
    <distributionManagement>
        <repository>
            <id>luban-nexus-releases</id>
            <url>http://nexus.luban.fit/repository/releases/</url>
        </repository>
        <snapshotRepository>
            <id>luban-nexus-snapshots</id>
            <url>http://nexus.luban.fit/repository/snapshots/</url>
        </snapshotRepository>
    </distributionManagement>
    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-archetype-plugin</artifactId>
                <version>3.2.0</version>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <version>3.3.0</version>
                <executions>
                    <execution>
                        <id>attach-sources</id>
                        <phase>package</phase>
                        <goals>
                            <goal>jar-no-fork</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>build-helper-maven-plugin</artifactId>
                    <version>3.5.0</version>
                    <executions>
                        <execution>
                            <id>timestamp-property</id>
                            <phase>validate</phase>
                            <goals>
                                <goal>timestamp-property</goal>
                            </goals>
                            <configuration>
                                <name>build.timestamp</name>
                                <pattern>yyyy-MM-dd HH:mm:ss z</pattern>
                                <timeSource>build</timeSource>
                                <locale>zh,CN</locale>
                            </configuration>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>buildnumber-maven-plugin</artifactId>
                    <version>3.1.0</version>
                    <executions>
                        <execution>
                            <id>create</id>
                            <phase>validate</phase>
                            <goals>
                                <goal>create</goal>
                            </goals>
                        </execution>
                    </executions>
                    <configuration>
                        <doCheck>false</doCheck>
                        <doUpdate>false</doUpdate>
                        <shortRevisionLength>8</shortRevisionLength>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-jar-plugin</artifactId>
                    <version>3.0.2</version>
                    <configuration>
                        <archive>
                            <manifest>
                                <addDefaultImplementationEntries>true</addDefaultImplementationEntries>
                            </manifest>
                            <manifestEntries>
                                <Implementation-Build>${buildNumber}</Implementation-Build>
                            </manifestEntries>
                        </archive>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <version>3.1.2</version>
                    <executions>
                        <execution>
                            <goals>
                                <goal>repackage</goal>
                                <goal>build-info</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>io.fabric8</groupId>
                    <artifactId>docker-maven-plugin</artifactId>
                    <version>0.44.0</version>
                    <configuration>
                        <verbose>all</verbose>
                        <logStdout>true</logStdout>
                        <images>
                            <image>
                                <!-- 仓库地址根据自己部门的调整 -->
                                <name>harbor.luban.fit:8384/dev/${project.build.finalName}:latest</name>
                                <build>
                                    <contextDir>${basedir}/../docker</contextDir>
                                    <dockerFile>Dockerfile</dockerFile>
                                    <tags>
                                        <tag>%v</tag>
                                        <tag>%T</tag>
                                        <tag>${buildNumber}</tag>
                                    </tags>
                                    <cleanup>try</cleanup>
                                    <noCache/>
                                </build>
                            </image>
                        </images>
                        <buildArgs>
                            <VERSION>${project.version}</VERSION>
                            <REVISION>${buildNumber}</REVISION>
                            <TIME>${build.timestamp}</TIME>
                        </buildArgs>
                    </configuration>
                    <executions>
                        <execution>
                            <phase>install</phase>
                            <goals>
                                <goal>build</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>
    <modules>
        <module>luban-msg-config</module>
        <module>luban-msg-model-starter</module>
        <module>luban-msg-upgrade</module>
        <module>luban-msg-bootstrap</module>
        <module>luban-msg-common</module>
    </modules>
</project>
