package com.lubansoft.msg.model.service.processor;

import com.lubansoft.msg.model.model.MessageRequestDTO;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 消息上下文
 */
public class MessageContext {
    
    /**
     * 消息请求
     */
    private MessageRequestDTO messageRequestDTO;
    
    /**
     * 应用ID
     */
    private String appId;
    
    /**
     * 请求ID
     */
    private String requestId;
    
    /**
     * 上下文属性
     */
    private Map<String, Object> attributes;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 构造函数
     */
    public MessageContext() {
        this.attributes = new HashMap<>();
        this.createTime = LocalDateTime.now();
    }
    
    public MessageContext(MessageRequestDTO messageRequestDTO) {
        this();
        this.messageRequestDTO = messageRequestDTO;
    }
    
    public MessageContext(MessageRequestDTO messageRequestDTO, String appId, String requestId) {
        this(messageRequestDTO);
        this.appId = appId;
        this.requestId = requestId;
    }
    
    // Getter and Setter methods
    public MessageRequestDTO getMessageRequest() {
        return messageRequestDTO;
    }
    
    public void setMessageRequest(MessageRequestDTO messageRequestDTO) {
        this.messageRequestDTO = messageRequestDTO;
    }
    
    public String getAppId() {
        return appId;
    }
    
    public void setAppId(String appId) {
        this.appId = appId;
    }
    
    public String getRequestId() {
        return requestId;
    }
    
    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }
    
    public Map<String, Object> getAttributes() {
        return attributes;
    }
    
    public void setAttributes(Map<String, Object> attributes) {
        this.attributes = attributes;
    }
    
    public LocalDateTime getCreateTime() {
        return createTime;
    }
    
    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }
    
    /**
     * 设置属性
     */
    public void setAttribute(String key, Object value) {
        this.attributes.put(key, value);
    }
    
    /**
     * 获取属性
     */
    public Object getAttribute(String key) {
        return this.attributes.get(key);
    }
    
    /**
     * 获取属性（带类型转换）
     */
    @SuppressWarnings("unchecked")
    public <T> T getAttribute(String key, Class<T> type) {
        Object value = this.attributes.get(key);
        if (value != null && type.isInstance(value)) {
            return (T) value;
        }
        return null;
    }
    
    /**
     * 是否包含属性
     */
    public boolean hasAttribute(String key) {
        return this.attributes.containsKey(key);
    }
    
    /**
     * 移除属性
     */
    public Object removeAttribute(String key) {
        return this.attributes.remove(key);
    }
    
    @Override
    public String toString() {
        return "MessageContext{" +
                "messageRequest=" + messageRequestDTO +
                ", appId='" + appId + '\'' +
                ", requestId='" + requestId + '\'' +
                ", attributes=" + attributes +
                ", createTime=" + createTime +
                '}';
    }
}