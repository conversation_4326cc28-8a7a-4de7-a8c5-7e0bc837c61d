package com.lubansoft.msg.model.repository.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.lubansoft.msg.common.util.JsonUtils;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import org.apache.ibatis.type.JdbcType;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 渠道配置实体
 */
@Data
@TableName(value = "channel_config", autoResultMap = true)
public class ChannelConfig {
    
    /**
     * 渠道ID
     */
    @TableId(value = "channel_id", type = IdType.ASSIGN_ID)
    private String channelId;
    
    /**
     * 渠道名称
     */
    @NotBlank(message = "渠道名称不能为空")
    @Size(max = 255, message = "渠道名称长度不能超过255个字符")
    @TableField("channel_name")
    private String channelName;
    
    /**
     * 渠道类型
     */
    @NotNull(message = "渠道类型不能为空")
    @TableField("channel_type")
    private String channelType;
    
    /**
     * 配置数据（JSON格式）
     */
    @TableField(value = "config_data", typeHandler = JacksonTypeHandler.class, jdbcType = JdbcType.JAVA_OBJECT)
    private ChannelConfigData configData;


    /**
     * 是否启用
     */
    @TableField("enabled")
    private Boolean enabled = true;
    
    /**
     * 创建时间
     */
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;
    
}