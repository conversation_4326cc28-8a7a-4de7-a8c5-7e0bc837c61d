package com.lubansoft.msg.model.service.processor;

import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 请求ID生成器
 */
@Component
public class RequestIdGenerator {
    
    private static final AtomicLong SEQUENCE = new AtomicLong(0);
    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
    
    /**
     * 生成请求ID
     * 格式：REQ_yyyyMMddHHmmss_序号
     * 
     * @return 请求ID
     */
    public String generateRequestId() {
        String timestamp = LocalDateTime.now().format(FORMATTER);
        long seq = SEQUENCE.incrementAndGet() % 10000;
        return String.format("REQ_%s_%04d", timestamp, seq);
    }
    
    /**
     * 生成批次ID
     * 格式：BATCH_yyyyMMddHHmmss_序号
     * 
     * @return 批次ID
     */
    public String generateBatchId() {
        String timestamp = LocalDateTime.now().format(FORMATTER);
        long seq = SEQUENCE.incrementAndGet() % 1000;
        return String.format("BATCH_%s_%03d", timestamp, seq);
    }
    
    /**
     * 从请求ID中提取时间戳
     * 
     * @param requestId 请求ID
     * @return 时间戳，如果解析失败返回null
     */
    public LocalDateTime extractTimestamp(String requestId) {
        try {
            if (requestId != null && requestId.startsWith("REQ_")) {
                String timestampStr = requestId.substring(4, 18); // 提取时间戳部分
                return LocalDateTime.parse(timestampStr, FORMATTER);
            }
        } catch (Exception e) {
            // 忽略解析错误
        }
        return null;
    }
    
    /**
     * 验证请求ID格式
     * 
     * @param requestId 请求ID
     * @return 是否为有效格式
     */
    public boolean isValidRequestId(String requestId) {
        if (requestId == null || requestId.length() != 23) {
            return false;
        }
        
        if (!requestId.startsWith("REQ_")) {
            return false;
        }
        
        try {
            // 验证时间戳部分
            String timestampStr = requestId.substring(4, 18);
            LocalDateTime.parse(timestampStr, FORMATTER);
            
            // 验证序号部分
            String seqStr = requestId.substring(19);
            Integer.parseInt(seqStr);
            
            return true;
        } catch (Exception e) {
            return false;
        }
    }
}