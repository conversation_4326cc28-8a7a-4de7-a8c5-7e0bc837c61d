package com.lubansoft.msg.model.controller.system;

import com.lubansoft.base.common.model.ResponseEntity;
import com.lubansoft.msg.common.model.MessageRequest;
import com.lubansoft.msg.model.model.MessageRequestDTO;
import com.lubansoft.msg.common.model.MessageResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import com.lubansoft.msg.model.service.processor.MessageRequestProcessor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/message")
@Tag(name = "消息网关", description = "消息发送接口")
public class MessageGatewayController {
    
    @Autowired
    private MessageRequestProcessor messageRequestProcessor;
    
    @Operation(summary = "发送消息(相同内容)", description = "发送消息到指定接收者")
    @PostMapping("/send-same")
    public MessageResponse sendMessage(@Validated @RequestBody MessageRequest messageRequest) {
        String appId = "dev_test";
        MessageRequestDTO messageRequestDTO = new MessageRequestDTO(appId, null, null, messageRequest.getTemplateId(),
                messageRequest.getGlobalParam(), messageRequest.getReceivers(), 1, messageRequest.getSendConfig());
        return messageRequestProcessor.processMessage(messageRequestDTO);
    }
    
    @Operation(summary = "消息回调", description = "消息回调")
    @GetMapping("/callback")
    public ResponseEntity<?> callback(@RequestParam String requestId, @RequestParam String receiverId, @RequestParam Boolean success, @RequestParam String msg, @RequestParam String callBackToken) {
        return ResponseEntity.success();
    }
}