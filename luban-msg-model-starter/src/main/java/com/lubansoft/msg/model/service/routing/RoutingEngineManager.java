package com.lubansoft.msg.model.service.routing;

import com.lubansoft.msg.model.model.MessageRequestDTO;
import com.lubansoft.msg.common.model.RoutingResult;
import com.lubansoft.msg.model.repository.entity.RoutingRule;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * 路由引擎管理服务
 */
@Service
public class RoutingEngineManager {
    
    private static final Logger logger = LoggerFactory.getLogger(RoutingEngineManager.class);
    
    @Autowired
    private RoutingEngine routingEngine;
    
    // 异步路由线程池
    private final ExecutorService asyncExecutor = Executors.newFixedThreadPool(10, 
        r -> {
            Thread t = new Thread(r, "routing-engine-async");
            t.setDaemon(true);
            return t;
        });
    
    /**
     * 同步路由消息
     * 
     * @param request 消息请求
     * @param appId 应用ID
     * @param requestId 请求ID
     * @return 路由结果
     */
    public RoutingResult route(MessageRequestDTO request, String appId, String requestId) {
        RoutingContext context = new RoutingContext();
        context.setAttribute("appId", appId);
        context.setAttribute("requestId", requestId);
        return routingEngine.route(request, context);
    }
    
    /**
     * 同步路由消息（带上下文）
     * 
     * @param request 消息请求
     * @param context 路由上下文
     * @return 路由结果
     */
    public RoutingResult route(MessageRequestDTO request, RoutingContext context) {
        return routingEngine.route(request, context);
    }
    
    /**
     * 批量路由消息
     * 
     * @param requests 消息请求列表
     * @param appId 应用ID
     * @param requestId 请求ID
     * @return 路由结果列表
     */
    public List<RoutingResult> routeBatch(List<MessageRequestDTO> requests,
                                                       String appId, String requestId) {
        RoutingContext context = new RoutingContext();
        context.setAttribute("appId", appId);
        context.setAttribute("requestId", requestId);
        return routingEngine.routeBatch(requests, context);
    }
    
    /**
     * 异步批量路由消息
     * 
     * @param requests 消息请求列表
     * @param appId 应用ID
     * @param requestId 请求ID
     * @return 路由结果列表的Future
     */
    public CompletableFuture<List<RoutingResult>> routeBatchAsync(List<MessageRequestDTO> requests,
                                                                               String appId, String requestId) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                return routeBatch(requests, appId, requestId);
            } catch (Exception e) {
                logger.error("异步批量路由执行异常: requestCount={}, appId={}", requests.size(), appId, e);
                throw new RuntimeException("异步批量路由执行异常", e);
            }
        }, asyncExecutor);
    }
    
    /**
     * 重新加载路由规则
     */
    public void reloadRules() {
        logger.info("重新加载路由规则");
        routingEngine.reloadRules();
    }
    
    /**
     * 获取启用的路由规则
     * 
     * @return 路由规则列表
     */
    public List<RoutingRule> getEnabledRules() {
        return routingEngine.getEnabledRules();
    }
    
    /**
     * 获取路由统计信息
     * 
     * @return 统计信息
     */
    public Map<String, Object> getRoutingStatistics() {
        if (routingEngine instanceof SpelRoutingEngine) {
            SpelRoutingEngine spelEngine = (SpelRoutingEngine) routingEngine;
            Map<String, Object> stats = spelEngine.getEngineStatus();
            stats.put("ruleStatistics", spelEngine.getRoutingStatistics());
            return stats;
        }
        return Map.of("message", "统计信息不可用");
    }
    
    /**
     * 重置路由统计信息
     */
    public void resetStatistics() {
        if (routingEngine instanceof SpelRoutingEngine) {
            ((SpelRoutingEngine) routingEngine).resetStatistics();
            logger.info("路由统计信息已重置");
        }
    }
    
    /**
     * 验证路由规则
     * 
     * @param rule 路由规则
     * @param testRequest 测试请求
     * @return 验证结果
     */
    public ValidationResult validateRule(RoutingRule rule, MessageRequestDTO testRequest) {
        try {
            RoutingContext context = new RoutingContext();
            context.setAttribute("appId", "test");
            context.setAttribute("requestId", "test-req");
            
            // 创建临时规则列表进行测试
            boolean matched = false;
            String errorMessage = null;
            
            try {
                // 这里可以添加规则验证逻辑
                // 比如检查SpEL表达式语法、目标渠道有效性等
                
                if (rule.getCondition() == null || rule.getCondition().trim().isEmpty()) {
                    errorMessage = "路由条件不能为空";
                } else if (rule.getTargetChannel() == null || rule.getTargetChannel().trim().isEmpty()) {
                    errorMessage = "目标渠道不能为空";
                } else {
                    // 可以在这里添加更多验证逻辑
                    matched = true;
                }
                
            } catch (Exception e) {
                errorMessage = "规则验证异常: " + e.getMessage();
            }
            
            return new ValidationResult(matched, errorMessage);
            
        } catch (Exception e) {
            logger.error("验证路由规则异常: ruleId={}", rule.getRuleId(), e);
            return new ValidationResult(false, "验证异常: " + e.getMessage());
        }
    }
    
    /**
     * 规则验证结果
     */
    public static class ValidationResult {
        private final boolean valid;
        private final String errorMessage;
        
        public ValidationResult(boolean valid, String errorMessage) {
            this.valid = valid;
            this.errorMessage = errorMessage;
        }
        
        public boolean isValid() {
            return valid;
        }
        
        public String getErrorMessage() {
            return errorMessage;
        }
    }
}