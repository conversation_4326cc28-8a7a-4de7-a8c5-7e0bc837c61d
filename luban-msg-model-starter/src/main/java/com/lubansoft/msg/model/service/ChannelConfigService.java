package com.lubansoft.msg.model.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lubansoft.msg.model.repository.entity.ChannelConfig;
import com.lubansoft.msg.model.repository.mapper.ChannelConfigMapper;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 渠道配置服务类
 */
@Service
public class ChannelConfigService extends ServiceImpl<ChannelConfigMapper, ChannelConfig> implements IService<ChannelConfig> {
    
    /**
     * 保存渠道配置
     * @param channelConfig 渠道配置信息
     * @return 保存后的渠道配置
     */
    public ChannelConfig saveChannelConfig(ChannelConfig channelConfig) {
        channelConfig.setCreatedAt(LocalDateTime.now());
        channelConfig.setUpdatedAt(LocalDateTime.now());
        this.save(channelConfig);
        return channelConfig;
    }
    
    /**
     * 更新渠道配置
     * @param channelConfig 渠道配置信息
     * @return 更新后的渠道配置
     */
    public ChannelConfig updateChannelConfig(ChannelConfig channelConfig) {
        channelConfig.setUpdatedAt(LocalDateTime.now());
        this.updateById(channelConfig);
        return channelConfig;
    }
    
    /**
     * 根据渠道ID删除渠道配置
     * @param channelId 渠道ID
     * @return 是否删除成功
     */
    public boolean deleteChannelConfig(String channelId) {
        return this.removeById(channelId);
    }
    
    /**
     * 根据渠道ID获取渠道配置
     * @param channelId 渠道ID
     * @return 渠道配置信息
     */
    public ChannelConfig getChannelConfigById(String channelId) {
        return this.getById(channelId);
    }
    
    /**
     * 获取所有渠道配置
     * @return 渠道配置列表
     */
    public List<ChannelConfig> getAllChannelConfigs() {
        return this.list();
    }
    
    /**
     * 分页查询渠道配置
     * @param current 当前页码
     * @param size 每页大小
     * @return 渠道配置分页数据
     */
    public IPage<ChannelConfig> getPageChannelConfigs(long current, long size) {
        Page<ChannelConfig> page = new Page<>(current, size);
        return this.page(page);
    }
    
    /**
     * 根据渠道类型查询渠道配置
     * @param channelType 渠道类型
     * @return 渠道配置列表
     */
    public List<ChannelConfig> getChannelConfigsByType(String channelType) {
        LambdaQueryWrapper<ChannelConfig> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ChannelConfig::getChannelType, channelType);
        return this.list(queryWrapper);
    }
    
    /**
     * 根据启用状态查询渠道配置
     * @param enabled 启用状态
     * @return 渠道配置列表
     */
    public List<ChannelConfig> getChannelConfigsByEnabled(Boolean enabled) {
        LambdaQueryWrapper<ChannelConfig> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ChannelConfig::getEnabled, enabled);
        return this.list(queryWrapper);
    }
}