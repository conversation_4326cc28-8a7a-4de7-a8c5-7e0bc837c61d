package com.lubansoft.msg.model.repository.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import com.lubansoft.msg.model.repository.entity.MessageStatistics;

import java.time.LocalDate;
import java.util.List;

/**
 * 消息统计Repository
 */
@Mapper
public interface MessageStatisticsMapper extends BaseMapper<MessageStatistics> {
    
    /**
     * 根据统计日期和应用ID查找统计记录
     */
    @Select("SELECT * FROM message_statistics WHERE statistics_date = #{statisticsDate} AND app_id = #{appId}")
    List<MessageStatistics> findByStatisticsDateAndAppId(@Param("statisticsDate") LocalDate statisticsDate, @Param("appId") String appId);
}