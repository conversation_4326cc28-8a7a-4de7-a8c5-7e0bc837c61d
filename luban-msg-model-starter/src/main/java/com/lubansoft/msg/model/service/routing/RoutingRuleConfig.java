package com.lubansoft.msg.model.service.routing;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.Map;

/**
 * 路由规则配置
 */
@Configuration
@ConfigurationProperties(prefix = "message.gateway.routing.rule")
public class RoutingRuleConfig {
    
    /**
     * 缓存配置
     */
    private CacheConfig cache = new CacheConfig();
    
    /**
     * 验证配置
     */
    private ValidationConfig validation = new ValidationConfig();
    
    /**
     * 导入导出配置
     */
    private ImportExportConfig importExport = new ImportExportConfig();
    
    /**
     * 监控配置
     */
    private MonitorConfig monitor = new MonitorConfig();
    
    /**
     * 规则引擎配置
     */
    private EngineConfig engine = new EngineConfig();
    
    // Getters and Setters
    public CacheConfig getCache() {
        return cache;
    }
    
    public void setCache(CacheConfig cache) {
        this.cache = cache;
    }
    
    public ValidationConfig getValidation() {
        return validation;
    }
    
    public void setValidation(ValidationConfig validation) {
        this.validation = validation;
    }
    
    public ImportExportConfig getImportExport() {
        return importExport;
    }
    
    public void setImportExport(ImportExportConfig importExport) {
        this.importExport = importExport;
    }
    
    public MonitorConfig getMonitor() {
        return monitor;
    }
    
    public void setMonitor(MonitorConfig monitor) {
        this.monitor = monitor;
    }
    
    public EngineConfig getEngine() {
        return engine;
    }
    
    public void setEngine(EngineConfig engine) {
        this.engine = engine;
    }
    
    /**
     * 缓存配置
     */
    public static class CacheConfig {
        private boolean enabled = true;
        private int expireSeconds = 300;
        private int maxSize = 1000;
        private boolean refreshAsync = true;
        private int refreshIntervalSeconds = 300;
        
        // Getters and Setters
        public boolean isEnabled() {
            return enabled;
        }
        
        public void setEnabled(boolean enabled) {
            this.enabled = enabled;
        }
        
        public int getExpireSeconds() {
            return expireSeconds;
        }
        
        public void setExpireSeconds(int expireSeconds) {
            this.expireSeconds = expireSeconds;
        }
        
        public int getMaxSize() {
            return maxSize;
        }
        
        public void setMaxSize(int maxSize) {
            this.maxSize = maxSize;
        }
        
        public boolean isRefreshAsync() {
            return refreshAsync;
        }
        
        public void setRefreshAsync(boolean refreshAsync) {
            this.refreshAsync = refreshAsync;
        }
        
        public int getRefreshIntervalSeconds() {
            return refreshIntervalSeconds;
        }
        
        public void setRefreshIntervalSeconds(int refreshIntervalSeconds) {
            this.refreshIntervalSeconds = refreshIntervalSeconds;
        }
    }
    
    /**
     * 验证配置
     */
    public static class ValidationConfig {
        private boolean enabled = true;
        private boolean strictMode = false;
        private boolean checkConflicts = true;
        private boolean validateCondition = true;
        private int maxRuleNameLength = 255;
        private int maxConditionLength = 2000;
        private int maxDescriptionLength = 1000;
        
        // Getters and Setters
        public boolean isEnabled() {
            return enabled;
        }
        
        public void setEnabled(boolean enabled) {
            this.enabled = enabled;
        }
        
        public boolean isStrictMode() {
            return strictMode;
        }
        
        public void setStrictMode(boolean strictMode) {
            this.strictMode = strictMode;
        }
        
        public boolean isCheckConflicts() {
            return checkConflicts;
        }
        
        public void setCheckConflicts(boolean checkConflicts) {
            this.checkConflicts = checkConflicts;
        }
        
        public boolean isValidateCondition() {
            return validateCondition;
        }
        
        public void setValidateCondition(boolean validateCondition) {
            this.validateCondition = validateCondition;
        }
        
        public int getMaxRuleNameLength() {
            return maxRuleNameLength;
        }
        
        public void setMaxRuleNameLength(int maxRuleNameLength) {
            this.maxRuleNameLength = maxRuleNameLength;
        }
        
        public int getMaxConditionLength() {
            return maxConditionLength;
        }
        
        public void setMaxConditionLength(int maxConditionLength) {
            this.maxConditionLength = maxConditionLength;
        }
        
        public int getMaxDescriptionLength() {
            return maxDescriptionLength;
        }
        
        public void setMaxDescriptionLength(int maxDescriptionLength) {
            this.maxDescriptionLength = maxDescriptionLength;
        }
    }
    
    /**
     * 导入导出配置
     */
    public static class ImportExportConfig {
        private boolean enabled = true;
        private int maxFileSize = ********; // 10MB
        private boolean compressExport = true;
        private boolean validateOnImport = true;
        private boolean backupBeforeImport = true;
        private String backupDirectory = "./backup/routing-rules";
        
        // Getters and Setters
        public boolean isEnabled() {
            return enabled;
        }
        
        public void setEnabled(boolean enabled) {
            this.enabled = enabled;
        }
        
        public int getMaxFileSize() {
            return maxFileSize;
        }
        
        public void setMaxFileSize(int maxFileSize) {
            this.maxFileSize = maxFileSize;
        }
        
        public boolean isCompressExport() {
            return compressExport;
        }
        
        public void setCompressExport(boolean compressExport) {
            this.compressExport = compressExport;
        }
        
        public boolean isValidateOnImport() {
            return validateOnImport;
        }
        
        public void setValidateOnImport(boolean validateOnImport) {
            this.validateOnImport = validateOnImport;
        }
        
        public boolean isBackupBeforeImport() {
            return backupBeforeImport;
        }
        
        public void setBackupBeforeImport(boolean backupBeforeImport) {
            this.backupBeforeImport = backupBeforeImport;
        }
        
        public String getBackupDirectory() {
            return backupDirectory;
        }
        
        public void setBackupDirectory(String backupDirectory) {
            this.backupDirectory = backupDirectory;
        }
    }
    
    /**
     * 监控配置
     */
    public static class MonitorConfig {
        private boolean enabled = true;
        private boolean collectUsageStats = true;
        private boolean trackPerformance = true;
        private int statsRetentionDays = 30;
        private int alertThresholdSeconds = 5;
        private boolean enableAlerts = true;
        
        // Getters and Setters
        public boolean isEnabled() {
            return enabled;
        }
        
        public void setEnabled(boolean enabled) {
            this.enabled = enabled;
        }
        
        public boolean isCollectUsageStats() {
            return collectUsageStats;
        }
        
        public void setCollectUsageStats(boolean collectUsageStats) {
            this.collectUsageStats = collectUsageStats;
        }
        
        public boolean isTrackPerformance() {
            return trackPerformance;
        }
        
        public void setTrackPerformance(boolean trackPerformance) {
            this.trackPerformance = trackPerformance;
        }
        
        public int getStatsRetentionDays() {
            return statsRetentionDays;
        }
        
        public void setStatsRetentionDays(int statsRetentionDays) {
            this.statsRetentionDays = statsRetentionDays;
        }
        
        public int getAlertThresholdSeconds() {
            return alertThresholdSeconds;
        }
        
        public void setAlertThresholdSeconds(int alertThresholdSeconds) {
            this.alertThresholdSeconds = alertThresholdSeconds;
        }
        
        public boolean isEnableAlerts() {
            return enableAlerts;
        }
        
        public void setEnableAlerts(boolean enableAlerts) {
            this.enableAlerts = enableAlerts;
        }
    }
    
    /**
     * 规则引擎配置
     */
    public static class EngineConfig {
        private String type = "spel"; // spel, drools, groovy
        private boolean parallelExecution = false;
        private int threadPoolSize = 10;
        private int maxExecutionTimeMs = 5000;
        private boolean enableProfiling = false;
        private Map<String, Object> engineProperties = new HashMap<>();
        
        // Getters and Setters
        public String getType() {
            return type;
        }
        
        public void setType(String type) {
            this.type = type;
        }
        
        public boolean isParallelExecution() {
            return parallelExecution;
        }
        
        public void setParallelExecution(boolean parallelExecution) {
            this.parallelExecution = parallelExecution;
        }
        
        public int getThreadPoolSize() {
            return threadPoolSize;
        }
        
        public void setThreadPoolSize(int threadPoolSize) {
            this.threadPoolSize = threadPoolSize;
        }
        
        public int getMaxExecutionTimeMs() {
            return maxExecutionTimeMs;
        }
        
        public void setMaxExecutionTimeMs(int maxExecutionTimeMs) {
            this.maxExecutionTimeMs = maxExecutionTimeMs;
        }
        
        public boolean isEnableProfiling() {
            return enableProfiling;
        }
        
        public void setEnableProfiling(boolean enableProfiling) {
            this.enableProfiling = enableProfiling;
        }
        
        public Map<String, Object> getEngineProperties() {
            return engineProperties;
        }
        
        public void setEngineProperties(Map<String, Object> engineProperties) {
            this.engineProperties = engineProperties;
        }
    }
}