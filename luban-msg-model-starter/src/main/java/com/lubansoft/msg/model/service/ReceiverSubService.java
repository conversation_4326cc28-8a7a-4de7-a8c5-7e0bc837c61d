package com.lubansoft.msg.model.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lubansoft.msg.model.repository.entity.ReceiverSub;

import java.util.List;

/**
 * 用户订阅设置服务接口
 */
public interface ReceiverSubService {

    /**
     * 保存订阅设置
     * @param receiverSub 订阅设置实体
     * @return 保存后的实体
     */
    ReceiverSub save(ReceiverSub receiverSub);

    /**
     * 根据ID更新订阅设置
     * @param receiverSub 订阅设置实体
     * @return 更新后的实体
     */
    ReceiverSub update(ReceiverSub receiverSub);

    /**
     * 根据ID删除订阅设置
     * @param id 主键ID
     * @return 是否删除成功
     */
    boolean deleteById(Long id);

    /**
     * 根据ID查询订阅设置
     * @param id 主键ID
     * @return 订阅设置实体
     */
    ReceiverSub findById(Long id);

    /**
     * 根据用户ID查询订阅设置列表
     * @param receiverId 用户ID
     * @return 订阅设置列表
     */
    List<ReceiverSub> findByReceiverId(String receiverId);

    /**
     * 根据用户ID和渠道查询订阅设置
     * @param receiverId 用户ID
     * @param channel 渠道
     * @return 订阅设置实体
     */
    ReceiverSub findByReceiverIdAndChannel(String receiverId, String channel);

    /**
     * 分页查询订阅设置
     * @param page 分页参数
     * @param receiverId 用户ID（可选）
     * @param channel 渠道（可选）
     * @return 分页结果
     */
    IPage<ReceiverSub> findByPage(Page<ReceiverSub> page, String receiverId, String channel);

    /**
     * 更新订阅状态
     * @param receiverId 用户ID
     * @param channel 渠道
     * @param isSub 订阅状态
     * @return 是否更新成功
     */
    boolean updateSubStatus(String receiverId, String channel, Boolean isSub);
}