package com.lubansoft.msg.model.service;

import com.lubansoft.msg.common.util.JsonUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * 缓存服务类
 */
@Service
public class CacheService {
    
    private static final Logger logger = LoggerFactory.getLogger(CacheService.class);
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    // ========== 基础操作 ==========
    
    /**
     * 设置缓存
     */
    public void set(String key, Object value) {
        try {
            redisTemplate.opsForValue().set(key, value);
        } catch (Exception e) {
            logger.error("设置缓存失败, key: {}", key, e);
        }
    }
    
    /**
     * 设置缓存并指定过期时间
     */
    public void set(String key, Object value, Duration timeout) {
        try {
            redisTemplate.opsForValue().set(key, value, timeout);
        } catch (Exception e) {
            logger.error("设置缓存失败, key: {}, timeout: {}", key, timeout, e);
        }
    }
    
    /**
     * 设置缓存并指定过期时间（秒）
     */
    public void set(String key, Object value, long timeout) {
        set(key, value, Duration.ofSeconds(timeout));
    }
    
    /**
     * 设置缓存（仅当不存在时）
     * 
     * @param key 缓存键
     * @param value 缓存值
     * @param timeout 过期时间
     * @return 是否设置成功
     */
    public boolean setIfAbsent(String key, String value, Duration timeout) {
        try {
            Boolean result = redisTemplate.opsForValue().setIfAbsent(key, value, timeout);
            logger.debug("条件设置缓存: key={}, value={}, timeout={}, result={}", key, value, timeout, result);
            return Boolean.TRUE.equals(result);
        } catch (Exception e) {
            logger.error("条件设置缓存失败, key: {}, value: {}, timeout: {}", key, value, timeout, e);
            return false;
        }
    }
    
    /**
     * 获取缓存
     */
    public Object get(String key) {
        try {
            return redisTemplate.opsForValue().get(key);
        } catch (Exception e) {
            logger.error("获取缓存失败, key: {}", key, e);
            return null;
        }
    }
    
    /**
     * 获取缓存并转换为指定类型
     */
    @SuppressWarnings("unchecked")
    public <T> T get(String key, Class<T> clazz) {
        try {
            Object value = redisTemplate.opsForValue().get(key);
            if (value == null) {
                return null;
            }
            if (clazz.isInstance(value)) {
                return (T) value;
            }
            // 尝试JSON转换
            String json = JsonUtils.toJson(value);
            return JsonUtils.fromJson(json, clazz);
        } catch (Exception e) {
            logger.error("获取缓存失败, key: {}, class: {}", key, clazz.getName(), e);
            return null;
        }
    }
    
    /**
     * 删除缓存
     */
    public boolean delete(String key) {
        try {
            return Boolean.TRUE.equals(redisTemplate.delete(key));
        } catch (Exception e) {
            logger.error("删除缓存失败, key: {}", key, e);
            return false;
        }
    }
    
    /**
     * 批量删除缓存
     */
    public long delete(Collection<String> keys) {
        try {
            Long count = redisTemplate.delete(keys);
            return count != null ? count : 0;
        } catch (Exception e) {
            logger.error("批量删除缓存失败, keys: {}", keys, e);
            return 0;
        }
    }
    
    /**
     * 检查key是否存在
     */
    public boolean exists(String key) {
        try {
            return Boolean.TRUE.equals(redisTemplate.hasKey(key));
        } catch (Exception e) {
            logger.error("检查缓存存在性失败, key: {}", key, e);
            return false;
        }
    }
    
    /**
     * 设置过期时间
     */
    public boolean expire(String key, Duration timeout) {
        try {
            return Boolean.TRUE.equals(redisTemplate.expire(key, timeout));
        } catch (Exception e) {
            logger.error("设置过期时间失败, key: {}, timeout: {}", key, timeout, e);
            return false;
        }
    }
    
    /**
     * 获取过期时间
     */
    public long getExpire(String key) {
        try {
            Long expire = redisTemplate.getExpire(key, TimeUnit.SECONDS);
            return expire != null ? expire : -1;
        } catch (Exception e) {
            logger.error("获取过期时间失败, key: {}", key, e);
            return -1;
        }
    }
    
    // ========== Hash操作 ==========
    
    /**
     * 设置Hash值
     */
    public void hSet(String key, String field, Object value) {
        try {
            redisTemplate.opsForHash().put(key, field, value);
        } catch (Exception e) {
            logger.error("设置Hash缓存失败, key: {}, field: {}", key, field, e);
        }
    }
    
    /**
     * 获取Hash值
     */
    public Object hGet(String key, String field) {
        try {
            return redisTemplate.opsForHash().get(key, field);
        } catch (Exception e) {
            logger.error("获取Hash缓存失败, key: {}, field: {}", key, field, e);
            return null;
        }
    }
    
    /**
     * 获取Hash所有值
     */
    public Map<Object, Object> hGetAll(String key) {
        try {
            return redisTemplate.opsForHash().entries(key);
        } catch (Exception e) {
            logger.error("获取Hash所有缓存失败, key: {}", key, e);
            return Map.of();
        }
    }
    
    /**
     * 删除Hash字段
     */
    public boolean hDelete(String key, String... fields) {
        try {
            Long count = redisTemplate.opsForHash().delete(key, (Object[]) fields);
            return count != null && count > 0;
        } catch (Exception e) {
            logger.error("删除Hash字段失败, key: {}, fields: {}", key, fields, e);
            return false;
        }
    }
    
    /**
     * 检查Hash字段是否存在
     */
    public boolean hExists(String key, String field) {
        try {
            return redisTemplate.opsForHash().hasKey(key, field);
        } catch (Exception e) {
            logger.error("检查Hash字段存在性失败, key: {}, field: {}", key, field, e);
            return false;
        }
    }
    
    // ========== List操作 ==========
    
    /**
     * 从左侧推入List
     */
    public long lPush(String key, Object... values) {
        try {
            Long count = redisTemplate.opsForList().leftPushAll(key, values);
            return count != null ? count : 0;
        } catch (Exception e) {
            logger.error("List左推失败, key: {}", key, e);
            return 0;
        }
    }
    
    /**
     * 从右侧推入List
     */
    public long rPush(String key, Object... values) {
        try {
            Long count = redisTemplate.opsForList().rightPushAll(key, values);
            return count != null ? count : 0;
        } catch (Exception e) {
            logger.error("List右推失败, key: {}", key, e);
            return 0;
        }
    }
    
    /**
     * 从左侧弹出List
     */
    public Object lPop(String key) {
        try {
            return redisTemplate.opsForList().leftPop(key);
        } catch (Exception e) {
            logger.error("List左弹失败, key: {}", key, e);
            return null;
        }
    }
    
    /**
     * 从右侧弹出List
     */
    public Object rPop(String key) {
        try {
            return redisTemplate.opsForList().rightPop(key);
        } catch (Exception e) {
            logger.error("List右弹失败, key: {}", key, e);
            return null;
        }
    }
    
    /**
     * 获取List范围内的元素
     */
    public List<Object> lRange(String key, long start, long end) {
        try {
            return redisTemplate.opsForList().range(key, start, end);
        } catch (Exception e) {
            logger.error("获取List范围失败, key: {}, start: {}, end: {}", key, start, end, e);
            return List.of();
        }
    }
    
    /**
     * 获取List长度
     */
    public long lSize(String key) {
        try {
            Long size = redisTemplate.opsForList().size(key);
            return size != null ? size : 0;
        } catch (Exception e) {
            logger.error("获取List长度失败, key: {}", key, e);
            return 0;
        }
    }
    
    // ========== Set操作 ==========
    
    /**
     * 添加Set元素
     */
    public long sAdd(String key, Object... values) {
        try {
            Long count = redisTemplate.opsForSet().add(key, values);
            return count != null ? count : 0;
        } catch (Exception e) {
            logger.error("Set添加失败, key: {}", key, e);
            return 0;
        }
    }
    
    /**
     * 获取Set所有元素
     */
    public Set<Object> sMembers(String key) {
        try {
            return redisTemplate.opsForSet().members(key);
        } catch (Exception e) {
            logger.error("获取Set成员失败, key: {}", key, e);
            return Set.of();
        }
    }
    
    /**
     * 检查Set是否包含元素
     */
    public boolean sIsMember(String key, Object value) {
        try {
            return Boolean.TRUE.equals(redisTemplate.opsForSet().isMember(key, value));
        } catch (Exception e) {
            logger.error("检查Set成员失败, key: {}, value: {}", key, value, e);
            return false;
        }
    }
    
    /**
     * 获取Set大小
     */
    public long sSize(String key) {
        try {
            Long size = redisTemplate.opsForSet().size(key);
            return size != null ? size : 0;
        } catch (Exception e) {
            logger.error("获取Set大小失败, key: {}", key, e);
            return 0;
        }
    }
    
    /**
     * 移除Set元素
     */
    public long sRemove(String key, Object... values) {
        try {
            Long count = redisTemplate.opsForSet().remove(key, values);
            return count != null ? count : 0;
        } catch (Exception e) {
            logger.error("Set移除失败, key: {}", key, e);
            return 0;
        }
    }
    
    // ========== 原子操作 ==========
    
    /**
     * 原子递增
     */
    public long increment(String key) {
        try {
            Long result = redisTemplate.opsForValue().increment(key);
            return result != null ? result : 0;
        } catch (Exception e) {
            logger.error("原子递增失败, key: {}", key, e);
            return 0;
        }
    }
    
    /**
     * 原子递增指定值
     */
    public long increment(String key, long delta) {
        try {
            Long result = redisTemplate.opsForValue().increment(key, delta);
            return result != null ? result : 0;
        } catch (Exception e) {
            logger.error("原子递增失败, key: {}, delta: {}", key, delta, e);
            return 0;
        }
    }
    
    /**
     * 原子递减
     */
    public long decrement(String key) {
        try {
            Long result = redisTemplate.opsForValue().decrement(key);
            return result != null ? result : 0;
        } catch (Exception e) {
            logger.error("原子递减失败, key: {}", key, e);
            return 0;
        }
    }
    
    /**
     * 原子递减指定值
     */
    public long decrement(String key, long delta) {
        try {
            Long result = redisTemplate.opsForValue().decrement(key, delta);
            return result != null ? result : 0;
        } catch (Exception e) {
            logger.error("原子递减失败, key: {}, delta: {}", key, delta, e);
            return 0;
        }
    }
}