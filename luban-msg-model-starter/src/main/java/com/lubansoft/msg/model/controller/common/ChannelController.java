package com.lubansoft.msg.model.controller.common;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.lubansoft.msg.model.repository.entity.ChannelConfig;
import com.lubansoft.msg.model.service.ChannelConfigService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/channel")
@Tag(name = "渠道配置", description = "渠道配置")
public class ChannelController {

    @Autowired
    private ChannelConfigService channelConfigService;

    @Operation(summary = "列表")
    @ApiOperationSupport(order = 10)
    @RequestMapping(value = "", method = RequestMethod.GET, produces = "application/json")
    public List<ChannelConfig> create() {
        return channelConfigService.getAllChannelConfigs();
    }

}