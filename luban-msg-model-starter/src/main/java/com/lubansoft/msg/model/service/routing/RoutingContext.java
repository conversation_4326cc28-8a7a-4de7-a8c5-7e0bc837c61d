package com.lubansoft.msg.model.service.routing;

import com.lubansoft.msg.model.model.MessageRequestDTO;
import lombok.Getter;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 路由上下文
 */
public class RoutingContext {
    
    /**
     * 消息请求
     */
    private MessageRequestDTO messageRequestDTO;
    
    /**
     * 路由属性
     */
    private Map<String, Object> attributes;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * appId
     * -- GETTER --
     *  获取应用ID

     */
    @Getter
    private String appId;

    /**
     * 构造函数
     */
    public RoutingContext() {
        this.attributes = new HashMap<>();
        this.createTime = LocalDateTime.now();
    }
    
    public RoutingContext(MessageRequestDTO messageRequestDTO) {
        this();
        this.messageRequestDTO = messageRequestDTO;
    }
    
    /**
     * 获取请求ID
     */
    public String getRequestId() {
        if (messageRequestDTO != null) {
            return messageRequestDTO.getRequestId();
        }
        return (String) getAttribute("requestId");
    }

    // Getter and Setter methods
    public MessageRequestDTO getMessageRequest() {
        return messageRequestDTO;
    }
    
    public void setMessageRequest(MessageRequestDTO messageRequestDTO) {
        this.messageRequestDTO = messageRequestDTO;
    }
    
    public Map<String, Object> getAttributes() {
        return attributes;
    }
    
    public void setAttributes(Map<String, Object> attributes) {
        this.attributes = attributes;
    }
    
    public LocalDateTime getCreateTime() {
        return createTime;
    }
    
    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }
    
    /**
     * 设置属性
     */
    public void setAttribute(String key, Object value) {
        this.attributes.put(key, value);
    }
    
    /**
     * 获取属性
     */
    public Object getAttribute(String key) {
        return this.attributes.get(key);
    }
    
    /**
     * 获取属性（带类型转换）
     */
    @SuppressWarnings("unchecked")
    public <T> T getAttribute(String key, Class<T> type) {
        Object value = this.attributes.get(key);
        if (value != null && type.isInstance(value)) {
            return (T) value;
        }
        return null;
    }
    
    /**
     * 是否包含属性
     */
    public boolean hasAttribute(String key) {
        return this.attributes.containsKey(key);
    }
    
    /**
     * 移除属性
     */
    public Object removeAttribute(String key) {
        return this.attributes.remove(key);
    }
    
    @Override
    public String toString() {
        return "RoutingContext{" +
                "messageRequest=" + messageRequestDTO +
                ", attributes=" + attributes +
                ", createTime=" + createTime +
                '}';
    }
}