package com.lubansoft.msg.model.service.processor;

import com.lubansoft.base.common.exception.BusinessException;
import com.lubansoft.msg.common.model.MessageReceiver;
import com.lubansoft.msg.common.model.SendResult;
import com.lubansoft.msg.model.model.MessageRequestDTO;
import com.lubansoft.msg.common.model.MessageResponse;
import com.lubansoft.msg.common.util.IdGenerator;
import com.lubansoft.msg.model.repository.entity.MessageRecord;
import com.lubansoft.msg.model.repository.repo.MessageRecordRepo;
import com.lubansoft.msg.model.service.MessageSendService;
import com.lubansoft.msg.model.service.validation.ValidContext;
import com.lubansoft.msg.model.service.validation.ValidResult;
import com.lubansoft.msg.model.service.validation.ValidationManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.sound.midi.Receiver;
import java.text.MessageFormat;
import java.util.*;

/**
 * 消息请求预处理器
 */
@Component
public class MessageRequestProcessor {
    
    private static final Logger logger = LoggerFactory.getLogger(MessageRequestProcessor.class);
    
    @Autowired
    private RequestIdGenerator requestIdGenerator;
    
    @Autowired
    private MessageContextBuilder messageContextBuilder;

    @Autowired
    private MessageSendService messageSendService;

    @Autowired
    private ValidationManager validationManager;
    
    @Autowired
    private MessageRecordRepo messageRecordRepo;

    /**
     * 处理消息发送请求（使用外部MessageContext）
     *
     * @param request 消息请求
     * @return 消息响应
     */
    public MessageResponse processMessage(MessageRequestDTO request) {
        String appId = request.getAppId();

        // 1. 预处理请求
        MessageRequestDTO processedRequest = request;

        // 2. 验证请求
        ValidResult validResult = validationManager.valid(ValidContext.builder().appId(appId).templateId(request.getTemplateId()).params(Collections.singletonList(request.getGlobalParam())).build());
        if(!validResult.getValid()) {
            throw new BusinessException(validResult.getErrorMessage());
        }

        // 3. 生成请求ID
        String requestId = requestIdGenerator.generateRequestId();
        processedRequest.setRequestId(requestId);

        // 4. 构建MessageContext
        MessageContext messageContext = messageContextBuilder.buildContext(processedRequest);
        messageContext.setAppId(appId);
        messageContext.setRequestId(requestId);

        // 5. 调用消息发送服务进行实际处理
        SendResult sendResult = messageSendService.sendMessage(processedRequest, appId, requestId, messageContext);

        if (sendResult.isSuccess()) {
            MessageResponse response = MessageResponse.success(processedRequest.getMessageId(), requestId);
            response.setStatus("ACCEPTED");
            return response;
        } else {
            throw new BusinessException("消息发送失败");
        }

    }

    /**
     * 取消定时消息
     * 
     * @param messageId 消息ID
     * @return 消息响应
     */
    public MessageResponse cancelScheduledMessage(String messageId) {
        logger.info("取消定时消息: messageId={}", messageId);

        // TODO: 这里应该实现实际的取消逻辑
        MessageResponse response = new MessageResponse();
        response.setMessageId(messageId);
        response.setStatus("CANCELLED");

        return response;
    }
    
    /**
     * 重试失败消息
     * 
     * @param messageId 消息ID
     * @return 消息响应
     */
    public MessageResponse retryMessage(String messageId) {
        logger.info("重试失败消息: messageId={}", messageId);
        
        try {
            // TODO: 这里应该实现实际的重试逻辑
            MessageResponse response = new MessageResponse();
            response.setMessageId(messageId);
            response.setStatus("RETRYING");

            return response;
            
        } catch (Exception e) {
            logger.error("重试失败消息失败: messageId={}", messageId, e);
            throw new IllegalArgumentException("消息不存在或无法重试: " + messageId);
        }
    }
}