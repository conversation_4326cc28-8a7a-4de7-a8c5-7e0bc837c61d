package com.lubansoft.msg.model.example;

import com.lubansoft.msg.model.repository.entity.TemplateChannelMapping;
import com.lubansoft.msg.model.service.TemplateChannelMappingService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * 参数映射功能使用示例
 * 演示如何配置和使用模板渠道参数映射功能
 */
@Component
public class ParameterMappingExample {
    
    private static final Logger logger = LoggerFactory.getLogger(ParameterMappingExample.class);
    
    @Autowired
    private TemplateChannelMappingService templateChannelMappingService;
    
    /**
     * 示例1：用户注册验证码通知
     * 演示同一个模板在不同渠道使用不同的参数名称
     */
    public void setupUserRegistrationTemplate() {
        logger.info("=== 示例1：配置用户注册验证码模板的渠道映射 ===");
        
        String templateId = "user_registration_verification";
        
        // 1. 配置短信渠道映射
        TemplateChannelMapping smsMapping = new TemplateChannelMapping();
        smsMapping.setTemplateId(templateId);
        smsMapping.setChannelType("sms");
        
        // 短信渠道参数映射：通用参数 -> 短信特定参数
        Map<String, String> smsParamMapping = new HashMap<>();
        smsParamMapping.put("code", "smsCode");           // 验证码
        smsParamMapping.put("name", "userName");          // 用户名
        smsParamMapping.put("phone", "mobile");           // 手机号
        smsParamMapping.put("expireTime", "validMinutes"); // 过期时间
        smsMapping.setParameterMapping(smsParamMapping);
        
        // 短信特定模板内容
        smsMapping.setChannelTemplateContent(
            "【系统通知】尊敬的${userName}，您的注册验证码是${smsCode}，请在${validMinutes}分钟内使用。"
        );
        
        // 短信渠道扩展配置
        Map<String, Object> smsExtConfig = new HashMap<>();
        smsExtConfig.put("signName", "系统通知");
        smsExtConfig.put("timeout", 5000);
        smsExtConfig.put("retryCount", 3);
        smsExtConfig.put("maxLength", 70);
        smsMapping.setExtendedConfig(smsExtConfig);
        
        // 2. 配置邮件渠道映射
        TemplateChannelMapping emailMapping = new TemplateChannelMapping();
        emailMapping.setTemplateId(templateId);
        emailMapping.setChannelType("email");
        
        // 邮件渠道参数映射：通用参数 -> 邮件特定参数
        Map<String, String> emailParamMapping = new HashMap<>();
        emailParamMapping.put("code", "verificationCode");    // 验证码
        emailParamMapping.put("name", "recipientName");       // 收件人姓名
        emailParamMapping.put("email", "emailAddress");       // 邮箱地址
        emailParamMapping.put("expireTime", "expirationTime"); // 过期时间
        emailMapping.setParameterMapping(emailParamMapping);
        
        // 邮件特定模板内容（HTML格式）
        emailMapping.setChannelTemplateContent(
            "<html><body>" +
            "<h2>用户注册验证</h2>" +
            "<p>尊敬的 ${recipientName}：</p>" +
            "<p>您的注册验证码是：<strong style='color: #ff6600;'>${verificationCode}</strong></p>" +
            "<p>请在 ${expirationTime} 分钟内使用，如非本人操作请忽略此邮件。</p>" +
            "<hr><p style='color: #999;'>此邮件由系统自动发送，请勿回复。</p>" +
            "</body></html>"
        );
        
        // 邮件渠道扩展配置
        Map<String, Object> emailExtConfig = new HashMap<>();
        emailExtConfig.put("subject", "【重要】用户注册验证码");
        emailExtConfig.put("fromName", "系统通知");
        emailExtConfig.put("contentType", "text/html");
        emailExtConfig.put("timeout", 10000);
        emailExtConfig.put("retryCount", 2);
        emailMapping.setExtendedConfig(emailExtConfig);
        
        // 3. 配置微信渠道映射
        TemplateChannelMapping wechatMapping = new TemplateChannelMapping();
        wechatMapping.setTemplateId(templateId);
        wechatMapping.setChannelType("wechat");
        
        // 微信渠道参数映射
        Map<String, String> wechatParamMapping = new HashMap<>();
        wechatParamMapping.put("code", "wxCode");
        wechatParamMapping.put("name", "wxUserName");
        wechatParamMapping.put("expireTime", "wxExpireTime");
        wechatMapping.setParameterMapping(wechatParamMapping);
        
        // 微信特定模板内容（纯文本格式）
        wechatMapping.setChannelTemplateContent(
            "用户注册验证\n" +
            "用户：${wxUserName}\n" +
            "验证码：${wxCode}\n" +
            "有效期：${wxExpireTime}分钟\n" +
            "请及时使用"
        );
        
        // 微信渠道扩展配置
        Map<String, Object> wechatExtConfig = new HashMap<>();
        wechatExtConfig.put("agentId", "1000001");
        wechatExtConfig.put("msgType", "text");
        wechatExtConfig.put("timeout", 3000);
        wechatMapping.setExtendedConfig(wechatExtConfig);
        
        try {
            // 创建所有映射配置
            templateChannelMappingService.createMapping(smsMapping);
            logger.info("短信渠道映射配置创建成功");
            
            templateChannelMappingService.createMapping(emailMapping);
            logger.info("邮件渠道映射配置创建成功");
            
            templateChannelMappingService.createMapping(wechatMapping);
            logger.info("微信渠道映射配置创建成功");
            
            logger.info("用户注册验证码模板的所有渠道映射配置完成！");
            
        } catch (Exception e) {
            logger.error("配置渠道映射失败", e);
        }
    }
    
    /**
     * 示例2：演示参数映射的实际应用
     * 模拟发送用户注册验证码到不同渠道
     */
    public void demonstrateParameterMapping() {
        logger.info("=== 示例2：演示参数映射的实际应用 ===");
        
        String templateId = "user_registration_verification";
        
        // 模拟全局参数（业务系统传入的通用参数）
        Map<String, Object> globalParams = new HashMap<>();
        globalParams.put("code", "123456");
        globalParams.put("name", "张三");
        globalParams.put("phone", "13800138000");
        globalParams.put("email", "<EMAIL>");
        globalParams.put("expireTime", "5");
        globalParams.put("timestamp", System.currentTimeMillis());
        
        logger.info("业务系统传入的全局参数: {}", globalParams);
        
        // 1. 短信渠道参数映射
        logger.info("\n--- 短信渠道参数映射 ---");
        Map<String, Object> smsParams = templateChannelMappingService.applyParameterMapping(
            templateId, "sms", globalParams);
        logger.info("短信渠道映射后参数: {}", smsParams);
        
        // 2. 邮件渠道参数映射
        logger.info("\n--- 邮件渠道参数映射 ---");
        Map<String, Object> emailParams = templateChannelMappingService.applyParameterMapping(
            templateId, "email", globalParams);
        logger.info("邮件渠道映射后参数: {}", emailParams);
        
        // 3. 微信渠道参数映射
        logger.info("\n--- 微信渠道参数映射 ---");
        Map<String, Object> wechatParams = templateChannelMappingService.applyParameterMapping(
            templateId, "wechat", globalParams);
        logger.info("微信渠道映射后参数: {}", wechatParams);
        
        // 4. 未配置映射的渠道（如钉钉）
        logger.info("\n--- 未配置映射的渠道（钉钉）---");
        Map<String, Object> dingtalkParams = templateChannelMappingService.applyParameterMapping(
            templateId, "dingtalk", globalParams);
        logger.info("钉钉渠道参数（无映射配置，使用原参数）: {}", dingtalkParams);
        
        logger.info("\n=== 参数映射演示完成 ===");
    }
    
    /**
     * 示例3：展示渠道特定模板内容的获取
     */
    public void demonstrateChannelSpecificContent() {
        logger.info("=== 示例3：展示渠道特定模板内容 ===");
        
        String templateId = "user_registration_verification";
        
        try {
            // 获取各渠道的特定模板内容
            var smsMapping = templateChannelMappingService.getMappingByTemplateAndChannelType(templateId, "sms");
            if (smsMapping.isPresent()) {
                logger.info("短信渠道模板内容: {}", smsMapping.get().getEffectiveTemplateContent());
            }
            
            var emailMapping = templateChannelMappingService.getMappingByTemplateAndChannelType(templateId, "email");
            if (emailMapping.isPresent()) {
                logger.info("邮件渠道模板内容: {}", emailMapping.get().getEffectiveTemplateContent());
            }
            
            var wechatMapping = templateChannelMappingService.getMappingByTemplateAndChannelType(templateId, "wechat");
            if (wechatMapping.isPresent()) {
                logger.info("微信渠道模板内容: {}", wechatMapping.get().getEffectiveTemplateContent());
            }
            
        } catch (Exception e) {
            logger.error("获取渠道特定模板内容失败", e);
        }
        
        logger.info("=== 渠道特定模板内容展示完成 ===");
    }
    
    /**
     * 运行所有示例
     */
    public void runAllExamples() {
        logger.info("开始运行参数映射功能示例...");
        
        // 先配置模板映射
        setupUserRegistrationTemplate();
        
        // 等待一下确保配置生效
        try {
            Thread.sleep(1000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        // 演示参数映射应用
        demonstrateParameterMapping();
        
        // 展示渠道特定内容
        demonstrateChannelSpecificContent();
        
        logger.info("所有示例运行完成！");
    }
}
