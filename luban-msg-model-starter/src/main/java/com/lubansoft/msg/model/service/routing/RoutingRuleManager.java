package com.lubansoft.msg.model.service.routing;

import com.lubansoft.msg.model.model.MessageRequestDTO;
import lombok.extern.slf4j.Slf4j;
import com.lubansoft.msg.model.repository.entity.RoutingRule;
import com.lubansoft.msg.model.repository.repo.RoutingRuleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.locks.ReentrantReadWriteLock;
import java.util.stream.Collectors;

/**
 * 路由规则管理器
 * 提供高级的路由规则管理功能，包括动态加载、缓存管理、规则验证等
 */
@Slf4j
@Service
public class RoutingRuleManager {

    @Autowired
    private RoutingRuleService routingRuleService;
    
    @Autowired
    private RoutingEngine routingEngine;
    
    // 规则缓存
    private final Map<String, RoutingRule> ruleCache = new ConcurrentHashMap<>();
    private final Map<String, List<RoutingRule>> channelRuleCache = new ConcurrentHashMap<>();
    private volatile List<RoutingRule> enabledRulesCache = new ArrayList<>();
    
    // 读写锁，保证缓存的线程安全
    private final ReentrantReadWriteLock cacheLock = new ReentrantReadWriteLock();
    
    // 规则变更监听器
    private final List<RuleChangeListener> changeListeners = new ArrayList<>();
    
    // 规则使用统计
    private final Map<String, RuleUsageStatistics> usageStatistics = new ConcurrentHashMap<>();
    
    /**
     * 初始化规则管理器
     */
    public void initialize() {
        log.info("初始化路由规则管理器");
        
        try {
            // 加载所有规则到缓存
            reloadAllRules();
            
            log.info("路由规则管理器初始化完成，加载规则数量: {}", ruleCache.size());
        } catch (Exception e) {
            log.error("路由规则管理器初始化失败", e);
            throw new RuntimeException("路由规则管理器初始化失败", e);
        }
    }
    
    /**
     * 重新加载所有规则
     */
    public void reloadAllRules() {
        log.info("重新加载所有路由规则");
        
        cacheLock.writeLock().lock();
        try {
            // 清空缓存
            ruleCache.clear();
            channelRuleCache.clear();
            
            // 从数据库加载所有规则
            List<RoutingRule> allRules = routingRuleService.findEnabledRulesOrderByPriority();
            
            // 更新缓存
            for (RoutingRule rule : allRules) {
                ruleCache.put(rule.getRuleId(), rule);
                
                // 按渠道分组缓存（每条规则绑定一个渠道）
                String boundChannel = rule.getBoundChannel();
                if (boundChannel != null) {
                    channelRuleCache.computeIfAbsent(boundChannel, k -> new ArrayList<>()).add(rule);
                }
            }
            
            // 更新启用规则缓存
            enabledRulesCache = new ArrayList<>(allRules);
            
            // 通知路由引擎重新加载
            routingEngine.reloadRules();
            
            // 通知变更监听器
            notifyRuleChanged(RuleChangeEvent.reloadAll(allRules));
            
            log.info("路由规则重新加载完成，规则数量: {}", allRules.size());
            
        } finally {
            cacheLock.writeLock().unlock();
        }
    }
    
    /**
     * 添加规则
     */
    public RoutingRule addRule(RoutingRule rule) {
        log.info("添加路由规则: {}", rule.getRuleName());
        
        // 通过core层的RoutingRuleService创建规则
        RoutingRule createdRule = routingRuleService.createRule(rule);
        
        // 重新加载规则缓存
        reloadAllRules();
        
        log.info("路由规则添加成功: ruleId={}", createdRule.getRuleId());
        return createdRule;
    }
    
    /**
     * 更新规则
     */
    public RoutingRule updateRule(String ruleId, RoutingRule rule) {
        log.info("更新路由规则: ruleId={}", ruleId);
        
        // 通过core层的RoutingRuleService更新规则
        RoutingRule updatedRule = routingRuleService.updateRule(ruleId, rule);
        
        // 重新加载规则缓存
        reloadAllRules();
        
        log.info("路由规则更新成功: ruleId={}", ruleId);
        return updatedRule;
    }
    
    /**
     * 删除规则
     */
    public void deleteRule(String ruleId) {
        log.info("删除路由规则: ruleId={}", ruleId);
        
        // 通过core层的RoutingRuleService删除规则
        routingRuleService.deleteRule(ruleId);
        
        // 重新加载规则缓存
        reloadAllRules();
        
        log.info("路由规则删除成功: ruleId={}", ruleId);
    }
    
    /**
     * 根据ID获取规则
     */
    public RoutingRule getRuleById(String ruleId) {
        cacheLock.readLock().lock();
        try {
            return ruleCache.get(ruleId);
        } finally {
            cacheLock.readLock().unlock();
        }
    }
    
    /**
     * 获取所有启用的规则
     */
    public List<RoutingRule> getEnabledRules() {
        cacheLock.readLock().lock();
        try {
            return new ArrayList<>(enabledRulesCache);
        } finally {
            cacheLock.readLock().unlock();
        }
    }
    
    /**
     * 根据渠道获取相关规则
     */
    public List<RoutingRule> getRulesByChannel(String channel) {
        cacheLock.readLock().lock();
        try {
            List<RoutingRule> rules = channelRuleCache.get(channel);
            return rules != null ? new ArrayList<>(rules) : new ArrayList<>();
        } finally {
            cacheLock.readLock().unlock();
        }
    }
    
    /**
     * 批量启用/禁用规则
     */
    @Transactional
    public void batchUpdateRuleStatus(List<String> ruleIds, boolean enabled) {
        log.info("批量更新规则状态: count={}, enabled={}", ruleIds.size(), enabled);
        
        for (String ruleId : ruleIds) {
            try {
                routingRuleService.updateRuleStatus(ruleId, enabled);
            } catch (Exception e) {
                log.error("更新规则状态失败: ruleId={}", ruleId, e);
            }
        }
        
        // 重新加载规则
        reloadAllRules();
        
        log.info("批量更新规则状态完成");
    }
    
    /**
     * 调整规则优先级
     */
    @Transactional
    public void adjustRulePriority(String ruleId, int newPriority) {
        log.info("调整规则优先级: ruleId={}, newPriority={}", ruleId, newPriority);
        
        RoutingRule rule = getRuleById(ruleId);
        if (rule == null) {
            throw new IllegalArgumentException("路由规则不存在: " + ruleId);
        }
        
        int oldPriority = rule.getPriority();
        if (oldPriority == newPriority) {
            return; // 优先级没有变化
        }
        
        // 调整其他规则的优先级
        List<RulePriority> priorityUpdates = new ArrayList<>();
        
        if (newPriority < oldPriority) {
            // 优先级提高，需要将中间的规则优先级降低
            List<RoutingRule> affectedRules = enabledRulesCache.stream()
                .filter(r -> r.getPriority() >= newPriority && r.getPriority() < oldPriority)
                .collect(Collectors.toList());
            
            for (RoutingRule affectedRule : affectedRules) {
                priorityUpdates.add(new RulePriority(
                    affectedRule.getRuleId(), affectedRule.getPriority() + 1));
            }
        } else {
            // 优先级降低，需要将中间的规则优先级提高
            List<RoutingRule> affectedRules = enabledRulesCache.stream()
                .filter(r -> r.getPriority() > oldPriority && r.getPriority() <= newPriority)
                .collect(Collectors.toList());
            
            for (RoutingRule affectedRule : affectedRules) {
                priorityUpdates.add(new RulePriority(
                    affectedRule.getRuleId(), affectedRule.getPriority() - 1));
            }
        }
        
        // 设置目标规则的新优先级
        priorityUpdates.add(new RulePriority(ruleId, newPriority));
        
        // 批量更新优先级
        updateRulePriorities(priorityUpdates);
        
        // 重新加载规则
        reloadAllRules();
        
        log.info("规则优先级调整完成: ruleId={}, {} -> {}", ruleId, oldPriority, newPriority);
    }
    
    /**
     * 批量更新规则优先级
     */
    public void updateRulePriorities(List<RulePriority> rulePriorities) {
        log.info("批量更新路由规则优先级: count={}", rulePriorities.size());
        
        for (RulePriority rulePriority : rulePriorities) {
            routingRuleService.updatePriorityByRuleId(
                rulePriority.getRuleId(), rulePriority.getPriority());
        }
        
        // 重新加载规则
        reloadAllRules();
        
        log.info("批量更新路由规则优先级完成");
    }
    
    /**
     * 验证规则冲突
     */
    public List<RuleConflict> validateRuleConflicts(RoutingRule newRule) {
        List<RuleConflict> conflicts = new ArrayList<>();
        
        cacheLock.readLock().lock();
        try {
            for (RoutingRule existingRule : enabledRulesCache) {
                // 跳过自己
                if (newRule.getRuleId() != null && newRule.getRuleId().equals(existingRule.getRuleId())) {
                    continue;
                }
                
                // 检查是否有相同的条件
                if (newRule.getCondition().equals(existingRule.getCondition())) {
                    conflicts.add(new RuleConflict(
                        RuleConflict.ConflictType.DUPLICATE_CONDITION,
                        existingRule,
                        "存在相同的路由条件"
                    ));
                }
                
                // 检查优先级冲突
                if (Objects.equals(newRule.getPriority(), existingRule.getPriority())) {
                    conflicts.add(new RuleConflict(
                        RuleConflict.ConflictType.PRIORITY_CONFLICT,
                        existingRule,
                        "存在相同的优先级"
                    ));
                }
                
                // 检查渠道覆盖冲突（每条规则绑定一个渠道）
                String newChannel = newRule.getBoundChannel();
                String existingChannel = existingRule.getBoundChannel();

                if (newChannel != null && newChannel.equals(existingChannel) &&
                    isConditionOverlap(newRule.getCondition(), existingRule.getCondition())) {
                    conflicts.add(new RuleConflict(
                        RuleConflict.ConflictType.CHANNEL_OVERLAP,
                        existingRule,
                        "存在渠道覆盖冲突: " + newChannel
                    ));
                }
            }
        } finally {
            cacheLock.readLock().unlock();
        }
        
        return conflicts;
    }
    
    /**
     * 获取规则使用统计
     */
    public Map<String, RuleUsageStatistics> getRuleUsageStatistics() {
        return new HashMap<>(usageStatistics);
    }
    
    /**
     * 记录规则使用
     */
    public void recordRuleUsage(String ruleId, boolean matched) {
        RuleUsageStatistics stats = usageStatistics.computeIfAbsent(ruleId, 
            k -> new RuleUsageStatistics(k));
        
        stats.recordUsage(matched);
    }
    
    /**
     * 添加规则变更监听器
     */
    public void addRuleChangeListener(RuleChangeListener listener) {
        synchronized (changeListeners) {
            changeListeners.add(listener);
        }
    }
    
    /**
     * 移除规则变更监听器
     */
    public void removeRuleChangeListener(RuleChangeListener listener) {
        synchronized (changeListeners) {
            changeListeners.remove(listener);
        }
    }
    
    /**
     * 定时清理未使用的规则统计
     */
    @Scheduled(fixedRate = 3600000) // 每小时执行一次
    public void cleanupUnusedStatistics() {
        Set<String> activeRuleIds = ruleCache.keySet();
        usageStatistics.entrySet().removeIf(entry -> !activeRuleIds.contains(entry.getKey()));
    }
    
    /**
     * 定时刷新规则缓存
     */
    @Scheduled(fixedRate = 300000) // 每5分钟执行一次
    public void refreshRuleCache() {
        log.info("刷新路由规则缓存");
        try {
            // 检查数据库中的规则是否有更新
            List<RoutingRule> latestRules = routingRuleService.findEnabledRulesOrderByPriority();
            
            // 如果规则数量不同，或者规则ID集合不同，则重新加载
            if (latestRules.size() != enabledRulesCache.size() ||
                !new HashSet<>(latestRules.stream().map(RoutingRule::getRuleId).collect(Collectors.toList()))
                    .equals(new HashSet<>(enabledRulesCache.stream().map(RoutingRule::getRuleId).collect(Collectors.toList())))) {
                
                log.info("检测到路由规则变更，重新加载规则缓存");
                reloadAllRules();
            }
            
        } catch (Exception e) {
            log.error("刷新路由规则缓存失败", e);
        }
    }
    
    /**
     * 通知规则变更
     */
    private void notifyRuleChanged(RuleChangeEvent event) {
        synchronized (changeListeners) {
            for (RuleChangeListener listener : changeListeners) {
                try {
                    listener.onRuleChanged(event);
                } catch (Exception e) {
                    log.error("通知规则变更监听器失败", e);
                }
            }
        }
    }
    
    /**
     * 重新加载规则缓存
     */
    private void reloadRulesCache(List<RoutingRule> rules) {
        cacheLock.writeLock().lock();
        try {
            // 清空缓存
            ruleCache.clear();
            channelRuleCache.clear();
            
            // 更新缓存
            for (RoutingRule rule : rules) {
                ruleCache.put(rule.getRuleId(), rule);
                
                // 按渠道分组缓存（每条规则绑定一个渠道）
                String boundChannel = rule.getBoundChannel();
                if (boundChannel != null) {
                    channelRuleCache.computeIfAbsent(boundChannel, k -> new ArrayList<>()).add(rule);
                }
            }
            
            // 更新启用规则缓存
            enabledRulesCache = new ArrayList<>(rules);
            
            // 通知路由引擎重新加载
            routingEngine.reloadRules();
            
        } finally {
            cacheLock.writeLock().unlock();
        }
    }
    
    /**
     * 更新规则缓存
     */
    private void updateRuleCache(RoutingRule rule) {
        cacheLock.writeLock().lock();
        try {
            // 更新规则缓存
            ruleCache.put(rule.getRuleId(), rule);
            
            // 更新渠道规则缓存
            // 先移除旧的关联
            channelRuleCache.values().forEach(rules -> rules.removeIf(r -> r.getRuleId().equals(rule.getRuleId())));
            
            // 添加新的关联（每条规则绑定一个渠道）
            if (rule.getEnabled()) {
                String boundChannel = rule.getBoundChannel();
                if (boundChannel != null) {
                    channelRuleCache.computeIfAbsent(boundChannel, k -> new ArrayList<>()).add(rule);
                }
            }
            
            // 更新启用规则缓存
            enabledRulesCache.removeIf(r -> r.getRuleId().equals(rule.getRuleId()));
            if (rule.getEnabled()) {
                enabledRulesCache.add(rule);
                enabledRulesCache.sort(Comparator.comparing(RoutingRule::getPriority));
            }
            
        } finally {
            cacheLock.writeLock().unlock();
        }
    }
    
    /**
     * 从缓存中移除规则
     */
    private void removeRuleFromCache(RoutingRule rule) {
        cacheLock.writeLock().lock();
        try {
            // 从规则缓存中移除
            ruleCache.remove(rule.getRuleId());
            
            // 从渠道规则缓存中移除
            channelRuleCache.values().forEach(rules -> rules.removeIf(r -> r.getRuleId().equals(rule.getRuleId())));
            
            // 从启用规则缓存中移除
            enabledRulesCache.removeIf(r -> r.getRuleId().equals(rule.getRuleId()));
            
        } finally {
            cacheLock.writeLock().unlock();
        }
    }
    
    /**
     * 验证条件是否重叠
     */
    private boolean isConditionOverlap(String condition1, String condition2) {
        // 简单实现：如果条件相同则认为重叠
        // 实际应用中可能需要更复杂的逻辑来判断条件是否重叠
        return condition1.equals(condition2);
    }
    
    /**
     * 验证规则
     */
    private void validateRule(RoutingRule rule) {
        if (!org.springframework.util.StringUtils.hasText(rule.getRuleName())) {
            throw new IllegalArgumentException("规则名称不能为空");
        }
        
        if (!org.springframework.util.StringUtils.hasText(rule.getCondition())) {
            throw new IllegalArgumentException("规则条件不能为空");
        }
        
        if (rule.getBoundChannel() == null) {
            throw new IllegalArgumentException("绑定渠道不能为空");
        }
        
        // 验证条件表达式
        RuleValidationResult validationResult = validateCondition(rule.getCondition());
        if (!validationResult.isValid()) {
            throw new IllegalArgumentException("规则条件验证失败: " + validationResult.getMessage());
        }
    }
    
    /**
     * 规则冲突信息
     */
    public static class RuleConflict {
        private ConflictType type;
        private RoutingRule conflictingRule;
        private String message;
        
        public RuleConflict(ConflictType type, RoutingRule conflictingRule, String message) {
            this.type = type;
            this.conflictingRule = conflictingRule;
            this.message = message;
        }
        
        public enum ConflictType {
            DUPLICATE_CONDITION,    // 条件重复
            PRIORITY_CONFLICT,      // 优先级冲突
            CHANNEL_OVERLAP         // 渠道覆盖冲突
        }
        
        // Getters
        public ConflictType getType() { return type; }
        public RoutingRule getConflictingRule() { return conflictingRule; }
        public String getMessage() { return message; }
    }
    
    /**
     * 规则使用统计
     */
    public static class RuleUsageStatistics {
        private final String ruleId;
        private long totalUsage = 0;
        private long matchedUsage = 0;
        private LocalDateTime lastUsedTime;
        
        public RuleUsageStatistics(String ruleId) {
            this.ruleId = ruleId;
        }
        
        public synchronized void recordUsage(boolean matched) {
            totalUsage++;
            if (matched) {
                matchedUsage++;
            }
            lastUsedTime = LocalDateTime.now();
        }
        
        public double getMatchRate() {
            return totalUsage > 0 ? (double) matchedUsage / totalUsage : 0.0;
        }
        
        // Getters
        public String getRuleId() { return ruleId; }
        public long getTotalUsage() { return totalUsage; }
        public long getMatchedUsage() { return matchedUsage; }
        public LocalDateTime getLastUsedTime() { return lastUsedTime; }
    }
    
    /**
     * 规则变更事件
     */
    public static class RuleChangeEvent {
        private ChangeType type;
        private RoutingRule rule;
        private RoutingRule oldRule;
        private List<RoutingRule> rules;
        
        private RuleChangeEvent(ChangeType type) {
            this.type = type;
        }
        
        public static RuleChangeEvent added(RoutingRule rule) {
            RuleChangeEvent event = new RuleChangeEvent(ChangeType.ADDED);
            event.rule = rule;
            return event;
        }
        
        public static RuleChangeEvent updated(RoutingRule oldRule, RoutingRule newRule) {
            RuleChangeEvent event = new RuleChangeEvent(ChangeType.UPDATED);
            event.oldRule = oldRule;
            event.rule = newRule;
            return event;
        }
        
        public static RuleChangeEvent deleted(RoutingRule rule) {
            RuleChangeEvent event = new RuleChangeEvent(ChangeType.DELETED);
            event.rule = rule;
            return event;
        }
        
        public static RuleChangeEvent reloadAll(List<RoutingRule> rules) {
            RuleChangeEvent event = new RuleChangeEvent(ChangeType.RELOAD_ALL);
            event.rules = rules;
            return event;
        }
        
        public enum ChangeType {
            ADDED, UPDATED, DELETED, RELOAD_ALL
        }
        
        // Getters
        public ChangeType getType() { return type; }
        public RoutingRule getRule() { return rule; }
        public RoutingRule getOldRule() { return oldRule; }
        public List<RoutingRule> getRules() { return rules; }
    }
    
    /**
     * 规则变更监听器
     */
    public interface RuleChangeListener {
        void onRuleChanged(RuleChangeEvent event);
    }
    
    /**
     * 规则验证结果
     */
    public static class RuleValidationResult {
        private boolean valid;
        private String message;
        
        private RuleValidationResult(boolean valid, String message) {
            this.valid = valid;
            this.message = message;
        }
        
        public static RuleValidationResult success(String message) {
            return new RuleValidationResult(true, message);
        }
        
        public static RuleValidationResult failure(String message) {
            return new RuleValidationResult(false, message);
        }
        
        // Getters
        public boolean isValid() {
            return valid;
        }
        
        public String getMessage() {
            return message;
        }
    }
    
    /**
     * 验证路由规则条件
     * 
     * @param condition 条件表达式
     * @return 验证结果
     */
    public RuleValidationResult validateCondition(String condition) {
        try {
            // 创建测试用的消息请求和上下文
            MessageRequestDTO testRequest = new MessageRequestDTO();
            testRequest.setTemplateId("TEST_001");
            testRequest.setPriority(1);
            testRequest.setReceivers(new ArrayList<>());
            
            RoutingContext testContext = new RoutingContext();
            testContext.setAttribute("appId", "test_app");
            testContext.setAttribute("requestId", "test_request");
            
            // 创建测试规则
            RoutingRule testRule = new RoutingRule();
            testRule.setRuleId("test_rule");
            testRule.setCondition(condition);
            
            // 使用规则评估器验证
            RuleEvaluator evaluator = new RuleEvaluator();
            evaluator.evaluate(testRule, testRequest, testContext);
            
            return RuleValidationResult.success("条件表达式验证通过");
            
        } catch (Exception e) {
            log.error("路由规则条件验证失败: condition={}", condition, e);
            return RuleValidationResult.failure("条件表达式验证失败: " + e.getMessage());
        }
    }
    
    /**
     * 规则优先级
     */
    public static class RulePriority {
        private String ruleId;
        private Integer priority;
        
        public RulePriority() {}
        
        public RulePriority(String ruleId, Integer priority) {
            this.ruleId = ruleId;
            this.priority = priority;
        }
        
        // Getters and Setters
        public String getRuleId() {
            return ruleId;
        }
        
        public void setRuleId(String ruleId) {
            this.ruleId = ruleId;
        }
        
        public Integer getPriority() {
            return priority;
        }
        
        public void setPriority(Integer priority) {
            this.priority = priority;
        }
    }
}