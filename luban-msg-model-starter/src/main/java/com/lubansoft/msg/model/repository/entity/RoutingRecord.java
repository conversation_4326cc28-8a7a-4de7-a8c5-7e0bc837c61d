package com.lubansoft.msg.model.repository.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 路由执行记录实体（对应routing_record表）
 */
@Data
@TableName("routing_record")
public class RoutingRecord {
    
    /**
     * 主键ID，自增唯一标识
     */
    @TableId(value = "id")
    private Long id;
    
    /**
     * 请求唯一标识
     */
    @NotBlank(message = "请求ID不能为空")
    @TableField("request_id")
    private String requestId;
    
    /**
     * 模板ID
     */
    @NotBlank(message = "模板ID不能为空")
    @TableField("template_id")
    private String templateId;
    
    /**
     * 路由是否成功
     */
    @NotNull(message = "路由成功标识不能为空")
    @TableField("route_success")
    private Boolean routeSuccess;
    
    /**
     * 执行时间(毫秒)
     */
    @NotNull(message = "执行时间不能为空")
    @TableField("execution_time_ms")
    private Integer executionTimeMs;
    
    /**
     * 目标渠道列表（PostgreSQL数组类型）
     */
    @NotNull(message = "目标渠道不能为空")
    @TableField("target_channels")
    private String targetChannelsArray;
    
    /**
     * 匹配的规则ID（单个规则场景）
     */
    @TableField("matched_rule_id")
    private String matchedRuleId;
    
    /**
     * 匹配的规则名称（单个规则场景）
     */
    @TableField("matched_rule_name")
    private String matchedRuleName;
    
    /**
     * 评估的规则数量
     */
    @TableField("evaluated_rules_count")
    private Integer evaluatedRulesCount;
    
    /**
     * 匹配的规则数量
     */
    @TableField("matched_rules_count")
    private Integer matchedRulesCount;
    
    /**
     * 匹配的规则ID列表（PostgreSQL数组类型）
     */
    @TableField("matched_rule_ids")
    private String matchedRuleIdsArray;
    
    /**
     * 匹配的规则名称列表（PostgreSQL数组类型）
     */
    @TableField("matched_rule_names")
    private String matchedRuleNamesArray;
    
    /**
     * 记录创建时间
     */
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;
    
    // 构造函数
    public RoutingRecord() {}
    
    public RoutingRecord(String requestId, String templateId, Boolean routeSuccess, Integer executionTimeMs) {
        this.requestId = requestId;
        this.templateId = templateId;
        this.routeSuccess = routeSuccess;
        this.executionTimeMs = executionTimeMs;
    }
    
    /**
     * 获取目标渠道列表
     */
    public List<String> getTargetChannels() {
        return parsePostgreSQLArray(targetChannelsArray);
    }
    
    /**
     * 设置目标渠道列表
     */
    public void setTargetChannels(List<String> targetChannels) {
        this.targetChannelsArray = formatPostgreSQLArray(targetChannels);
    }
    
    /**
     * 获取匹配的规则ID列表
     */
    public List<String> getMatchedRuleIds() {
        return parsePostgreSQLArray(matchedRuleIdsArray);
    }
    
    /**
     * 设置匹配的规则ID列表
     */
    public void setMatchedRuleIds(List<String> matchedRuleIds) {
        this.matchedRuleIdsArray = formatPostgreSQLArray(matchedRuleIds);
    }
    
    /**
     * 获取匹配的规则名称列表
     */
    public List<String> getMatchedRuleNames() {
        return parsePostgreSQLArray(matchedRuleNamesArray);
    }
    
    /**
     * 设置匹配的规则名称列表
     */
    public void setMatchedRuleNames(List<String> matchedRuleNames) {
        this.matchedRuleNamesArray = formatPostgreSQLArray(matchedRuleNames);
    }
    
    /**
     * 解析PostgreSQL数组格式字符串
     */
    private List<String> parsePostgreSQLArray(String arrayStr) {
        if (arrayStr == null || arrayStr.trim().isEmpty()) {
            return List.of();
        }
        
        // 移除大括号并按逗号分割
        String content = arrayStr.replaceAll("^\\{|\\}$", "");
        if (content.trim().isEmpty()) {
            return List.of();
        }
        
        return List.of(content.split(","));
    }
    
    /**
     * 格式化为PostgreSQL数组格式
     */
    private String formatPostgreSQLArray(List<String> list) {
        if (list == null || list.isEmpty()) {
            return "{}";
        }
        
        return "{" + String.join(",", list) + "}";
    }
    
    /**
     * 路由结果枚举
     */
    public enum RouteResult {
        SUCCESS(true, "路由成功"),
        FAILED(false, "路由失败");
        
        private final boolean success;
        private final String description;
        
        RouteResult(boolean success, String description) {
            this.success = success;
            this.description = description;
        }
        
        public boolean isSuccess() {
            return success;
        }
        
        public String getDescription() {
            return description;
        }
    }
}
