package com.lubansoft.msg.model.service.producer;

import com.lubansoft.msg.common.model.MessageReceiver;
import com.lubansoft.msg.model.model.MessageReceiverDTO;
import com.lubansoft.msg.model.model.MessageRequestDTO;
import com.lubansoft.msg.model.service.QueueConfigManager;
import com.lubansoft.msg.model.service.QueueSelector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 消息生产者管理器
 * 使用队列选择器智能选择队列，消息中包含渠道信息
 */
@Service
public class MessageProducerManager {

    private static final Logger logger = LoggerFactory.getLogger(MessageProducerManager.class);

    @Autowired
    private MessageProducer messageProducer;

    @Autowired
    private QueueConfigManager queueConfigManager;

    @Autowired
    private QueueSelector queueSelector;

    /**
     * 发送消息（统一方法，支持渠道列表和按渠道分组的接收者）
     *
     * @param messageRequest   消息请求
     * @param targetChannels   目标渠道列表（可选）
     * @param channelReceivers 按渠道分组的接收者列表（可选）
     * @param properties       消息属性
     * @return 发送结果
     */
    public MessageProducer.SendResult sendMessage(MessageRequestDTO messageRequest,
                                                  List<String> targetChannels,
                                                  Map<String, List<MessageReceiverDTO>> channelReceivers,
                                                  Map<String, Object> properties) {
        // 确定发送方式
        boolean hasTargetChannels = targetChannels != null && !targetChannels.isEmpty();
        boolean hasChannelReceivers = channelReceivers != null && !channelReceivers.isEmpty();

        try {
            // 使用队列选择器选择合适的队列类型
            QueueConfigManager.QueueType queueType = queueSelector.selectQueueType(messageRequest, properties);

            // 获取队列配置
            QueueConfigManager.QueueConfig queueConfig = queueConfigManager.getQueueConfigByType(queueType);
            if (queueConfig == null) {
                logger.error("未找到队列配置: queueType={}", queueType);
                return MessageProducer.SendResult.failure("QUEUE_NOT_FOUND", "未找到队列配置: " + queueType);
            }

            // 添加渠道信息到属性中
            if (hasChannelReceivers) {
                properties.put("channelReceivers", channelReceivers);
            } else if (hasTargetChannels) {
                properties.put("targetChannel", targetChannels);
            } else {
                // 如果都没有指定，则使用空列表
                properties.put("targetChannel", new ArrayList<>());
            }
            properties.put("originalMessageId", messageRequest.getMessageId());

            // 发送消息到选定的队列
            return sendToQueue(queueConfig, messageRequest, properties);

        } catch (Exception e) {
            logger.error("发送消息异常: messageId={}", messageRequest.getMessageId(), e);
            return MessageProducer.SendResult.failure("SEND_ERROR", "发送消息异常: " + e.getMessage());
        }
    }

    /**
     * 发送消息到指定队列
     */
    @SuppressWarnings("unchecked")
    private MessageProducer.SendResult sendToQueue(QueueConfigManager.QueueConfig queueConfig,
                                                   MessageRequestDTO messageRequest,
                                                   Map<String, Object> properties) {
        Map<String, List<MessageReceiver>> channelReceivers = null;
        List<String> targetChannels = null;

        if (properties != null) {
            if (properties.containsKey("channelReceivers")) {
                channelReceivers = (Map<String, List<MessageReceiver>>) properties.get("channelReceivers");
            } else if (properties.containsKey("targetChannel")) {
                targetChannels = (List<String>) properties.get("targetChannel");
            }
        }

        return messageProducer.sendMessage(
                queueConfig.getExchangeName(),
                queueConfig.getRoutingKey(),
                messageRequest,
                targetChannels,
                channelReceivers,
                properties
        );
    }

}