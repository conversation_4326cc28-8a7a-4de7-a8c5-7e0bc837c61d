package com.lubansoft.msg.model.controller.system;

import com.lubansoft.base.common.model.ResponseEntity;
import com.lubansoft.msg.model.repository.entity.TemplateChannelMapping;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import com.lubansoft.msg.model.repository.entity.MessageTemplate;
import com.lubansoft.msg.model.repository.repo.MessageTemplateRepo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * 消息模板控制器
 */
@RestController
@RequestMapping("/templates")
@Tag(name = "消息模板", description = "消息模板管理接口")
public class MessageTemplateController {
    
    private static final Logger logger = LoggerFactory.getLogger(MessageTemplateController.class);
    
    @Autowired
    private MessageTemplateRepo messageTemplateRepo;
    
    @Operation(summary = "创建消息模板", description = "创建新的消息模板")
    @PostMapping
    public ResponseEntity<MessageTemplate> createTemplate(
            @Parameter(description = "消息模板信息") @Valid @RequestBody MessageTemplate template) {
        
        logger.info("创建消息模板: templateName={}, templateType={}", 
                   template.getTemplateName(), template.getTemplateType());
        
        try {
            MessageTemplate created = messageTemplateRepo.createTemplate(template);
            logger.info("消息模板创建成功: templateId={}", created.getTemplateId());
            
            return ResponseEntity.success(created);
            
        } catch (IllegalArgumentException e) {
            logger.info("创建消息模板参数错误: {}", e.getMessage());
            return ResponseEntity.fail(400, "创建消息模板参数错误");
            
        } catch (Exception e) {
            logger.error("创建消息模板失败", e);
            return ResponseEntity.fail(500, "创建消息模板失败");
        }
    }
    
    @Operation(summary = "更新消息模板", description = "更新现有的消息模板")
    @PutMapping("/{templateId}")
    public ResponseEntity<MessageTemplate> updateTemplate(
            @Parameter(description = "模板ID") @PathVariable String templateId,
            @Parameter(description = "消息模板信息") @Valid @RequestBody MessageTemplate template) {
        
        logger.info("更新消息模板: templateId={}", templateId);
        
        try {
            MessageTemplate updated = messageTemplateRepo.updateTemplate(templateId, template);
            logger.info("消息模板更新成功: templateId={}", updated.getTemplateId());
            
            return ResponseEntity.success(updated);
            
        } catch (IllegalArgumentException e) {
            logger.info("更新消息模板参数错误: {}", e.getMessage());
            return ResponseEntity.fail(400, "更新消息模板参数错误");
            
        } catch (Exception e) {
            logger.error("更新消息模板失败: templateId={}", templateId, e);
            return ResponseEntity.fail(500, "更新消息模板失败");
        }
    }
    
    @Operation(summary = "删除消息模板", description = "删除指定的消息模板")
    @DeleteMapping("/{templateId}")
    public ResponseEntity<Void> deleteTemplate(
            @Parameter(description = "模板ID") @PathVariable String templateId) {
        
        logger.info("删除消息模板: templateId={}", templateId);
        
        try {
            messageTemplateRepo.deleteTemplate(templateId);
            logger.info("消息模板删除成功: templateId={}", templateId);
            
            return ResponseEntity.success();
            
        } catch (IllegalArgumentException e) {
            logger.info("删除消息模板参数错误: {}", e.getMessage());
            return ResponseEntity.fail(404, "模板不存在");
            
        } catch (Exception e) {
            logger.error("删除消息模板失败: templateId={}", templateId, e);
            return ResponseEntity.fail(500, "删除消息模板失败");
        }
    }
    
    @Operation(summary = "获取消息模板", description = "根据模板ID获取消息模板")
    @GetMapping("/{templateId}")
    public ResponseEntity<MessageTemplate> getTemplate(
            @Parameter(description = "模板ID") @PathVariable String templateId) {
        
        logger.info("获取消息模板: templateId={}", templateId);
        
        try {
            Optional<MessageTemplate> template = messageTemplateRepo.findById(templateId);
            if (template.isEmpty()) {
                logger.info("模板不存在: {}", templateId);
                return ResponseEntity.fail(404, "模板不存在");
            }
            
            return ResponseEntity.success(template.get());
            
        } catch (Exception e) {
            logger.error("获取消息模板失败: templateId={}", templateId, e);
            return ResponseEntity.fail(500, "获取消息模板失败");
        }
    }
    
    @Operation(summary = "查询消息模板列表", description = "分页查询消息模板列表")
    @GetMapping
    public ResponseEntity<com.baomidou.mybatisplus.core.metadata.IPage<MessageTemplate>> getTemplates(
            @Parameter(description = "页码") @RequestParam(defaultValue = "0") int pageNum,
            @Parameter(description = "页大小") @RequestParam(defaultValue = "20") int pageSize,
            @Parameter(description = "模板类型") @RequestParam(required = false) String templateType,
            @Parameter(description = "是否启用") @RequestParam(required = false) Boolean enabled) {
        
        logger.info("查询消息模板列表: pageNum={}, size={}, templateType={}, enabled={}",
                   pageNum, pageSize, templateType, enabled);
        
        try {
            com.baomidou.mybatisplus.core.metadata.IPage<MessageTemplate> templates = messageTemplateRepo.findTemplates(pageNum, pageSize, templateType, enabled);
            
            logger.info("查询消息模板列表成功: totalElements={}", templates.getTotal());
            return ResponseEntity.success(templates);
            
        } catch (Exception e) {
            logger.error("查询消息模板列表失败", e);
            return ResponseEntity.fail(500, "查询消息模板列表失败");
        }
    }
    
    @Operation(summary = "获取启用的模板", description = "获取所有启用的模板")
    @GetMapping("/enabled")
    public ResponseEntity<List<MessageTemplate>> getEnabledTemplates() {
        
        logger.info("获取启用的模板");
        
        try {
            List<MessageTemplate> templates = messageTemplateRepo.findEnabledTemplates();
            logger.info("获取启用模板成功: count={}", templates.size());
            
            return ResponseEntity.success(templates);
            
        } catch (Exception e) {
            logger.error("获取启用模板失败", e);
            return ResponseEntity.fail(500, "获取启用模板失败");
        }
    }
    
    @Operation(summary = "根据类型获取模板", description = "获取指定类型的所有模板")
    @GetMapping("/type/{templateType}")
    public ResponseEntity<List<MessageTemplate>> getTemplatesByType(
            @Parameter(description = "模板类型") @PathVariable String templateType) {
        
        logger.info("根据类型获取模板: templateType={}", templateType);
        
        try {
            List<MessageTemplate> templates = messageTemplateRepo.findByTemplateType(templateType);
            logger.info("根据类型获取模板成功: templateType={}, count={}", templateType, templates.size());
            
            return ResponseEntity.success(templates);
            
        } catch (Exception e) {
            logger.error("根据类型获取模板失败: templateType={}", templateType, e);
            return ResponseEntity.fail(500, "根据类型获取模板失败");
        }
    }
    
    @Operation(summary = "搜索消息模板", description = "根据关键字搜索消息模板")
    @GetMapping("/search")
    public ResponseEntity<List<MessageTemplate>> searchTemplates(
            @Parameter(description = "搜索关键字") @RequestParam String keyword) {
        
        logger.info("搜索消息模板: keyword={}", keyword);
        
        try {
            List<MessageTemplate> templates = messageTemplateRepo.findByTemplateNameContaining(keyword);
            
            logger.info("搜索消息模板成功: keyword={}, count={}", keyword, templates.size());
            return ResponseEntity.success(templates);
            
        } catch (Exception e) {
            logger.error("搜索消息模板失败: keyword={}", keyword, e);
            return ResponseEntity.fail(500, "搜索消息模板失败");
        }
    }
    
    @Operation(summary = "更新模板状态", description = "启用或禁用消息模板")
    @PatchMapping("/{templateId}/status")
    public ResponseEntity<Void> updateTemplateStatus(
            @Parameter(description = "模板ID") @PathVariable String templateId,
            @Parameter(description = "是否启用") @RequestParam boolean enabled) {
        
        logger.info("更新模板状态: templateId={}, enabled={}", templateId, enabled);
        
        try {
            messageTemplateRepo.updateTemplateStatus(templateId, enabled);
            logger.info("模板状态更新成功: templateId={}, enabled={}", templateId, enabled);
            
            return ResponseEntity.success();
            
        } catch (IllegalArgumentException e) {
            logger.info("更新模板状态参数错误: {}", e.getMessage());
            return ResponseEntity.fail(404, "模板不存在");
            
        } catch (Exception e) {
            logger.error("更新模板状态失败: templateId={}", templateId, e);
            return ResponseEntity.fail(500, "更新模板状态失败");
        }
    }

    @Operation(summary = "获取模板的渠道映射配置", description = "获取指定模板的所有渠道映射配置")
    @GetMapping("/{templateId}/channel-mappings")
    public ResponseEntity<List<TemplateChannelMapping>> getTemplateChannelMappings(
            @Parameter(description = "模板ID") @PathVariable String templateId) {
        try {
            List<TemplateChannelMapping> mappings = new ArrayList<>();
            return ResponseEntity.success(mappings);
        } catch (Exception e) {
            return ResponseEntity.fail(500, "获取模板渠道映射配置失败");
        }
    }

    @Operation(summary = "检查模板是否支持渠道", description = "检查指定模板是否支持指定渠道")
    @GetMapping("/{templateId}/supports-channel/{channelId}")
    public ResponseEntity<Boolean> isChannelSupported(
            @Parameter(description = "模板ID") @PathVariable String templateId,
            @Parameter(description = "渠道ID") @PathVariable String channelId) {

        logger.info("检查模板是否支持渠道: templateId={}, channelId={}", templateId, channelId);

        try {
            boolean supported = messageTemplateRepo.isChannelSupported(templateId, channelId);

            logger.info("检查模板渠道支持完成: templateId={}, channelId={}, supported={}",
                       templateId, channelId, supported);

            return ResponseEntity.success(supported);

        } catch (Exception e) {
            logger.error("检查模板渠道支持失败: templateId={}, channelId={}", templateId, channelId, e);
            return ResponseEntity.fail(500, "检查模板渠道支持失败");
        }
    }

}