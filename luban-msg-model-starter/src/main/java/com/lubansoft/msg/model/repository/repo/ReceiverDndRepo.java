package com.lubansoft.msg.model.repository.repo;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lubansoft.msg.model.repository.entity.ReceiverDnd;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 用户免打扰设置表 Repository 接口
 */
@Mapper
public interface ReceiverDndRepo extends BaseMapper<ReceiverDnd> {

    /**
     * 根据用户ID查询免打扰设置
     * @param receiverId 用户ID
     * @return 免打扰设置列表
     */
    default List<ReceiverDnd> findByReceiverId(String receiverId) {
        return selectList(new QueryWrapper<ReceiverDnd>().eq("receiver_id", receiverId));
    }

    /**
     * 分页查询免打扰设置
     * @param page 分页参数
     * @param wrapper 查询条件
     * @return 分页结果
     */
    default IPage<ReceiverDnd> findByPage(Page<ReceiverDnd> page, @Param("ew") QueryWrapper<ReceiverDnd> wrapper) {
        return selectPage(page, wrapper);
    }

    /**
     * 根据ID更新免打扰设置
     * @param receiverDnd 免打扰设置实体
     * @return 更新影响的行数
     */
    default int updateById(ReceiverDnd receiverDnd) {
        return update(receiverDnd, new QueryWrapper<ReceiverDnd>().eq("id", receiverDnd.getId()));
    }

    /**
     * 根据用户ID和策略查询免打扰设置
     * @param receiverId 用户ID
     * @param policy 策略
     * @return 免打扰设置列表
     */
    default List<ReceiverDnd> findByReceiverIdAndPolicy(String receiverId, String policy) {
        return selectList(new QueryWrapper<ReceiverDnd>()
                .eq("receiver_id", receiverId)
                .eq("policy", policy));
    }
}