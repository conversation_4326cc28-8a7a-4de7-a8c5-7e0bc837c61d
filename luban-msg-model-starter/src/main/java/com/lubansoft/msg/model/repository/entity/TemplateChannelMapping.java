package com.lubansoft.msg.model.repository.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.lubansoft.msg.model.repository.typehandle.JsonTypeHandle;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;

/**
 * 模板渠道映射配置实体
 * 用于实现消息模板与渠道的多对多关系，支持参数映射配置
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("template_channel_mapping")
public class TemplateChannelMapping {
    
    private static final Logger logger = LoggerFactory.getLogger(TemplateChannelMapping.class);
    
    /**
     * 映射配置ID
     */
    @TableId(value = "mapping_id", type = IdType.ASSIGN_ID)
    private String mappingId;
    
    /**
     * 模板ID，关联message_template表
     */
    @NotBlank(message = "模板ID不能为空")
    @TableField("template_id")
    private String templateId;
    
    /**
     * 渠道类型
     */
    @NotBlank(message = "渠道类型不能为空")
    @TableField("channel_type")
    private String channelType;
    
    /**
     * 参数映射配置（JSON格式）
     * 示例：{"code":"sms_code", "name":"user_name", "phone":"mobile"}
     */
    @TableField(value = "parameter_mapping", typeHandler = JsonTypeHandle.class)
    private String parameterMappingJson;
    
    /**
     * 渠道特定的模板内容（可选，用于覆盖原模板内容）
     */
    @TableField("channel_template_content")
    private String channelTemplateContent;
    
    /**
     * 渠道特定的模板变量（JSON Schema格式，可选）
     */
    @TableField(value = "channel_template_variables", typeHandler = JsonTypeHandle.class)
    private String channelTemplateVariablesJson;
    
    /**
     * 扩展配置（JSON格式，用于存储渠道特定的额外配置）
     */
    @TableField(value = "extended_config", typeHandler = JsonTypeHandle.class)
    private String extendedConfigJson;
    
    /**
     * 创建时间
     */
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;
    
    /**
     * 创建者
     */
    @TableField("created_by")
    private String createdBy;
    
    /**
     * 更新者
     */
    @TableField("updated_by")
    private String updatedBy;

}
