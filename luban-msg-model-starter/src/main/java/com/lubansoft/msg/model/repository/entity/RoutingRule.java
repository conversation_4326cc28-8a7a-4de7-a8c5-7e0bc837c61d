package com.lubansoft.msg.model.repository.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.lubansoft.msg.common.util.JsonUtils;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 路由规则实体
 */
@Data
@TableName("routing_rule")
public class RoutingRule {
    
    /**
     * 规则ID
     */
    @TableId(value = "rule_id", type = IdType.ASSIGN_ID)
    private String ruleId;
    
    /**
     * 规则名称
     */
    @NotBlank(message = "规则名称不能为空")
    @Size(max = 255, message = "规则名称长度不能超过255个字符")
    @TableField("rule_name")
    private String ruleName;
    
    /**
     * 路由条件（SpEL表达式）
     */
    @NotBlank(message = "路由条件不能为空")
    @TableField("condition")
    private String condition;
    
    /**
     * 目标渠道列表（JSON格式）
     */
    @NotBlank(message = "目标渠道不能为空")
    @TableField("target_channel")
    private String targetChannel;
    
    /**
     * 优先级（数字越小优先级越高）
     */
    @NotNull(message = "优先级不能为空")
    @TableField("priority")
    private Integer priority = 0;
    
    /**
     * 是否启用
     */
    @TableField("enabled")
    private Boolean enabled = true;
    
    /**
     * 关联的模板ID（可选，为空表示适用于所有模板）
     */
    @TableField("template_id")
    private String templateId;
    
    /**
     * 规则描述
     */
    @TableField("description")
    private String description;
    
    /**
     * 创建时间
     */
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;

    /**
     * 获取绑定的单个渠道（每条规则绑定一个渠道）
     *
     * @return 绑定的渠道
     */
    public String getBoundChannel() {
        return this.targetChannel;
    }

    /**
     * 设置绑定的单个渠道
     *
     * @param channel 渠道名称
     */
    public void setBoundChannel(String channel) {
        this.targetChannel = channel;
    }

    /**
     * 获取目标渠道列表（为了兼容性，返回单个渠道的列表）
     *
     * @return 目标渠道列表
     */
    public List<String> getTargetChannels() {
        if (targetChannel == null || targetChannel.trim().isEmpty()) {
            return List.of();
        }
        return List.of(targetChannel);
    }

    /**
     * 设置目标渠道列表（为了兼容性，只使用第一个渠道）
     *
     * @param targetChannels 目标渠道列表
     */
    public void setTargetChannels(List<String> targetChannels) {
        if (targetChannels == null || targetChannels.isEmpty()) {
            this.targetChannel = null;
        } else {
            this.targetChannel = targetChannels.get(0);
            if (targetChannels.size() > 1) {
                org.slf4j.LoggerFactory.getLogger(RoutingRule.class)
                    .warn("路由规则只支持单个渠道，忽略其他渠道: ruleId={}, channels={}",
                          ruleId, targetChannels);
            }
        }
    }

}