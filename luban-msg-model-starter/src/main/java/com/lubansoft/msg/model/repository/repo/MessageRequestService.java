package com.lubansoft.msg.model.repository.repo;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lubansoft.msg.model.model.MessageRequestDTO;
import com.lubansoft.msg.model.repository.entity.MessageRequestEntity;
import com.lubansoft.msg.model.repository.mapper.MessageRequestMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * 消息请求服务
 */
@Service
public class MessageRequestService extends ServiceImpl<MessageRequestMapper, MessageRequestEntity> {
    
    private static final Logger logger = LoggerFactory.getLogger(MessageRequestService.class);
    
    /**
     * 保存消息请求
     * 
     * @param requestId 请求ID
     * @param requestDTO 请求DTO
     * @param status 初始状态
     * @return 保存的实体
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public MessageRequestEntity saveRequest(String requestId, MessageRequestDTO requestDTO, String status) {
        MessageRequestEntity entity = new MessageRequestEntity(requestId, requestDTO, status);
        save(entity);
        return entity;
    }
    
    /**
     * 根据请求ID查找消息请求
     * 
     * @param requestId 请求ID
     * @return 消息请求实体
     */
    public Optional<MessageRequestEntity> findByRequestId(String requestId) {
        return baseMapper.findByRequestId(requestId);
    }
    
    /**
     * 更新请求状态
     * 
     * @param requestId 请求ID
     * @param status 新状态
     * @return 是否更新成功
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public boolean updateStatus(String requestId, String status) {
        int updated = baseMapper.updateStatusByRequestId(requestId, status, new Date());
        return updated > 0;
    }
    
    /**
     * 根据状态查找消息请求（分页）
     * 
     * @param status 状态
     * @param pageNum 页码
     * @param pageSize 页大小
     * @return 分页结果
     */
    public IPage<MessageRequestEntity> findByStatus(String status, int pageNum, int pageSize) {
        Page<MessageRequestEntity> page = new Page<>(pageNum, pageSize);
        return baseMapper.findByStatus(status, page);
    }
    
    /**
     * 根据时间范围查找消息请求（分页）
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param pageNum 页码
     * @param pageSize 页大小
     * @return 分页结果
     */
    public IPage<MessageRequestEntity> findByTimeRange(LocalDateTime startTime, LocalDateTime endTime, 
                                                      int pageNum, int pageSize) {
        Page<MessageRequestEntity> page = new Page<>(pageNum, pageSize);
        return baseMapper.findByTimeRange(startTime, endTime, page);
    }
    
    /**
     * 统计各状态的请求数量
     * 
     * @return 状态统计列表
     */
    public List<MessageRequestMapper.StatusCount> getStatusStatistics() {
        return baseMapper.countByStatus();
    }
    
    /**
     * 统计指定时间范围内的请求数量
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 请求数量
     */
    public long countByTimeRange(LocalDateTime startTime, LocalDateTime endTime) {
        return baseMapper.countByTimeRange(startTime, endTime);
    }
    
    /**
     * 查找待处理的请求
     * 
     * @return 待处理请求列表
     */
    public List<MessageRequestEntity> findPendingRequests() {
        return list(new LambdaQueryWrapper<MessageRequestEntity>()
                .eq(MessageRequestEntity::getStatus, MessageRequestEntity.RequestStatus.PENDING.getCode())
                .orderByAsc(MessageRequestEntity::getCreatedAt));
    }
    
    /**
     * 查找失败的请求
     * 
     * @return 失败请求列表
     */
    public List<MessageRequestEntity> findFailedRequests() {
        return list(new LambdaQueryWrapper<MessageRequestEntity>()
                .eq(MessageRequestEntity::getStatus, MessageRequestEntity.RequestStatus.FAILED.getCode())
                .orderByDesc(MessageRequestEntity::getCreatedAt));
    }
    
    /**
     * 删除过期的请求记录
     * 
     * @param beforeTime 删除此时间之前的记录
     * @return 删除的记录数
     */
    @Transactional
    public int deleteExpiredRequests(LocalDateTime beforeTime) {
        logger.info("删除过期的请求记录: beforeTime={}", beforeTime);
        
        int deleted = baseMapper.delete(new LambdaQueryWrapper<MessageRequestEntity>()
                .lt(MessageRequestEntity::getCreatedAt, beforeTime)
                .in(MessageRequestEntity::getStatus, 
                    MessageRequestEntity.RequestStatus.COMPLETED.getCode(),
                    MessageRequestEntity.RequestStatus.FAILED.getCode()));
        
        logger.info("删除过期请求记录完成: count={}", deleted);
        return deleted;
    }
}
