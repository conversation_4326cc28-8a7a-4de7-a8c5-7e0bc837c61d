package com.lubansoft.msg.model.repository.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;
import com.lubansoft.msg.model.repository.entity.AppConfig;

import java.util.Optional;

/**
 * 应用配置Repository
 */
@Mapper
public interface AppConfigMapper extends BaseMapper<AppConfig> {
    
    /**
     * 根据ID查找应用配置
     */
    default Optional<AppConfig> findById(String id) {
        return Optional.ofNullable(selectById(id));
    }
    
    /**
     * 保存应用配置
     */
    default AppConfig save(AppConfig appConfig) {
        if (appConfig.getAppId() == null) {
            insert(appConfig);
        } else {
            updateById(appConfig);
        }
        return appConfig;
    }
    
    /**
     * 更新应用配额使用量
     * 
     * @param appId 应用ID
     * @param quotaUsed 已使用配额
     * @return 更新行数
     */
    @Update("UPDATE app_config SET quota_used = #{quotaUsed}, updated_at = NOW() WHERE app_id = #{appId}")
    int updateQuotaUsedByAppId(@Param("appId") String appId, @Param("quotaUsed") Integer quotaUsed);
    
    /**
     * 增加应用配额使用量
     * 
     * @param appId 应用ID
     * @param increment 增加量
     * @return 更新行数
     */
    @Update("UPDATE app_config SET quota_used = quota_used + #{increment}, updated_at = NOW() WHERE app_id = #{appId}")
    int incrementQuotaUsed(@Param("appId") String appId, @Param("increment") int increment);
    
    // BaseMapper已提供基本的CRUD操作
    // 复杂查询请使用 AppConfigService
}