package com.lubansoft.msg.model.repository.repo;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lubansoft.msg.common.util.IdGenerator;
import com.lubansoft.msg.common.util.JsonUtils;
import com.lubansoft.msg.model.repository.entity.MessageTemplate;
import com.lubansoft.msg.model.repository.mapper.MessageTemplateMapper;
import com.lubansoft.msg.model.service.CacheService;
import com.lubansoft.msg.model.util.JsonSchemaUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 消息模板服务类
 */
@Service
public class MessageTemplateRepo extends ServiceImpl<MessageTemplateMapper, MessageTemplate> {
    
    private static final Logger logger = LoggerFactory.getLogger(MessageTemplateRepo.class);
    
    private static final String CACHE_KEY_TEMPLATE = "message_template:";
    private static final String CACHE_KEY_TEMPLATE_LIST = "message_template_list:";
    private static final int CACHE_EXPIRE_SECONDS = 1800; // 30分钟缓存
    
    // 模板变量匹配模式：${variableName}
    private static final Pattern VARIABLE_PATTERN = Pattern.compile("\\$\\{([^}]+)\\}");
    
    @Autowired
    private CacheService cacheService;

    @Autowired
    private com.lubansoft.msg.model.service.TemplateChannelMappingService templateChannelMappingService;
    
    /**
     * 根据模板类型查找启用的模板
     */
    public List<MessageTemplate> findByTemplateTypeAndEnabled(String templateType) {
        return list(new LambdaQueryWrapper<MessageTemplate>()
                .eq(MessageTemplate::getTemplateType, templateType)
                .eq(MessageTemplate::isEnabled, true));
    }
    
    /**
     * 根据模板名称查找模板
     */
    public Optional<MessageTemplate> findByTemplateName(String templateName) {
        MessageTemplate template = getOne(new LambdaQueryWrapper<MessageTemplate>()
                .eq(MessageTemplate::getTemplateName, templateName));
        return Optional.ofNullable(template);
    }
    
    /**
     * 根据模板类型和启用状态查找模板
     */
    public List<MessageTemplate> findByTemplateTypeAndEnabled(String templateType, Boolean enabled) {
        return list(new LambdaQueryWrapper<MessageTemplate>()
                .eq(MessageTemplate::getTemplateType, templateType)
                .eq(MessageTemplate::isEnabled, enabled));
    }
    
    /**
     * 查找所有启用的模板
     */
    public List<MessageTemplate> findEnabledTemplatesOrderByCreatedAt() {
        return list(new LambdaQueryWrapper<MessageTemplate>()
                .eq(MessageTemplate::isEnabled, true)
                .orderByDesc(MessageTemplate::getCreatedAt));
    }
    
    /**
     * 根据模板名称模糊查询
     */
    public List<MessageTemplate> findByTemplateNameContaining(String name) {
        return list(new LambdaQueryWrapper<MessageTemplate>()
                .like(MessageTemplate::getTemplateName, name)
                .eq(MessageTemplate::isEnabled, true));
    }
    
    /**
     * 根据内容关键字查找模板
     */
    public List<MessageTemplate> findByContentContaining(String keyword) {
        return list(new LambdaQueryWrapper<MessageTemplate>()
                .like(MessageTemplate::getTemplateContent, keyword)
                .eq(MessageTemplate::isEnabled, true));
    }
    
    /**
     * 统计指定类型的模板数量
     */
    public long countByTemplateTypeAndEnabled(String templateType, Boolean enabled) {
        return count(new LambdaQueryWrapper<MessageTemplate>()
                .eq(MessageTemplate::getTemplateType, templateType)
                .eq(MessageTemplate::isEnabled, enabled));
    }
    
    /**
     * 查找指定时间范围内创建的模板
     */
    public List<MessageTemplate> findByCreatedAtBetween(LocalDateTime startTime, LocalDateTime endTime) {
        return list(new LambdaQueryWrapper<MessageTemplate>()
                .between(MessageTemplate::getCreatedAt, startTime, endTime)
                .orderByDesc(MessageTemplate::getCreatedAt));
    }
    
    /**
     * 检查模板名称是否存在（排除指定ID）
     */
    public boolean existsByTemplateNameAndTemplateIdNot(String templateName, String excludeId) {
        return count(new LambdaQueryWrapper<MessageTemplate>()
                .eq(MessageTemplate::getTemplateName, templateName)
                .ne(MessageTemplate::getTemplateId, excludeId)) > 0;
    }
    
    /**
     * 批量更新模板启用状态
     */
    public boolean updateEnabledByTemplateIdIn(List<String> templateIds, Boolean enabled) {
        return update(new LambdaUpdateWrapper<MessageTemplate>()
                .in(MessageTemplate::getTemplateId, templateIds)
                .set(MessageTemplate::isEnabled, enabled)
                .set(MessageTemplate::getUpdatedAt, LocalDateTime.now()));
    }
    
    /**
     * 删除指定时间之前的禁用模板
     */
    public boolean deleteDisabledTemplatesBeforeTime(LocalDateTime beforeTime) {
        return remove(new LambdaQueryWrapper<MessageTemplate>()
                .eq(MessageTemplate::isEnabled, false)
                .lt(MessageTemplate::getUpdatedAt, beforeTime));
    }
    
    /**
     * 创建消息模板
     * 
     * @param template 模板信息
     * @return 创建的模板
     */
    @Transactional
    public MessageTemplate createTemplate(MessageTemplate template) {
        logger.info("创建消息模板: templateName={}, templateType={}", 
                   template.getTemplateName(), template.getTemplateType());
        
        // 验证模板
        validateTemplate(template);
        
        // 检查模板ID是否已存在
        MessageTemplate existing = getById(template.getTemplateId());
        if (existing != null) {
            throw new IllegalArgumentException("模板ID已存在: " + template.getTemplateId());
        }
        
        // 设置基础信息
        if (!StringUtils.hasText(template.getTemplateId())) {
            template.setTemplateId(IdGenerator.generateId());
        }
        template.setVersion(1);
        template.setCreatedAt(LocalDateTime.now());
        template.setUpdatedAt(LocalDateTime.now());
        
        // 提取模板变量
        List<String> variables = extractVariables(template.getTemplateContent());
        template.setVariables(variables);
        
        // 保存模板
        save(template);
        
        // 清除缓存
        clearCache();
        
        logger.info("消息模板创建成功: templateId={}, templateName={}", 
                   template.getTemplateId(), template.getTemplateName());
        
        return template;
    }
    
    /**
     * 更新消息模板
     * 
     * @param templateId 模板ID
     * @param template 更新的模板信息
     * @return 更新后的模板
     */
    @Transactional
    public MessageTemplate updateTemplate(String templateId, MessageTemplate template) {
        logger.info("更新消息模板: templateId={}", templateId);
        
        // 查找现有模板
        MessageTemplate existingTemplate = getById(templateId);
        if (existingTemplate == null) {
            throw new IllegalArgumentException("模板不存在: " + templateId);
        }
        
        // 验证模板
        validateTemplate(template);
        
        // 更新模板信息
        existingTemplate.setTemplateName(template.getTemplateName());
        existingTemplate.setTemplateType(template.getTemplateType());
        existingTemplate.setTemplateContent(template.getTemplateContent());
        existingTemplate.setDescription(template.getDescription());
        existingTemplate.setEnabled(template.isEnabled());
        existingTemplate.setUpdatedAt(LocalDateTime.now());
        
        // 如果内容发生变化，增加版本号并提取变量
        if (!existingTemplate.getTemplateContent().equals(template.getTemplateContent())) {
            existingTemplate.setVersion(existingTemplate.getVersion() + 1);
            List<String> variables = extractVariables(template.getTemplateContent());
            existingTemplate.setVariables(variables);
        }
        
        // 保存模板
        updateById(existingTemplate);
        
        // 清除缓存
        clearCache();
        clearTemplateCache(templateId);
        
        logger.info("消息模板更新成功: templateId={}, version={}", 
                   existingTemplate.getTemplateId(), existingTemplate.getVersion());
        
        return existingTemplate;
    }
    
    /**
     * 删除消息模板
     * 
     * @param templateId 模板ID
     */
    @Transactional
    public void deleteTemplate(String templateId) {
        logger.info("删除消息模板: templateId={}", templateId);
        
        // 检查模板是否存在
        if (getById(templateId) == null) {
            throw new IllegalArgumentException("模板不存在: " + templateId);
        }
        
        // 删除模板
        removeById(templateId);
        
        // 清除缓存
        clearCache();
        clearTemplateCache(templateId);
        
        logger.info("消息模板删除成功: templateId={}", templateId);
    }
    
    /**
     * 根据ID查找模板
     * 
     * @param templateId 模板ID
     * @return 模板信息
     */
    public Optional<MessageTemplate> findById(String templateId) {
        // 先从缓存获取
        String cacheKey = CACHE_KEY_TEMPLATE + templateId;
//        MessageTemplate cachedTemplate = (MessageTemplate) cacheService.get(cacheKey);
//        if (cachedTemplate != null) {
//            return Optional.of(cachedTemplate);
//        }
        
        // 从数据库获取
        MessageTemplate template = getById(templateId);
//        if (template != null) {
//            template.setCreatedAt(null);
//            template.setUpdatedAt(null);
//            cacheService.set(cacheKey, template, Duration.ofMinutes(CACHE_EXPIRE_SECONDS));
//        }
        
        return Optional.ofNullable(template);
    }
    
    /**
     * 分页查询模板
     * 
     * @param pageNum 页码（从1开始）
     * @param pageSize 页大小
     * @param templateType 模板类型过滤（可选）
     * @param enabled 启用状态过滤（可选）
     * @return 分页结果
     */
    public IPage<MessageTemplate> findTemplates(int pageNum, int pageSize, String templateType, Boolean enabled) {
        Page<MessageTemplate> pageRequest = new Page<>(pageNum, pageSize);
        
        LambdaQueryWrapper<MessageTemplate> queryWrapper = new LambdaQueryWrapper<>();
        
        if (StringUtils.hasText(templateType)) {
            queryWrapper.eq(MessageTemplate::getTemplateType, templateType);
        }
        
        if (enabled != null) {
            queryWrapper.eq(MessageTemplate::isEnabled, enabled);
        }
        
        return page(pageRequest, queryWrapper);
    }
    
    /**
     * 启用/禁用模板
     * 
     * @param templateId 模板ID
     * @param enabled 启用状态
     */
    @Transactional
    public void updateTemplateStatus(String templateId, boolean enabled) {
        logger.info("更新模板状态: templateId={}, enabled={}", templateId, enabled);
        
        // 检查模板是否存在
        if (getById(templateId) == null) {
            throw new IllegalArgumentException("模板不存在: " + templateId);
        }
        
        // 更新状态
        MessageTemplate template = new MessageTemplate();
        template.setTemplateId(templateId);
        template.setEnabled(enabled);
        template.setUpdatedAt(LocalDateTime.now());
        updateById(template);
        
        // 清除缓存
        clearCache();
        clearTemplateCache(templateId);
        
        logger.info("模板状态更新成功: templateId={}, enabled={}", templateId, enabled);
    }
    
    /**
     * 查找启用的模板
     * 
     * @return 启用的模板列表
     */
    public List<MessageTemplate> findEnabledTemplates() {
        logger.debug("查找启用的模板");
        return list(new LambdaQueryWrapper<MessageTemplate>()
                .eq(MessageTemplate::isEnabled, true));
    }
    
    /**
     * 根据模板类型查找模板
     * 
     * @param templateType 模板类型
     * @return 模板列表
     */
    public List<MessageTemplate> findByTemplateType(String templateType) {
        logger.debug("根据模板类型查找模板: templateType={}", templateType);
        if (!StringUtils.hasText(templateType)) {
            return List.of();
        }
        return list(new LambdaQueryWrapper<MessageTemplate>()
                .eq(MessageTemplate::getTemplateType, templateType));
    }
    
    /**
     * 根据模板名称模糊查找模板
     * 
     * @param templateName 模板名称
     * @return 模板列表
     */
    public List<MessageTemplate> findByTemplateNameContainingIgnoreCase(String templateName) {
        logger.debug("根据模板名称模糊查找模板: templateName={}", templateName);
        if (!StringUtils.hasText(templateName)) {
            return List.of();
        }
        return list(new LambdaQueryWrapper<MessageTemplate>()
                .like(MessageTemplate::getTemplateName, templateName));
    }
    
    /**
     * 验证模板变量
     * 
     * @param templateId 模板ID
     * @param variables 变量
     * @return 验证结果
     */
    public TemplateValidationResult validateTemplateVariables(String templateId, Map<String, Object> variables) {
        logger.debug("验证模板变量: templateId={}", templateId);
        
        try {
            // 获取模板
            MessageTemplate template = findById(templateId)
                .orElseThrow(() -> new IllegalArgumentException("模板不存在: " + templateId));
            
            // 验证参数
            ValidationResult validationResult = validateParameters(template, variables);
            
            if (validationResult.isValid()) {
                return TemplateValidationResult.success("验证通过");
            } else {
                return TemplateValidationResult.failure(validationResult.getMessage());
            }
            
        } catch (Exception e) {
            logger.error("验证模板变量异常: templateId={}", templateId, e);
            return TemplateValidationResult.failure("验证异常: " + e.getMessage());
        }
    }
    
    /**
     * 获取模板统计信息
     * 
     * @return 统计信息
     */
    public TemplateStatistics getStatistics() {
        long totalCount = count();
        long enabledCount = count(new LambdaQueryWrapper<MessageTemplate>()
                .eq(MessageTemplate::isEnabled, true));
        long disabledCount = totalCount - enabledCount;
        
        // 按类型统计
        List<Map<String, Object>> typeStats = listMaps(new com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<MessageTemplate>()
                .select("template_type, COUNT(*) as count")
                .groupBy("template_type"));
        Map<String, Long> typeCountMap = new HashMap<>();
        for (Map<String, Object> stat : typeStats) {
            String type = (String) stat.get("template_type");
            Long count = ((Number) stat.get("count")).longValue();
            typeCountMap.put(type, count);
        }
        
        return new TemplateStatistics(totalCount, enabledCount, disabledCount, typeCountMap);
    }
    
    /**
     * 验证模板
     */
    private void validateTemplate(MessageTemplate template) {
        if (!StringUtils.hasText(template.getTemplateName())) {
            throw new IllegalArgumentException("模板名称不能为空");
        }
        
        if (!StringUtils.hasText(template.getTemplateType())) {
            throw new IllegalArgumentException("模板类型不能为空");
        }
        
        if (!StringUtils.hasText(template.getTemplateContent())) {
            throw new IllegalArgumentException("模板内容不能为空");
        }
        
        // 验证模板内容格式
        try {
            extractVariables(template.getTemplateContent());
        } catch (Exception e) {
            throw new IllegalArgumentException("模板内容格式错误: " + e.getMessage());
        }
    }
    
    /**
     * 提取模板变量
     */
    private List<String> extractVariables(String content) {
        List<String> variables = new java.util.ArrayList<>();
        Matcher matcher = VARIABLE_PATTERN.matcher(content);
        
        while (matcher.find()) {
            String variable = matcher.group(1);
            if (!variables.contains(variable)) {
                variables.add(variable);
            }
        }
        
        return variables;
    }
    
    /**
     * 验证参数
     */
    private ValidationResult validateParameters(MessageTemplate template, Map<String, Object> parameters) {
        if (template.getTemplateVariablesJson() == null || template.getTemplateVariablesJson().trim().isEmpty()) {
            return ValidationResult.success();
        }
        
        try {
            // 解析模板变量JSON Schema
            Map<String, Object> jsonSchema = JsonUtils.fromJson(template.getTemplateVariablesJson(), Map.class);
            if (jsonSchema == null || jsonSchema.isEmpty()) {
                return ValidationResult.success();
            }
            
            // 使用JsonSchemaUtils进行参数校验
            JsonSchemaUtils.validate(jsonSchema, parameters);
            return ValidationResult.success();
        } catch (Exception e) {
            logger.warn("模板参数校验失败: templateId={}, error={}", template.getTemplateId(), e.getMessage());
            return ValidationResult.failure(e.getMessage());
        }
    }
    
    /**
     * 清除所有相关缓存
     */
    private void clearCache() {
        // 这里可以实现更精确的缓存清除逻辑
        logger.debug("清除模板相关缓存");
    }
    
    /**
     * 清除单个模板缓存
     */
    private void clearTemplateCache(String templateId) {
        cacheService.delete(CACHE_KEY_TEMPLATE + templateId);
    }
    
    /**
     * 模板渲染结果
     */
    public static class TemplateRenderResult {
        private boolean success;
        private String content;
        private String errorCode;
        private String errorMessage;
        private long executionTimeMs;
        
        // 静态工厂方法
        public static TemplateRenderResult success(String content) {
            TemplateRenderResult result = new TemplateRenderResult();
            result.success = true;
            result.content = content;
            return result;
        }
        
        public static TemplateRenderResult failure(String errorCode, String errorMessage) {
            TemplateRenderResult result = new TemplateRenderResult();
            result.success = false;
            result.errorCode = errorCode;
            result.errorMessage = errorMessage;
            return result;
        }
        
        // Getters and Setters
        public boolean isSuccess() {
            return success;
        }
        
        public void setSuccess(boolean success) {
            this.success = success;
        }
        
        public String getContent() {
            return content;
        }
        
        public void setContent(String content) {
            this.content = content;
        }
        
        public String getErrorCode() {
            return errorCode;
        }
        
        public void setErrorCode(String errorCode) {
            this.errorCode = errorCode;
        }
        
        public String getErrorMessage() {
            return errorMessage;
        }
        
        public void setErrorMessage(String errorMessage) {
            this.errorMessage = errorMessage;
        }
        
        public long getExecutionTimeMs() {
            return executionTimeMs;
        }
        
        public void setExecutionTimeMs(long executionTimeMs) {
            this.executionTimeMs = executionTimeMs;
        }
    }
    
    /**
     * 模板验证结果
     */
    public static class TemplateValidationResult {
        private boolean valid;
        private String message;
        
        private TemplateValidationResult(boolean valid, String message) {
            this.valid = valid;
            this.message = message;
        }
        
        public static TemplateValidationResult success(String message) {
            return new TemplateValidationResult(true, message);
        }
        
        public static TemplateValidationResult failure(String message) {
            return new TemplateValidationResult(false, message);
        }
        
        public boolean isValid() {
            return valid;
        }
        
        public String getMessage() {
            return message;
        }
    }
    
    /**
     * 验证结果
     */
    private static class ValidationResult {
        private boolean valid;
        private String message;
        
        private ValidationResult(boolean valid, String message) {
            this.valid = valid;
            this.message = message;
        }
        
        public static ValidationResult success() {
            return new ValidationResult(true, null);
        }
        
        public static ValidationResult failure(String message) {
            return new ValidationResult(false, message);
        }
        
        public boolean isValid() {
            return valid;
        }
        
        public String getMessage() {
            return message;
        }
    }
    
    /**
     * 模板统计信息
     */
    public static class TemplateStatistics {
        private long totalCount;
        private long enabledCount;
        private long disabledCount;
        private Map<String, Long> typeCountMap;
        
        public TemplateStatistics(long totalCount, long enabledCount, long disabledCount) {
            this.totalCount = totalCount;
            this.enabledCount = enabledCount;
            this.disabledCount = disabledCount;
            this.typeCountMap = new HashMap<>();
        }
        
        public TemplateStatistics(long totalCount, long enabledCount, long disabledCount, Map<String, Long> typeCountMap) {
            this.totalCount = totalCount;
            this.enabledCount = enabledCount;
            this.disabledCount = disabledCount;
            this.typeCountMap = typeCountMap;
        }
        
        // Getters
        public long getTotalCount() {
            return totalCount;
        }
        
        public long getEnabledCount() {
            return enabledCount;
        }
        
        public long getDisabledCount() {
            return disabledCount;
        }
        
        public Map<String, Long> getTypeCountMap() {
            return typeCountMap;
        }
        
        public double getEnabledRate() {
            return totalCount > 0 ? (double) enabledCount / totalCount : 0.0;
        }
    }

    /**
     * 检查模板是否支持指定渠道
     *
     * @param templateId 模板ID
     * @param channelId 渠道ID
     * @return 是否支持
     */
    public boolean isChannelSupported(String templateId, String channelId) {
        logger.debug("检查模板是否支持渠道: templateId={}, channelId={}", templateId, channelId);

        return templateChannelMappingService.existsMapping(templateId, channelId);
    }

}