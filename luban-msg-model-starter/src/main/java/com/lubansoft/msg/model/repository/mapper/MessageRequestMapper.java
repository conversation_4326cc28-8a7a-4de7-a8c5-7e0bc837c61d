package com.lubansoft.msg.model.repository.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lubansoft.msg.model.repository.entity.MessageRequestEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * 消息请求Mapper
 */
@Mapper
public interface MessageRequestMapper extends BaseMapper<MessageRequestEntity> {
    
    /**
     * 根据请求ID查找消息请求
     */
    @Select("SELECT * FROM message_request WHERE request_id = #{requestId}")
    Optional<MessageRequestEntity> findByRequestId(@Param("requestId") String requestId);
    
    /**
     * 根据状态查找消息请求（分页）
     */
    @Select("SELECT * FROM message_request WHERE status = #{status} ORDER BY created_at DESC")
    IPage<MessageRequestEntity> findByStatus(@Param("status") String status, Page<?> page);
    
    /**
     * 根据时间范围查找消息请求（分页）
     */
    @Select("SELECT * FROM message_request WHERE created_at BETWEEN #{startTime} AND #{endTime} ORDER BY created_at DESC")
    IPage<MessageRequestEntity> findByTimeRange(@Param("startTime") LocalDateTime startTime, 
                                               @Param("endTime") LocalDateTime endTime, 
                                               Page<?> page);
    
    /**
     * 更新请求状态
     */
    @Update("UPDATE message_request SET status = #{status}, updated_at = #{updatedAt} WHERE request_id = #{requestId}")
    int updateStatusByRequestId(@Param("requestId") String requestId,
                               @Param("status") String status,
                               @Param("updatedAt") Date updatedAt);
    
    /**
     * 统计各状态的请求数量
     */
    @Select("SELECT status, COUNT(*) as count FROM message_request GROUP BY status")
    List<StatusCount> countByStatus();
    
    /**
     * 统计指定时间范围内的请求数量
     */
    @Select("SELECT COUNT(*) FROM message_request WHERE created_at BETWEEN #{startTime} AND #{endTime}")
    long countByTimeRange(@Param("startTime") LocalDateTime startTime, 
                         @Param("endTime") LocalDateTime endTime);
    
    /**
     * 状态统计结果
     */
    class StatusCount {
        private String status;
        private Long count;
        
        public StatusCount() {}
        
        public StatusCount(String status, Long count) {
            this.status = status;
            this.count = count;
        }
        
        public String getStatus() {
            return status;
        }
        
        public void setStatus(String status) {
            this.status = status;
        }
        
        public Long getCount() {
            return count;
        }
        
        public void setCount(Long count) {
            this.count = count;
        }
    }
}
