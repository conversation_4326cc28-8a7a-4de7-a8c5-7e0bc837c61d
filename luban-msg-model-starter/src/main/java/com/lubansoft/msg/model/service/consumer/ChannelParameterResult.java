package com.lubansoft.msg.model.service.consumer;

import lombok.Data;

import java.util.Map;

/**
 * 渠道参数转换结果
 * 包含转换后的模板参数和全局参数
 */
@Data
public class ChannelParameterResult {
    
    /**
     * 转换后的模板参数
     * 根据parameter_mapping配置转换的参数
     */
    private Map<String, Object> templateParam;
    
    /**
     * 全局参数
     * 从模板映射数据的extraParam中获取
     */
    private Map<String, Object> globalParam;
    
    public ChannelParameterResult() {}
    
    public ChannelParameterResult(Map<String, Object> templateParam, Map<String, Object> globalParam) {
        this.templateParam = templateParam;
        this.globalParam = globalParam;
    }
}
