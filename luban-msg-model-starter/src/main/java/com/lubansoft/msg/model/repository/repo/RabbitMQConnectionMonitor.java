package com.lubansoft.msg.model.repository.repo;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.rabbit.connection.Connection;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.connection.ConnectionListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicLong;

/**
 * RabbitMQ连接监控服务
 */
@Service
public class RabbitMQConnectionMonitor implements ConnectionListener {
    
    private static final Logger logger = LoggerFactory.getLogger(RabbitMQConnectionMonitor.class);
    
    @Autowired
    private ConnectionFactory connectionFactory;
    
    // 连接状态
    private final AtomicBoolean isConnected = new AtomicBoolean(false);
    private final AtomicLong lastConnectedTime = new AtomicLong(0);
    private final AtomicLong lastDisconnectedTime = new AtomicLong(0);
    private final AtomicLong connectionCount = new AtomicLong(0);
    private final AtomicLong disconnectionCount = new AtomicLong(0);
    
    // 连接信息
    private volatile String currentConnectionInfo = "";
    
    @PostConstruct
    public void initialize() {
        logger.info("初始化RabbitMQ连接监控器");
        
        // 注册连接监听器
        if (connectionFactory instanceof org.springframework.amqp.rabbit.connection.CachingConnectionFactory) {
            connectionFactory
                    .addConnectionListener(this);
        }
        
        // 初始连接检查
        checkConnection();
    }
    
    @Override
    public void onCreate(Connection connection) {
        isConnected.set(true);
        lastConnectedTime.set(System.currentTimeMillis());
        connectionCount.incrementAndGet();
        currentConnectionInfo = connection.toString();
        
        logger.debug("RabbitMQ连接建立: connection={}", connection);
    }
    
    @Override
    public void onClose(Connection connection) {
        isConnected.set(false);
        lastDisconnectedTime.set(System.currentTimeMillis());
        disconnectionCount.incrementAndGet();
        
        logger.debug("RabbitMQ连接关闭: connection={}", connection);
    }
    
    @Override
    public void onShutDown(com.rabbitmq.client.ShutdownSignalException signal) {
        isConnected.set(false);
        lastDisconnectedTime.set(System.currentTimeMillis());
        
        if (signal.isInitiatedByApplication()) {
            logger.info("RabbitMQ连接正常关闭: reason={}", signal.getReason());
        } else {
            logger.error("RabbitMQ连接异常关闭: reason={}", signal.getReason(), signal);
        }
    }
    
    /**
     * 定期检查连接状态
     */
    @Scheduled(fixedDelay = 30000) // 每30秒检查一次
    public void checkConnection() {
        try {
            Connection connection = connectionFactory.createConnection();
            if (connection.isOpen()) {
                if (!isConnected.get()) {
                    logger.info("RabbitMQ连接恢复正常");
                    isConnected.set(true);
                    lastConnectedTime.set(System.currentTimeMillis());
                }
            } else {
                if (isConnected.get()) {
                    logger.info("RabbitMQ连接已断开");
                    isConnected.set(false);
                    lastDisconnectedTime.set(System.currentTimeMillis());
                }
            }
            connection.close();
        } catch (Exception e) {
            if (isConnected.get()) {
                logger.error("RabbitMQ连接检查失败", e);
                isConnected.set(false);
                lastDisconnectedTime.set(System.currentTimeMillis());
            }
        }
    }
    
    /**
     * 获取连接状态信息
     */
    public ConnectionStatus getConnectionStatus() {
        ConnectionStatus status = new ConnectionStatus();
        status.setConnected(isConnected.get());
        status.setLastConnectedTime(lastConnectedTime.get());
        status.setLastDisconnectedTime(lastDisconnectedTime.get());
        status.setConnectionCount(connectionCount.get());
        status.setDisconnectionCount(disconnectionCount.get());
        status.setCurrentConnectionInfo(currentConnectionInfo);
        
        // 计算连接持续时间
        if (isConnected.get() && lastConnectedTime.get() > 0) {
            status.setConnectionDuration(System.currentTimeMillis() - lastConnectedTime.get());
        }
        
        // 计算断开持续时间
        if (!isConnected.get() && lastDisconnectedTime.get() > 0) {
            status.setDisconnectionDuration(System.currentTimeMillis() - lastDisconnectedTime.get());
        }
        
        return status;
    }
    
    /**
     * 手动触发连接检查
     */
    public boolean testConnection() {
        try {
            Connection connection = connectionFactory.createConnection();
            boolean isOpen = connection.isOpen();
            connection.close();
            
            logger.info("手动连接测试结果: {}", isOpen ? "成功" : "失败");
            return isOpen;
            
        } catch (Exception e) {
            logger.error("手动连接测试失败", e);
            return false;
        }
    }
    
    /**
     * 强制重新连接
     */
    public boolean forceReconnect() {
        try {
            if (connectionFactory instanceof org.springframework.amqp.rabbit.connection.CachingConnectionFactory) {
                ((org.springframework.amqp.rabbit.connection.CachingConnectionFactory) connectionFactory)
                    .resetConnection();
                
                // 等待一段时间后检查连接
                Thread.sleep(1000);
                return testConnection();
            }
            return false;
        } catch (Exception e) {
            logger.error("强制重连失败", e);
            return false;
        }
    }
    
    /**
     * 获取连接工厂信息
     */
    public Map<String, Object> getConnectionFactoryInfo() {
        Map<String, Object> info = new HashMap<>();
        
        if (connectionFactory instanceof org.springframework.amqp.rabbit.connection.CachingConnectionFactory) {
            org.springframework.amqp.rabbit.connection.CachingConnectionFactory cachingFactory = 
                (org.springframework.amqp.rabbit.connection.CachingConnectionFactory) connectionFactory;
            
            info.put("host", cachingFactory.getHost());
            info.put("port", cachingFactory.getPort());
            info.put("virtualHost", cachingFactory.getVirtualHost());
            info.put("username", cachingFactory.getUsername());
            info.put("channelCacheSize", cachingFactory.getChannelCacheSize());
            info.put("connectionCacheSize", cachingFactory.getConnectionCacheSize());
            info.put("publisherConfirms", cachingFactory.isPublisherConfirms());
            info.put("publisherReturns", cachingFactory.isPublisherReturns());
        }
        
        return info;
    }
    
    @PreDestroy
    public void destroy() {
        logger.info("关闭RabbitMQ连接监控器");
    }
    
    /**
     * 连接状态类
     */
    public static class ConnectionStatus {
        private boolean connected;
        private long lastConnectedTime;
        private long lastDisconnectedTime;
        private long connectionCount;
        private long disconnectionCount;
        private long connectionDuration;
        private long disconnectionDuration;
        private String currentConnectionInfo;
        
        // Getters and Setters
        public boolean isConnected() {
            return connected;
        }
        
        public void setConnected(boolean connected) {
            this.connected = connected;
        }
        
        public long getLastConnectedTime() {
            return lastConnectedTime;
        }
        
        public void setLastConnectedTime(long lastConnectedTime) {
            this.lastConnectedTime = lastConnectedTime;
        }
        
        public long getLastDisconnectedTime() {
            return lastDisconnectedTime;
        }
        
        public void setLastDisconnectedTime(long lastDisconnectedTime) {
            this.lastDisconnectedTime = lastDisconnectedTime;
        }
        
        public long getConnectionCount() {
            return connectionCount;
        }
        
        public void setConnectionCount(long connectionCount) {
            this.connectionCount = connectionCount;
        }
        
        public long getDisconnectionCount() {
            return disconnectionCount;
        }
        
        public void setDisconnectionCount(long disconnectionCount) {
            this.disconnectionCount = disconnectionCount;
        }
        
        public long getConnectionDuration() {
            return connectionDuration;
        }
        
        public void setConnectionDuration(long connectionDuration) {
            this.connectionDuration = connectionDuration;
        }
        
        public long getDisconnectionDuration() {
            return disconnectionDuration;
        }
        
        public void setDisconnectionDuration(long disconnectionDuration) {
            this.disconnectionDuration = disconnectionDuration;
        }
        
        public String getCurrentConnectionInfo() {
            return currentConnectionInfo;
        }
        
        public void setCurrentConnectionInfo(String currentConnectionInfo) {
            this.currentConnectionInfo = currentConnectionInfo;
        }
    }
}