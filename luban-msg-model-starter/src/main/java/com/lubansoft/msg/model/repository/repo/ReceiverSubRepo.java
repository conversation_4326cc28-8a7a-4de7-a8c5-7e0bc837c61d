package com.lubansoft.msg.model.repository.repo;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lubansoft.msg.model.repository.entity.ReceiverSub;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 用户订阅设置表 Repository 接口
 */
@Mapper
public interface ReceiverSubRepo extends BaseMapper<ReceiverSub> {

    /**
     * 根据用户ID查询订阅设置
     * @param receiverId 用户ID
     * @return 订阅设置列表
     */
    default List<ReceiverSub> findByReceiverId(String receiverId) {
        return selectList(new QueryWrapper<ReceiverSub>().eq("receiver_id", receiverId));
    }

    /**
     * 根据用户ID和渠道查询订阅设置
     * @param receiverId 用户ID
     * @param channel 渠道
     * @return 订阅设置
     */
    default ReceiverSub findByReceiverIdAndChannel(String receiverId, String channel) {
        return selectOne(new QueryWrapper<ReceiverSub>()
                .eq("receiver_id", receiverId)
                .eq("channel", channel));
    }

    /**
     * 分页查询订阅设置
     * @param page 分页参数
     * @param wrapper 查询条件
     * @return 分页结果
     */
    default IPage<ReceiverSub> findByPage(Page<ReceiverSub> page, @Param("ew") QueryWrapper<ReceiverSub> wrapper) {
        return selectPage(page, wrapper);
    }

    /**
     * 根据ID更新订阅设置
     * @param receiverSub 订阅设置实体
     * @return 更新影响的行数
     */
    default int updateById(ReceiverSub receiverSub) {
        return update(receiverSub, new QueryWrapper<ReceiverSub>().eq("id", receiverSub.getId()));
    }

    /**
     * 根据用户ID和渠道更新订阅状态
     * @param receiverId 用户ID
     * @param channel 渠道
     * @param isSub 订阅状态
     * @return 更新影响的行数
     */
    default int updateSubStatus(String receiverId, String channel, Boolean isSub) {
        ReceiverSub receiverSub = new ReceiverSub();
        receiverSub.setIsSub(isSub);
        return update(receiverSub, new QueryWrapper<ReceiverSub>()
                .eq("receiver_id", receiverId)
                .eq("channel", channel));
    }
}