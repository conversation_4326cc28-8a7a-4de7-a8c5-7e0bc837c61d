package com.lubansoft.msg.model.repository.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import com.lubansoft.msg.model.repository.entity.MessageTemplate;

import java.util.List;
import java.util.Optional;

/**
 * 消息模板Repository
 */
@Mapper
public interface MessageTemplateMapper extends BaseMapper<MessageTemplate> {
    
    /**
     * 根据模板名称查找模板
     * 
     * @param templateName 模板名称
     * @return 模板信息
     */
    @Select("SELECT * FROM message_template WHERE template_name = #{templateName} AND deleted = 0 LIMIT 1")
    Optional<MessageTemplate> findByTemplateName(@Param("templateName") String templateName);
    
    /**
     * 根据模板类型和启用状态查找模板（分页）
     * 
     * @param templateType 模板类型
     * @param enabled 是否启用
     * @param pageable 分页参数
     * @return 分页结果
     */
    @Select("SELECT * FROM message_template WHERE template_type = #{templateType} AND enabled = #{enabled} AND deleted = 0 ORDER BY created_at DESC")
    IPage<MessageTemplate> findByTemplateTypeAndEnabled(@Param("templateType") String templateType, 
                                                        @Param("enabled") Boolean enabled, 
                                                        Page<MessageTemplate> pageable);
    
    /**
     * 根据模板类型查找模板（分页）
     * 
     * @param templateType 模板类型
     * @param pageable 分页参数
     * @return 分页结果
     */
    @Select("SELECT * FROM message_template WHERE template_type = #{templateType} AND deleted = 0 ORDER BY created_at DESC")
    IPage<MessageTemplate> findByTemplateType(@Param("templateType") String templateType, 
                                             Page<MessageTemplate> pageable);
    
    /**
     * 根据启用状态查找模板（分页）
     * 
     * @param enabled 是否启用
     * @param pageable 分页参数
     * @return 分页结果
     */
    @Select("SELECT * FROM message_template WHERE enabled = #{enabled} AND deleted = 0 ORDER BY created_at DESC")
    IPage<MessageTemplate> findByEnabled(@Param("enabled") Boolean enabled, 
                                        Page<MessageTemplate> pageable);
    
    /**
     * 查找所有模板（分页）
     * 
     * @param pageable 分页参数
     * @return 分页结果
     */
    @Select("SELECT * FROM message_template WHERE deleted = 0 ORDER BY created_at DESC")
    IPage<MessageTemplate> findAll(Page<MessageTemplate> pageable);
    
    /**
     * 根据模板名称模糊查询
     * 
     * @param templateName 模板名称关键词
     * @return 模板列表
     */
    @Select("SELECT * FROM message_template WHERE template_name LIKE CONCAT('%', #{templateName}, '%') AND deleted = 0 ORDER BY created_at DESC")
    List<MessageTemplate> findByTemplateNameContainingIgnoreCase(@Param("templateName") String templateName);
    
    /**
     * 更新模板启用状态
     * 
     * @param templateId 模板ID
     * @param enabled 是否启用
     * @return 更新行数
     */
    @Update("UPDATE message_template SET enabled = #{enabled}, updated_at = NOW() WHERE template_id = #{templateId}")
    int updateEnabledByTemplateId(@Param("templateId") String templateId, @Param("enabled") boolean enabled);
    
    /**
     * 统计启用的模板数量
     * 
     * @return 启用的模板数量
     */
    @Select("SELECT COUNT(*) FROM message_template WHERE enabled = 1 AND deleted = 0")
    long countByEnabledTrue();
    
    /**
     * 查找模板类型统计信息
     * 
     * @return 统计信息列表
     */
    @Select("SELECT template_type, COUNT(*) as count FROM message_template WHERE deleted = 0 GROUP BY template_type")
    List<java.util.Map<String, Object>> findTemplateTypeStatistics();
    
    /**
     * 根据启用状态查找模板列表
     * 
     * @param enabled 是否启用
     * @return 模板列表
     */
    @Select("SELECT * FROM message_template WHERE enabled = #{enabled} AND deleted = 0 ORDER BY created_at DESC")
    List<MessageTemplate> findByEnabled(@Param("enabled") Boolean enabled);
    
    /**
     * 根据模板类型查找模板列表
     * 
     * @param templateType 模板类型
     * @return 模板列表
     */
    @Select("SELECT * FROM message_template WHERE template_type = #{templateType} AND deleted = 0 ORDER BY created_at DESC")
    List<MessageTemplate> findByTemplateType(@Param("templateType") String templateType);
    
    // BaseMapper已提供基本的CRUD操作
    // 复杂查询请使用 MessageTemplateService
}