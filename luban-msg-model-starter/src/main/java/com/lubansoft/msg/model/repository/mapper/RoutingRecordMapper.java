package com.lubansoft.msg.model.repository.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lubansoft.msg.model.repository.entity.RoutingRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Date;
import java.util.List;

/**
 * 路由记录Mapper
 */
@Mapper
public interface RoutingRecordMapper extends BaseMapper<RoutingRecord> {
    
    /**
     * 根据请求ID查找路由记录
     */
    @Select("SELECT * FROM routing_record WHERE request_id = #{requestId} ORDER BY created_at DESC")
    List<RoutingRecord> findByRequestId(@Param("requestId") String requestId);
    
    /**
     * 根据模板ID查找路由记录（分页）
     */
    @Select("SELECT * FROM routing_record WHERE template_id = #{templateId} ORDER BY created_at DESC")
    IPage<RoutingRecord> findByTemplateId(@Param("templateId") String templateId, Page<?> page);
    
    /**
     * 根据路由结果查找记录（分页）
     */
    @Select("SELECT * FROM routing_record WHERE route_success = #{routeSuccess} ORDER BY created_at DESC")
    IPage<RoutingRecord> findByRouteSuccess(@Param("routeSuccess") Boolean routeSuccess, Page<?> page);
    
    /**
     * 根据时间范围查找路由记录（分页）
     */
    @Select("SELECT * FROM routing_record WHERE created_at BETWEEN #{startTime} AND #{endTime} ORDER BY created_at DESC")
    IPage<RoutingRecord> findByTimeRange(@Param("startTime") Date startTime, 
                                        @Param("endTime") Date endTime, 
                                        Page<?> page);
    
    /**
     * 根据匹配的规则ID查找记录
     */
    @Select("SELECT * FROM routing_record WHERE matched_rule_id = #{ruleId} OR #{ruleId} = ANY(matched_rule_ids) ORDER BY created_at DESC")
    List<RoutingRecord> findByMatchedRuleId(@Param("ruleId") String ruleId);
    
    /**
     * 根据目标渠道查找记录
     */
    @Select("SELECT * FROM routing_record WHERE #{channel} = ANY(target_channels) ORDER BY created_at DESC")
    List<RoutingRecord> findByTargetChannel(@Param("channel") String channel);
    
    /**
     * 统计路由成功率
     */
    @Select("SELECT " +
            "COUNT(*) as total_count, " +
            "COUNT(CASE WHEN route_success = true THEN 1 END) as success_count, " +
            "COUNT(CASE WHEN route_success = false THEN 1 END) as failed_count " +
            "FROM routing_record " +
            "WHERE created_at BETWEEN #{startTime} AND #{endTime}")
    RoutingStatistics getRoutingStatistics(@Param("startTime") Date startTime, 
                                          @Param("endTime") Date endTime);
    
    /**
     * 统计平均执行时间
     */
    @Select("SELECT AVG(execution_time_ms) FROM routing_record WHERE route_success = true AND created_at BETWEEN #{startTime} AND #{endTime}")
    Double getAverageExecutionTime(@Param("startTime") Date startTime, 
                                  @Param("endTime") Date endTime);
    
    /**
     * 统计各模板的路由情况
     */
    @Select("SELECT template_id, " +
            "COUNT(*) as total_count, " +
            "COUNT(CASE WHEN route_success = true THEN 1 END) as success_count, " +
            "AVG(execution_time_ms) as avg_execution_time " +
            "FROM routing_record " +
            "WHERE created_at BETWEEN #{startTime} AND #{endTime} " +
            "GROUP BY template_id " +
            "ORDER BY total_count DESC")
    List<TemplateRoutingStatistics> getTemplateRoutingStatistics(@Param("startTime") Date startTime, 
                                                                @Param("endTime") Date endTime);
    
    /**
     * 统计各规则的匹配情况
     */
    @Select("SELECT matched_rule_id, matched_rule_name, " +
            "COUNT(*) as match_count, " +
            "AVG(execution_time_ms) as avg_execution_time " +
            "FROM routing_record " +
            "WHERE matched_rule_id IS NOT NULL AND created_at BETWEEN #{startTime} AND #{endTime} " +
            "GROUP BY matched_rule_id, matched_rule_name " +
            "ORDER BY match_count DESC")
    List<RuleMatchStatistics> getRuleMatchStatistics(@Param("startTime") Date startTime, 
                                                     @Param("endTime") Date endTime);
    
    /**
     * 删除过期的路由记录
     */
    @Select("DELETE FROM routing_record WHERE created_at < #{beforeTime}")
    int deleteExpiredRecords(@Param("beforeTime") Date beforeTime);
    
    /**
     * 路由统计结果
     */
    class RoutingStatistics {
        private Long totalCount;
        private Long successCount;
        private Long failedCount;
        
        public RoutingStatistics() {}
        
        public RoutingStatistics(Long totalCount, Long successCount, Long failedCount) {
            this.totalCount = totalCount;
            this.successCount = successCount;
            this.failedCount = failedCount;
        }
        
        public Double getSuccessRate() {
            return totalCount > 0 ? (double) successCount / totalCount : 0.0;
        }
        
        // Getters and Setters
        public Long getTotalCount() { return totalCount; }
        public void setTotalCount(Long totalCount) { this.totalCount = totalCount; }
        public Long getSuccessCount() { return successCount; }
        public void setSuccessCount(Long successCount) { this.successCount = successCount; }
        public Long getFailedCount() { return failedCount; }
        public void setFailedCount(Long failedCount) { this.failedCount = failedCount; }
    }
    
    /**
     * 模板路由统计结果
     */
    class TemplateRoutingStatistics {
        private String templateId;
        private Long totalCount;
        private Long successCount;
        private Double avgExecutionTime;
        
        public TemplateRoutingStatistics() {}
        
        // Getters and Setters
        public String getTemplateId() { return templateId; }
        public void setTemplateId(String templateId) { this.templateId = templateId; }
        public Long getTotalCount() { return totalCount; }
        public void setTotalCount(Long totalCount) { this.totalCount = totalCount; }
        public Long getSuccessCount() { return successCount; }
        public void setSuccessCount(Long successCount) { this.successCount = successCount; }
        public Double getAvgExecutionTime() { return avgExecutionTime; }
        public void setAvgExecutionTime(Double avgExecutionTime) { this.avgExecutionTime = avgExecutionTime; }
        
        public Double getSuccessRate() {
            return totalCount > 0 ? (double) successCount / totalCount : 0.0;
        }
    }
    
    /**
     * 规则匹配统计结果
     */
    class RuleMatchStatistics {
        private String matchedRuleId;
        private String matchedRuleName;
        private Long matchCount;
        private Double avgExecutionTime;
        
        public RuleMatchStatistics() {}
        
        // Getters and Setters
        public String getMatchedRuleId() { return matchedRuleId; }
        public void setMatchedRuleId(String matchedRuleId) { this.matchedRuleId = matchedRuleId; }
        public String getMatchedRuleName() { return matchedRuleName; }
        public void setMatchedRuleName(String matchedRuleName) { this.matchedRuleName = matchedRuleName; }
        public Long getMatchCount() { return matchCount; }
        public void setMatchCount(Long matchCount) { this.matchCount = matchCount; }
        public Double getAvgExecutionTime() { return avgExecutionTime; }
        public void setAvgExecutionTime(Double avgExecutionTime) { this.avgExecutionTime = avgExecutionTime; }
    }
}
