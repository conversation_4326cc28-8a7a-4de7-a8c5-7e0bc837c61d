package com.lubansoft.msg.model.service.processor;

import com.lubansoft.msg.model.model.MessageRequestDTO;
import org.springframework.stereotype.Component;

/**
 * 消息上下文构建器
 */
@Component
public class MessageContextBuilder {
    
    /**
     * 构建消息处理上下文
     * 
     * @param request 消息请求
     * @return 消息上下文
     */
    public MessageContext buildContext(MessageRequestDTO request) {
        MessageContext context = new MessageContext(request);
        return context;
    }
}