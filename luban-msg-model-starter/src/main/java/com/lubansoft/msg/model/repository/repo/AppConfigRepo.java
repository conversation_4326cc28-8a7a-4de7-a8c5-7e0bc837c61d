package com.lubansoft.msg.model.repository.repo;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lubansoft.msg.model.repository.entity.AppConfig;
import com.lubansoft.msg.model.repository.mapper.AppConfigMapper;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 应用配置服务类
 */
@Service
public class AppConfigRepo extends ServiceImpl<AppConfigMapper, AppConfig> {
    
    /**
     * 根据应用名称查找配置
     */
    public Optional<AppConfig> findByAppName(String appName) {
        AppConfig config = getOne(new LambdaQueryWrapper<AppConfig>()
                .eq(AppConfig::getAppName, appName));
        return Optional.ofNullable(config);
    }
    
    /**
     * 根据应用密钥查找配置
     */
    public Optional<AppConfig> findByAppSecret(String appSecret) {
        AppConfig config = getOne(new LambdaQueryWrapper<AppConfig>()
                .eq(AppConfig::getAppSecret, appSecret));
        return Optional.ofNullable(config);
    }
    
    /**
     * 查找所有启用的应用配置
     */
    public List<AppConfig> findEnabledAppsOrderByCreatedAt() {
        return list(new LambdaQueryWrapper<AppConfig>()
                .eq(AppConfig::getEnabled, true)
                .orderByDesc(AppConfig::getCreatedAt));
    }
    
    /**
     * 根据启用状态查找应用配置
     */
    public List<AppConfig> findByEnabledOrderByCreatedAt(Boolean enabled) {
        return list(new LambdaQueryWrapper<AppConfig>()
                .eq(AppConfig::getEnabled, enabled)
                .orderByDesc(AppConfig::getCreatedAt));
    }
    
    /**
     * 查找需要重置配额的应用
     */
    public List<AppConfig> findAppsNeedQuotaReset(LocalDateTime currentTime) {
        return list(new LambdaQueryWrapper<AppConfig>()
                .le(AppConfig::getQuotaResetTime, currentTime)
                .eq(AppConfig::getEnabled, true));
    }
    
    /**
     * 查找配额不足的应用
     */
    public List<AppConfig> findAppsWithInsufficientQuota() {
        return list(new LambdaQueryWrapper<AppConfig>()
                .apply("quota_used >= quota_limit")
                .eq(AppConfig::getEnabled, true));
    }
    
    /**
     * 统计启用的应用数量
     */
    public long countEnabledApps() {
        return count(new LambdaQueryWrapper<AppConfig>()
                .eq(AppConfig::getEnabled, true));
    }
    
    /**
     * 查找指定时间范围内创建的应用配置
     */
    public List<AppConfig> findByCreatedAtBetween(LocalDateTime startTime, LocalDateTime endTime) {
        return list(new LambdaQueryWrapper<AppConfig>()
                .between(AppConfig::getCreatedAt, startTime, endTime)
                .orderByDesc(AppConfig::getCreatedAt));
    }
    
    /**
     * 检查应用名称是否存在（排除指定ID）
     */
    public boolean existsByAppNameAndAppIdNot(String appName, String excludeId) {
        return count(new LambdaQueryWrapper<AppConfig>()
                .eq(AppConfig::getAppName, appName)
                .ne(AppConfig::getAppId, excludeId)) > 0;
    }
    
    /**
     * 检查应用密钥是否存在（排除指定ID）
     */
    public boolean existsByAppSecretAndAppIdNot(String appSecret, String excludeId) {
        return count(new LambdaQueryWrapper<AppConfig>()
                .eq(AppConfig::getAppSecret, appSecret)
                .ne(AppConfig::getAppId, excludeId)) > 0;
    }
    
    /**
     * 更新应用启用状态
     */
    public boolean updateEnabledByAppId(String appId, Boolean enabled) {
        return update(new LambdaUpdateWrapper<AppConfig>()
                .eq(AppConfig::getAppId, appId)
                .set(AppConfig::getEnabled, enabled)
                .set(AppConfig::getUpdatedAt, LocalDateTime.now()));
    }
    
    /**
     * 更新应用配额使用量
     */
    public boolean updateQuotaUsedByAppId(String appId, Integer quotaUsed) {
        return update(new LambdaUpdateWrapper<AppConfig>()
                .eq(AppConfig::getAppId, appId)
                .set(AppConfig::getQuotaUsed, quotaUsed)
                .set(AppConfig::getUpdatedAt, LocalDateTime.now()));
    }
    
    /**
     * 增加应用配额使用量
     */
    public boolean incrementQuotaUsedByAppId(String appId, Integer amount) {
        return update(new LambdaUpdateWrapper<AppConfig>()
                .eq(AppConfig::getAppId, appId)
                .setSql("quota_used = quota_used + " + amount)
                .set(AppConfig::getUpdatedAt, LocalDateTime.now()));
    }
    
    /**
     * 重置应用配额
     */
    public boolean resetQuotaByAppId(String appId, LocalDateTime nextResetTime) {
        return update(new LambdaUpdateWrapper<AppConfig>()
                .eq(AppConfig::getAppId, appId)
                .set(AppConfig::getQuotaUsed, 0)
                .set(AppConfig::getQuotaResetTime, nextResetTime)
                .set(AppConfig::getUpdatedAt, LocalDateTime.now()));
    }
    
    /**
     * 批量重置配额
     */
    public boolean batchResetQuota(List<String> appIds, LocalDateTime nextResetTime) {
        return update(new LambdaUpdateWrapper<AppConfig>()
                .in(AppConfig::getAppId, appIds)
                .set(AppConfig::getQuotaUsed, 0)
                .set(AppConfig::getQuotaResetTime, nextResetTime)
                .set(AppConfig::getUpdatedAt, LocalDateTime.now()));
    }
    
    /**
     * 更新应用配额限制
     */
    public boolean updateQuotaLimitByAppId(String appId, Integer quotaLimit) {
        return update(new LambdaUpdateWrapper<AppConfig>()
                .eq(AppConfig::getAppId, appId)
                .set(AppConfig::getQuotaLimit, quotaLimit)
                .set(AppConfig::getUpdatedAt, LocalDateTime.now()));
    }
    
    /**
     * 更新应用密钥
     */
    public boolean updateAppSecretByAppId(String appId, String appSecret) {
        return update(new LambdaUpdateWrapper<AppConfig>()
                .eq(AppConfig::getAppId, appId)
                .set(AppConfig::getAppSecret, appSecret)
                .set(AppConfig::getUpdatedAt, LocalDateTime.now()));
    }
    
    /**
     * 删除指定时间之前的禁用应用配置
     */
    public boolean deleteDisabledAppsBeforeTime(LocalDateTime beforeTime) {
        return remove(new LambdaQueryWrapper<AppConfig>()
                .eq(AppConfig::getEnabled, false)
                .lt(AppConfig::getUpdatedAt, beforeTime));
    }
}