package com.lubansoft.msg.model.service.consumer;

import com.lubansoft.msg.model.model.ChannelMessageRequestDTO;
import com.lubansoft.msg.model.model.MessageReceiverDTO;
import com.lubansoft.msg.model.model.MessageRequestDTO;
import com.lubansoft.msg.model.repository.entity.ChannelConfig;
import com.lubansoft.msg.model.repository.entity.ChannelConfigData;
import com.lubansoft.msg.model.service.ChannelConfigService;
import com.lubansoft.msg.model.service.TemplateChannelMappingService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClient;

import java.util.List;
import java.util.Map;

/**
 * 消息渠道处理器
 * 负责将消息通过具体的渠道发送出去
 */
@Component
public class MessageChannelHandler {

    private static final Logger logger = LoggerFactory.getLogger(MessageChannelHandler.class);

    @Autowired
    private ChannelConfigService channelConfigService;

    @Autowired
    private WebClient webClient;

    @Autowired
    private TemplateChannelMappingService templateChannelMappingService;

    /**
     * 处理消息发送到指定渠道
     *
     * @param messageRequest 消息请求
     * @param channel        渠道名称
     * @param receivers      指定的接收者列表
     */
    public void handleMessage(MessageRequestDTO messageRequest, String channel, List<MessageReceiverDTO> receivers) {
        // 获取渠道配置
        List<ChannelConfig> channelConfigs = channelConfigService.getChannelConfigsByType(channel);
        if (channelConfigs == null || channelConfigs.isEmpty()) {
            logger.error("未找到渠道配置: messageId={}, channel={}",
                    messageRequest.getMessageId(), channel);
            throw new RuntimeException("未找到渠道配置: " + channel);
        }

        // 使用第一个可用的渠道配置
        ChannelConfig channelConfig = channelConfigs.get(0);
        ChannelConfigData configData = channelConfig.getConfigData();

        if (configData == null) {
            logger.error("渠道配置数据为空: messageId={}, channel={}",
                    messageRequest.getMessageId(), channel);
            throw new RuntimeException("渠道配置数据为空: " + channel);
        }

        // 准备发送的数据
        ChannelMessageRequestDTO channelMessageRequestDTO = new ChannelMessageRequestDTO();
        channelMessageRequestDTO.setRequestId(messageRequest.getRequestId());
        channelMessageRequestDTO.setTemplateId(messageRequest.getTemplateId());
        channelMessageRequestDTO.setReceivers(receivers);
        channelMessageRequestDTO.setCallBackToken(channel);

        // 应用模板参数转换逻辑
        ChannelParameterResult parameterResult = templateChannelMappingService.applyChannelParameterMapping(messageRequest.getTemplateId(), channel, messageRequest.getGlobalParam());

        // 设置转换后的模板参数和全局参数
        channelMessageRequestDTO.setGlobalParam(parameterResult.getGlobalParam());
        channelMessageRequestDTO.setTemplateParam(parameterResult.getTemplateParam());

        // 发送消息
        sendToChannel(channelMessageRequestDTO, configData);
    }

    /**
     * 发送消息到渠道
     *
     * @param messageRequest 消息请求
     * @param configData     渠道配置数据
     */
    private void sendToChannel(ChannelMessageRequestDTO messageRequest, ChannelConfigData configData) {
        sendUnifiedMessage(configData.getBaseUrl() + configData.getMsgApi(), messageRequest);
    }

    /**
     * 统一发送消息方法
     */
    private void sendUnifiedMessage(String url, ChannelMessageRequestDTO messageRequest) {
        logger.info("发送统一消息: url={}, messageId={}", url, messageRequest.getRequestId());

        try {
            // 发送POST请求，直接使用对象而不是序列化后的字符串
            String response = webClient.post()
                    .uri(url)
                    .contentType(MediaType.APPLICATION_JSON)
                    .bodyValue(messageRequest)
                    .retrieve()
                    .bodyToMono(String.class)
                    .block();

            logger.info("渠道响应: requestId={}, response={}",
                    messageRequest.getRequestId(), response);
        } catch (Exception e) {
            throw new RuntimeException("统一消息发送失败: " + e.getMessage(), e);
        }
    }

}