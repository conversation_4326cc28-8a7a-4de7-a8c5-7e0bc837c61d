package com.lubansoft.msg.model.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lubansoft.msg.model.repository.entity.ReceiverDnd;

import java.util.List;

/**
 * 用户免打扰设置服务接口
 */
public interface ReceiverDndService {

    /**
     * 保存免打扰设置
     * @param receiverDnd 免打扰设置实体
     * @return 保存后的实体
     */
    ReceiverDnd save(ReceiverDnd receiverDnd);

    /**
     * 根据ID更新免打扰设置
     * @param receiverDnd 免打扰设置实体
     * @return 更新后的实体
     */
    ReceiverDnd update(ReceiverDnd receiverDnd);

    /**
     * 根据ID删除免打扰设置
     * @param id 主键ID
     * @return 是否删除成功
     */
    boolean deleteById(Long id);

    /**
     * 根据ID查询免打扰设置
     * @param id 主键ID
     * @return 免打扰设置实体
     */
    ReceiverDnd findById(Long id);

    /**
     * 根据用户ID查询免打扰设置列表
     * @param receiverId 用户ID
     * @return 免打扰设置列表
     */
    List<ReceiverDnd> findByReceiverId(String receiverId);

    /**
     * 分页查询免打扰设置
     * @param page 分页参数
     * @param receiverId 用户ID（可选）
     * @return 分页结果
     */
    IPage<ReceiverDnd> findByPage(Page<ReceiverDnd> page, String receiverId);
}