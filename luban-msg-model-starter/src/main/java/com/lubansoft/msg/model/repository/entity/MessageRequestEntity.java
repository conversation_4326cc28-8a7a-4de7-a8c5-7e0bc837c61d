package com.lubansoft.msg.model.repository.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.lubansoft.msg.common.util.JsonUtils;
import com.lubansoft.msg.model.model.MessageRequestDTO;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 消息请求实体（对应message_request表）
 */
@Data
@TableName("message_request")
public class MessageRequestEntity {
    
    /**
     * 消息ID（主键）
     */
    @TableId(value = "id")
    private Long id;
    
    /**
     * 请求ID
     */
    @NotBlank(message = "请求ID不能为空")
    @TableField("request_id")
    private String requestId;
    
    /**
     * 请求参数（JSON格式存储完整的MessageRequestDTO）
     */
    @NotNull(message = "请求参数不能为空")
    @TableField("request_param")
    private String requestParam;
    
    /**
     * 创建时间
     */
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;
    
    /**
     * 请求状态
     */
    @NotBlank(message = "请求状态不能为空")
    @TableField("status")
    private String status;
    
    // 构造函数
    public MessageRequestEntity() {}
    
    public MessageRequestEntity(String requestId, MessageRequestDTO requestDTO, String status) {
        this.requestId = requestId;
        this.requestParam = JsonUtils.toJson(requestDTO);
        this.status = status;
    }
    
    /**
     * 获取请求参数对象
     * 
     * @return MessageRequestDTO对象
     */
    public MessageRequestDTO getRequestDTO() {
        if (requestParam == null || requestParam.trim().isEmpty()) {
            return null;
        }
        return JsonUtils.fromJson(requestParam, MessageRequestDTO.class);
    }
    
    /**
     * 设置请求参数对象
     * 
     * @param requestDTO MessageRequestDTO对象
     */
    public void setRequestDTO(MessageRequestDTO requestDTO) {
        this.requestParam = JsonUtils.toJson(requestDTO);
    }
    
    /**
     * 请求状态枚举
     */
    public enum RequestStatus {
        PENDING("PENDING", "待处理"),
        PROCESSING("PROCESSING", "处理中"),
        COMPLETED("COMPLETED", "已完成"),
        FAILED("FAILED", "处理失败"),
        CANCELLED("CANCELLED", "已取消");
        
        private final String code;
        private final String description;
        
        RequestStatus(String code, String description) {
            this.code = code;
            this.description = description;
        }
        
        public String getCode() {
            return code;
        }
        
        public String getDescription() {
            return description;
        }
        
        public static RequestStatus fromCode(String code) {
            for (RequestStatus status : values()) {
                if (status.code.equals(code)) {
                    return status;
                }
            }
            throw new IllegalArgumentException("Unknown request status code: " + code);
        }
    }
}
