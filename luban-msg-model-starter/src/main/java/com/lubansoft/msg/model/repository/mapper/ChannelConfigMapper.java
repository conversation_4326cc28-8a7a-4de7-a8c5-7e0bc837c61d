package com.lubansoft.msg.model.repository.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lubansoft.msg.common.model.ChannelType;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import com.lubansoft.msg.model.repository.entity.ChannelConfig;

import java.util.List;

/**
 * 渠道配置Repository
 */
@Mapper
public interface ChannelConfigMapper extends BaseMapper<ChannelConfig> {
    
}