package com.lubansoft.msg.model.controller.system.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class RegisterParam implements Serializable {
	private static final long serialVersionUID = 1L;

    @Schema(description = "渠道名称")
    private String channelName;

    @Schema(description = "渠道类型")
    @NotBlank(message = "类型不能为空")
    private String channelType;

    @Schema(description = "api路径")
    @NotBlank(message = "api路径不能为空")
    private String baseUrl;

    @Schema(description = "检查API")
    @NotBlank(message = "检查API")
    private String healthApi;

    @Schema(description = "发信接口")
    @NotBlank(message = "发信接口")
    private String msgApi;

    @Schema(description = "发信接口额外参数说明")
    private String msgApiExtraParamSchema;
}