package com.lubansoft.msg.model.repository.entity;

import com.baomidou.mybatisplus.annotation.*;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

import java.time.LocalDateTime;

/**
 * 应用配置实体
 */
@TableName("app_config")
public class AppConfig {
    
    /**
     * 应用ID
     */
    @TableId(value = "app_id", type = IdType.ASSIGN_ID)
    private String appId;
    
    /**
     * 应用名称
     */
    @NotBlank(message = "应用名称不能为空")
    @Size(max = 255, message = "应用名称长度不能超过255个字符")
    @TableField("app_name")
    private String appName;
    
    /**
     * 应用密钥
     */
    @NotBlank(message = "应用密钥不能为空")
    @Size(max = 255, message = "应用密钥长度不能超过255个字符")
    @TableField("app_secret")
    private String appSecret;
    
    /**
     * 配额限制
     */
    @Min(value = 0, message = "配额限制不能小于0")
    @TableField("quota_limit")
    private Integer quotaLimit = 1000;
    
    /**
     * 已使用配额
     */
    @Min(value = 0, message = "已使用配额不能小于0")
    @TableField("quota_used")
    private Integer quotaUsed = 0;
    
    /**
     * 配额重置时间
     */
    @TableField("quota_reset_time")
    private LocalDateTime quotaResetTime;
    
    /**
     * 是否启用
     */
    @TableField("enabled")
    private Boolean enabled = true;
    
    /**
     * 创建时间
     */
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;
    
    // Constructors
    public AppConfig() {}
    
    public AppConfig(String appId, String appName, String appSecret) {
        this.appId = appId;
        this.appName = appName;
        this.appSecret = appSecret;
    }
    
    /**
     * 检查配额是否足够
     */
    public boolean hasQuota(int required) {
        return quotaUsed + required <= quotaLimit;
    }
    
    /**
     * 消费配额
     */
    public boolean consumeQuota(int amount) {
        if (hasQuota(amount)) {
            this.quotaUsed += amount;
            return true;
        }
        return false;
    }
    
    /**
     * 重置配额
     */
    public void resetQuota() {
        this.quotaUsed = 0;
        this.quotaResetTime = LocalDateTime.now().plusDays(1).withHour(0).withMinute(0).withSecond(0).withNano(0);
    }
    
    /**
     * 检查是否需要重置配额
     */
    public boolean needResetQuota() {
        return quotaResetTime != null && LocalDateTime.now().isAfter(quotaResetTime);
    }
    
    // Getters and Setters
    public String getAppId() {
        return appId;
    }
    
    public void setAppId(String appId) {
        this.appId = appId;
    }
    
    public String getAppName() {
        return appName;
    }
    
    public void setAppName(String appName) {
        this.appName = appName;
    }
    
    public String getAppSecret() {
        return appSecret;
    }
    
    public void setAppSecret(String appSecret) {
        this.appSecret = appSecret;
    }
    
    public Integer getQuotaLimit() {
        return quotaLimit;
    }
    
    public void setQuotaLimit(Integer quotaLimit) {
        this.quotaLimit = quotaLimit;
    }
    
    public Integer getQuotaUsed() {
        return quotaUsed;
    }
    
    public void setQuotaUsed(Integer quotaUsed) {
        this.quotaUsed = quotaUsed;
    }
    
    public LocalDateTime getQuotaResetTime() {
        return quotaResetTime;
    }
    
    public void setQuotaResetTime(LocalDateTime quotaResetTime) {
        this.quotaResetTime = quotaResetTime;
    }
    
    public Boolean getEnabled() {
        return enabled;
    }
    
    public void setEnabled(Boolean enabled) {
        this.enabled = enabled;
    }
    
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
    
    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }
    
    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }
    
    @Override
    public String toString() {
        return "AppConfig{" +
                "appId='" + appId + '\'' +
                ", appName='" + appName + '\'' +
                ", appSecret='***'" + // 隐藏密钥
                ", quotaLimit=" + quotaLimit +
                ", quotaUsed=" + quotaUsed +
                ", quotaResetTime=" + quotaResetTime +
                ", enabled=" + enabled +
                ", createdAt=" + createdAt +
                ", updatedAt=" + updatedAt +
                '}';
    }
}