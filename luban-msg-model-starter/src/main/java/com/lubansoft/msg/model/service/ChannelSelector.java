package com.lubansoft.msg.model.service;

import com.lubansoft.msg.common.model.RoutingResult;
import com.lubansoft.msg.model.model.MessageRequestDTO;
import com.lubansoft.msg.model.repository.entity.ChannelConfig;
import com.lubansoft.msg.model.repository.entity.ChannelConfigData;
import com.lubansoft.msg.model.service.routing.RoutingContext;
import com.lubansoft.msg.model.service.routing.RoutingEngineManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClient;

import java.time.Duration;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 渠道选择器
 * 在队列消费端根据消息内容和路由规则动态选择发送渠道
 */
@Component
public class ChannelSelector {
    
    private static final Logger logger = LoggerFactory.getLogger(ChannelSelector.class);
    
    @Autowired
    private RoutingEngineManager routingEngineManager;
    
    @Autowired
    private ChannelConfigService channelConfigService;
    
    @Autowired
    private WebClient webClient;
    
    /**
     * 根据消息内容选择发送渠道
     * 
     * @param messageRequest 消息请求
     * @param messageProperties 消息属性
     * @return 选择的渠道列表
     */
    public List<String> selectChannels(MessageRequestDTO messageRequest, Map<String, Object> messageProperties) {
        logger.debug("开始选择发送渠道: messageId={}", messageRequest.getMessageId());
        
        try {
            // 1. 首先检查消息属性中是否已包含目标渠道
            List<String> preSelectedChannels = extractPreSelectedChannels(messageProperties);
            if (preSelectedChannels != null && !preSelectedChannels.isEmpty()) {
                logger.debug("使用预选择的渠道: messageId={}, channels={}", 
                           messageRequest.getMessageId(), preSelectedChannels);
                return preSelectedChannels;
            }
            
            // 2. 使用路由引擎进行渠道选择
            List<String> routedChannels = routeChannels(messageRequest, messageProperties);
            if (routedChannels != null && !routedChannels.isEmpty()) {
                logger.debug("路由选择的渠道: messageId={}, channels={}", 
                           messageRequest.getMessageId(), routedChannels);
                return routedChannels;
            }
            
            // 3. 使用默认渠道选择策略
        } catch (Exception e) {
            logger.error("渠道选择异常: messageId={}, error={}", 
                        messageRequest.getMessageId(), e.getMessage(), e);
            
            // 异常情况下返回默认渠道
        }
        return null;
    }
    
    /**
     * 从消息属性中提取预选择的渠道
     */
    @SuppressWarnings("unchecked")
    private List<String> extractPreSelectedChannels(Map<String, Object> messageProperties) {
        if (messageProperties == null) {
            return null;
        }
        
        // 检查targetChannels属性
        Object targetChannels = messageProperties.get("targetChannel");
        if (targetChannels instanceof List) {
            List<?> channelList = (List<?>) targetChannels;
            List<String> result = new ArrayList<>();
            for (Object channel : channelList) {
                if (channel instanceof String) {
                    result.add((String) channel);
                }
            }
            return result.isEmpty() ? null : result;
        }
        
        // 检查单个渠道
        Object singleChannel = messageProperties.get("channel");
        if (singleChannel instanceof String) {
            List<String> result = new ArrayList<>();
            result.add((String) singleChannel);
            return result;
        }
        
        return null;
    }
    
    /**
     * 使用路由引擎进行渠道选择
     */
    private List<String> routeChannels(MessageRequestDTO messageRequest, Map<String, Object> messageProperties) {
        try {
            // 构建路由上下文
            RoutingContext context = new RoutingContext();
            
            // 从消息属性中提取上下文信息
            if (messageProperties != null) {
                Object appId = messageProperties.get("appId");
                if (appId instanceof String) {
                    context.setAttribute("appId", (String) appId);
                }
                
                Object requestId = messageProperties.get("requestId");
                if (requestId instanceof String) {
                    context.setAttribute("requestId", (String) requestId);
                }
            }
            
            // 执行路由
            RoutingResult routingResult = routingEngineManager.route(messageRequest, context);
            
            if (routingResult.isMatched()) {
                return routingResult.getTargetChannels();
            }
            
            logger.debug("路由引擎未匹配到渠道: messageId={}, reason={}", 
                       messageRequest.getMessageId(), routingResult.getErrorMessage());
            return null;
            
        } catch (Exception e) {
            logger.error("路由引擎执行异常: messageId={}, error={}", 
                        messageRequest.getMessageId(), e.getMessage(), e);
            return null;
        }
    }
    
    /**
     * 获取紧急情况下的渠道
     */
    private List<String> getEmergencyChannels(MessageRequestDTO messageRequest) {
        List<String> emergencyChannels = new ArrayList<>();
        emergencyChannels.add("push"); // 紧急情况下使用推送
        
        logger.info("使用紧急渠道: messageId={}, channels={}", 
                   messageRequest.getMessageId(), emergencyChannels);
        
        return emergencyChannels;
    }
    
    /**
     * 验证渠道是否可用
     * 
     * @param channels 渠道列表
     * @return 可用的渠道列表
     */
    public List<String> validateChannels(List<String> channels) {
        if (channels == null || channels.isEmpty()) {
            return new ArrayList<>();
        }
        
        List<String> validChannels = new ArrayList<>();
        
        for (String channel : channels) {
            if (isChannelAvailable(channel)) {
                validChannels.add(channel);
            } else {
                logger.info("渠道不可用，已跳过: channel={}", channel);
            }
        }
        
        return validChannels;
    }
    
    /**
     * 检查渠道是否可用
     */
    private boolean isChannelAvailable(String channel) {
        if (channel == null || channel.trim().isEmpty()) {
            return false;
        }
        
        // 通过ChannelConfigService获取渠道配置并检查健康状态
        try {
            List<ChannelConfig> channelConfigs = channelConfigService.getChannelConfigsByType(channel);
            if (channelConfigs == null || channelConfigs.isEmpty()) {
                logger.warn("未找到渠道配置: channel={}", channel);
                return false;
            }
            
            ChannelConfig channelConfig = channelConfigs.get(0);
            if (channelConfig.getEnabled() == null || !channelConfig.getEnabled()) {
                logger.warn("渠道未启用: channel={}", channel);
                return false;
            }
            
            ChannelConfigData configData = channelConfig.getConfigData();
            if (configData == null || configData.getHealthApi() == null) {
                logger.warn("渠道缺少健康检查配置: channel={}", channel);
                return false;
            }
            
            // 构造健康检查URL
            String baseUrl = configData.getBaseUrl();
            String healthApi = configData.getHealthApi();
            String url = baseUrl;
            if (!baseUrl.endsWith("/") && !healthApi.startsWith("/")) {
                url += "/";
            }
            url += healthApi;
            
            // 调用健康检查接口，设置超时时间
            String response = webClient.get()
                    .uri(url)
                    .retrieve()
                    .bodyToMono(String.class)
                    .timeout(Duration.ofSeconds(5)) // 设置5秒超时
                    .block();
            
            logger.debug("渠道健康检查成功: channel={}, url={}, response={}", channel, url, response);
            return true;
        } catch (Exception e) {
            logger.error("渠道健康检查失败: channel={}, {}", channel, e.getMessage());
            return false;
        }
    }
    
    /**
     * 获取渠道选择统计信息
     */
    public Map<String, Object> getChannelSelectionStatistics() {
        // 这里可以添加渠道选择的统计信息
        // 比如各渠道的使用频率、成功率等
        return Map.of(
            "supportedChannels", List.of("sms", "email", "push", "wechat", "dingtalk"),
            "defaultChannel", "push",
            "emergencyChannel", "push"
        );
    }
}