package com.lubansoft.msg.model.repository.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lubansoft.msg.model.repository.entity.TemplateChannelMapping;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 模板渠道映射配置Mapper接口
 */
@Mapper
public interface TemplateChannelMappingMapper extends BaseMapper<TemplateChannelMapping> {
    
    /**
     * 根据模板ID查找所有渠道映射
     *
     * @param templateId 模板ID
     * @return 渠道映射列表
     */
    @Select("SELECT * FROM template_channel_mapping " +
            "WHERE template_id = #{templateId} " +
            "ORDER BY created_at ASC")
    List<TemplateChannelMapping> findByTemplateId(@Param("templateId") String templateId);
    
    /**
     * 根据渠道类型查找所有模板映射
     *
     * @param channelType 渠道类型
     * @return 模板映射列表
     */
    @Select("SELECT * FROM template_channel_mapping " +
            "WHERE channel_type = #{channelType} " +
            "ORDER BY created_at ASC")
    List<TemplateChannelMapping> findByChannelType(@Param("channelType") String channelType);
    
    /**
     * 根据模板ID和渠道类型查找特定的映射配置
     *
     * @param templateId 模板ID
     * @param channelType 渠道类型
     * @return 映射配置
     */
    @Select("SELECT * FROM template_channel_mapping " +
            "WHERE template_id = #{templateId} AND channel_type = #{channelType} " +
            "LIMIT 1")
    TemplateChannelMapping findByTemplateIdAndChannelType(@Param("templateId") String templateId,
                                                          @Param("channelType") String channelType);
    

    
    /**
     * 查找所有启用的映射配置，分页查询
     * 
     * @param page 分页参数
     * @return 分页结果
     */
    @Select("SELECT * FROM template_channel_mapping " +
            "WHERE enabled = true AND deleted = 0 " +
            "ORDER BY priority ASC, created_at DESC")
    IPage<TemplateChannelMapping> findAllEnabledWithPaging(Page<TemplateChannelMapping> page);
    
    /**
     * 根据启用状态查找映射配置，分页查询
     * 
     * @param page 分页参数
     * @param enabled 启用状态
     * @return 分页结果
     */
    @Select("SELECT * FROM template_channel_mapping " +
            "WHERE enabled = #{enabled} AND deleted = 0 " +
            "ORDER BY priority ASC, created_at DESC")
    IPage<TemplateChannelMapping> findByEnabledWithPaging(Page<TemplateChannelMapping> page, 
                                                          @Param("enabled") Boolean enabled);
    
    /**
     * 根据映射名称模糊查询
     * 
     * @param mappingName 映射名称关键词
     * @return 映射配置列表
     */
    @Select("SELECT * FROM template_channel_mapping " +
            "WHERE mapping_name LIKE CONCAT('%', #{mappingName}, '%') AND deleted = 0 " +
            "ORDER BY priority ASC, created_at DESC")
    List<TemplateChannelMapping> findByMappingNameContaining(@Param("mappingName") String mappingName);
    
    /**
     * 统计某个模板的渠道映射数量
     * 
     * @param templateId 模板ID
     * @return 映射数量
     */
    @Select("SELECT COUNT(*) FROM template_channel_mapping " +
            "WHERE template_id = #{templateId} AND deleted = 0")
    Integer countByTemplateId(@Param("templateId") String templateId);
    
    /**
     * 统计某个渠道的模板映射数量
     * 
     * @param channelId 渠道ID
     * @return 映射数量
     */
    @Select("SELECT COUNT(*) FROM template_channel_mapping " +
            "WHERE channel_id = #{channelId} AND deleted = 0")
    Integer countByChannelId(@Param("channelId") String channelId);
    
    /**
     * 统计启用的映射配置数量
     * 
     * @return 启用的映射数量
     */
    @Select("SELECT COUNT(*) FROM template_channel_mapping " +
            "WHERE enabled = true AND deleted = 0")
    Integer countEnabledMappings();
    
    /**
     * 批量更新映射配置的启用状态
     * 
     * @param mappingIds 映射ID列表
     * @param enabled 启用状态
     * @param updatedBy 更新者
     * @return 更新的记录数
     */
    @Update("<script>" +
            "UPDATE template_channel_mapping SET " +
            "enabled = #{enabled}, " +
            "updated_by = #{updatedBy}, " +
            "updated_at = CURRENT_TIMESTAMP " +
            "WHERE mapping_id IN " +
            "<foreach collection='mappingIds' item='id' open='(' separator=',' close=')'>" +
            "#{id}" +
            "</foreach>" +
            " AND deleted = 0" +
            "</script>")
    Integer batchUpdateEnabled(@Param("mappingIds") List<String> mappingIds, 
                              @Param("enabled") Boolean enabled, 
                              @Param("updatedBy") String updatedBy);
    
    /**
     * 批量更新映射配置的优先级
     * 
     * @param mappingIds 映射ID列表
     * @param priority 优先级
     * @param updatedBy 更新者
     * @return 更新的记录数
     */
    @Update("<script>" +
            "UPDATE template_channel_mapping SET " +
            "priority = #{priority}, " +
            "updated_by = #{updatedBy}, " +
            "updated_at = CURRENT_TIMESTAMP " +
            "WHERE mapping_id IN " +
            "<foreach collection='mappingIds' item='id' open='(' separator=',' close=')'>" +
            "#{id}" +
            "</foreach>" +
            " AND deleted = 0" +
            "</script>")
    Integer batchUpdatePriority(@Param("mappingIds") List<String> mappingIds, 
                               @Param("priority") Integer priority, 
                               @Param("updatedBy") String updatedBy);
    
    /**
     * 根据模板ID删除所有相关的映射配置（逻辑删除）
     * 
     * @param templateId 模板ID
     * @param updatedBy 更新者
     * @return 删除的记录数
     */
    @Update("UPDATE template_channel_mapping SET " +
            "deleted = 1, " +
            "updated_by = #{updatedBy}, " +
            "updated_at = CURRENT_TIMESTAMP " +
            "WHERE template_id = #{templateId} AND deleted = 0")
    Integer deleteByTemplateId(@Param("templateId") String templateId, 
                              @Param("updatedBy") String updatedBy);
    
    /**
     * 根据渠道ID删除所有相关的映射配置（逻辑删除）
     * 
     * @param channelId 渠道ID
     * @param updatedBy 更新者
     * @return 删除的记录数
     */
    @Update("UPDATE template_channel_mapping SET " +
            "deleted = 1, " +
            "updated_by = #{updatedBy}, " +
            "updated_at = CURRENT_TIMESTAMP " +
            "WHERE channel_id = #{channelId} AND deleted = 0")
    Integer deleteByChannelId(@Param("channelId") String channelId, 
                             @Param("updatedBy") String updatedBy);
    
    /**
     * 检查模板和渠道的映射是否已存在
     * 
     * @param templateId 模板ID
     * @param channelId 渠道ID
     * @return 是否存在
     */
    @Select("SELECT COUNT(*) > 0 FROM template_channel_mapping " +
            "WHERE template_id = #{templateId} AND channel_id = #{channelId} AND deleted = 0")
    Boolean existsByTemplateIdAndChannelId(@Param("templateId") String templateId, 
                                          @Param("channelId") String channelId);
    
    /**
     * 获取某个模板的最大优先级值
     * 
     * @param templateId 模板ID
     * @return 最大优先级值
     */
    @Select("SELECT COALESCE(MAX(priority), 0) FROM template_channel_mapping " +
            "WHERE template_id = #{templateId} AND deleted = 0")
    Integer getMaxPriorityByTemplateId(@Param("templateId") String templateId);
}
