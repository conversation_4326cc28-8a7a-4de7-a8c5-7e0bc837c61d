package com.lubansoft.msg.model.service.producer;

import com.lubansoft.msg.common.model.MessageReceiver;
import com.lubansoft.msg.common.util.IdGenerator;
import com.lubansoft.msg.common.util.JsonUtils;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.core.MessageProperties;
import org.springframework.amqp.rabbit.connection.CorrelationData;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.*;

/**
 * RabbitMQ消息生产者实现
 */
@Component
public class RabbitMessageProducer implements MessageProducer {
    
    private static final Logger logger = LoggerFactory.getLogger(RabbitMessageProducer.class);
    
    @Autowired
    private RabbitTemplate rabbitTemplate;
    
    private final ExecutorService executorService = Executors.newFixedThreadPool(10);
    private final Map<String, SendResult> pendingConfirms = new ConcurrentHashMap<>();
    private final Map<String, ProducerStatistics> statisticsMap = new ConcurrentHashMap<>();
    
    @PostConstruct
    public void initialize() {
        logger.info("初始化RabbitMQ消息生产者");
        
        // 设置确认回调
        rabbitTemplate.setConfirmCallback((correlationData, ack, cause) -> {
            if (correlationData != null) {
                String messageId = correlationData.getId();
                handleConfirmation(messageId, ack, cause);
//                logger.info("收到消息确认: messageId={}, ack={}, cause={}", messageId, ack, cause);
            }
        });
        
        // 设置返回回调
        rabbitTemplate.setReturnsCallback(returned -> {
            String messageId = returned.getMessage().getMessageProperties().getMessageId();
            if (messageId != null) {
                handleReturn(messageId, returned.getReplyText());
                logger.info("消息被返回: messageId={}, replyText={}, exchange={}, routingKey={}",
                           messageId, returned.getReplyText(), returned.getExchange(), returned.getRoutingKey());
            }
        });
        
        logger.info("RabbitMQ消息生产者初始化完成");
    }
    
    @Override
    public SendResult sendMessage(String exchangeName, String routingKey, Object message,
                                 List<String> targetChannels,
                                 Map<String, List<MessageReceiver>> channelReceivers,
                                 Map<String, Object> properties) {
        // 确定发送方式
        boolean hasTargetChannels = targetChannels != null && !targetChannels.isEmpty();
        boolean hasChannelReceivers = channelReceivers != null && !channelReceivers.isEmpty();
        
        // 记录日志
        if (hasChannelReceivers) {
            logger.debug("发送消息（按渠道分组）: exchangeName={}, routingKey={}, channels={}", 
                        exchangeName, routingKey, channelReceivers.keySet());
        } else if (hasTargetChannels) {
            logger.info("发送消息到交换机: exchangeName={}, targetChannels={}", 
                       exchangeName, targetChannels);
        } else {
            logger.info("发送消息到交换机: exchangeName={}", exchangeName);
        }
        
        long startTime = System.currentTimeMillis();
        String messageId = IdGenerator.generateId();
        
        try {
            // 添加渠道信息到属性中
            if (hasChannelReceivers) {
                properties.put("channelReceivers", channelReceivers);
            } else if (hasTargetChannels) {
                properties.put("targetChannel", targetChannels);
            } else {
                properties.put("targetChannel", new ArrayList<>());
            }
            
            // 构建消息
            Message rabbitMessage = buildMessage(message, messageId, properties);
            
            // 创建关联数据
            CorrelationData correlationData = new CorrelationData(messageId);
            
            // 发送消息到fanout交换机，路由键可以为空或任意值
            rabbitTemplate.convertAndSend(exchangeName, routingKey, rabbitMessage, correlationData);
            
            // 等待确认
            return waitForConfirmation(messageId, startTime);
            
        } catch (Exception e) {
            logger.error("发送消息异常: exchangeName={}, routingKey={}", exchangeName, routingKey, e);
            SendResult result = SendResult.failure("SEND_ERROR", "发送消息异常: " + e.getMessage());
            result.setExecutionTimeMs(System.currentTimeMillis() - startTime);
            return result;
        }
    }
    
    public SendResult sendMessage(String exchangeName, String routingKey, Object message, Map<String, Object> properties) {
        return sendMessage(exchangeName, routingKey, message, null, null, properties);
    }
    
    public SendResult sendMessage(String exchangeName, String routingKey, Object message, 
                                 Map<String, List<MessageReceiver>> channelReceivers, 
                                 Map<String, Object> properties) {
        return sendMessage(exchangeName, routingKey, message, null, channelReceivers, properties);
    }
    
    @Override
    public SendResult sendDelayMessage(String exchangeName, String routingKey, Object message, 
                                      long delayMillis, Map<String, Object> properties) {
        logger.debug("发送延时消息: exchangeName={}, routingKey={}, delayMillis={}", 
                    exchangeName, routingKey, delayMillis);
        
        // 设置延时属性
        if (properties == null) {
            properties = new ConcurrentHashMap<>();
        }
        properties.put("x-delay", delayMillis);
        
        return sendMessage(exchangeName, routingKey, message, properties);
    }
    
    @Override
    public SendResult sendDelayMessage(String exchangeName, String routingKey, Object message, 
                                      Map<String, List<MessageReceiver>> channelReceivers,
                                      long delayMillis, Map<String, Object> properties) {
        logger.debug("发送延时消息（按渠道分组）: exchangeName={}, routingKey={}, delayMillis={}, channels={}", 
                    exchangeName, routingKey, delayMillis, channelReceivers.keySet());
        
        // 设置延时属性
        if (properties == null) {
            properties = new ConcurrentHashMap<>();
        }
        properties.put("x-delay", delayMillis);
        
        return sendMessage(exchangeName, routingKey, message, channelReceivers, properties);
    }
    
    @Override
    public SendResult sendScheduledMessage(String exchangeName, String routingKey, Object message, 
                                          LocalDateTime scheduleTime, Map<String, Object> properties) {
        logger.debug("发送定时消息: exchangeName={}, routingKey={}, scheduleTime={}", 
                    exchangeName, routingKey, scheduleTime);
        
        // 计算延时时间
        long delayMillis = Duration.between(LocalDateTime.now(), scheduleTime).toMillis();
        
        if (delayMillis <= 0) {
            logger.info("定时时间已过期，立即发送: scheduleTime={}", scheduleTime);
            return sendMessage(exchangeName, routingKey, message, properties);
        }
        
        return sendDelayMessage(exchangeName, routingKey, message, delayMillis, properties);
    }
    
    @Override
    public List<SendResult> sendBatchMessages(List<MessageTask> messages) {
        logger.debug("批量发送消息: count={}", messages.size());
        
        List<CompletableFuture<SendResult>> futures = new ArrayList<>();
        
        // 异步发送每个消息
        for (MessageTask task : messages) {
            CompletableFuture<SendResult> future = CompletableFuture.supplyAsync(() -> {
                if (task.getDelayMillis() != null) {
                    return sendDelayMessage(task.getExchangeName(), task.getRoutingKey(), 
                                          task.getMessage(), task.getDelayMillis(), task.getProperties());
                } else if (task.getScheduleTime() != null) {
                    return sendScheduledMessage(task.getExchangeName(), task.getRoutingKey(), 
                                              task.getMessage(), task.getScheduleTime(), task.getProperties());
                } else {
                    return sendMessage(task.getExchangeName(), task.getRoutingKey(), 
                                     task.getMessage(), task.getProperties());
                }
            }, executorService);
            
            futures.add(future);
        }
        
        // 等待所有消息发送完成
        List<SendResult> results = new ArrayList<>();
        for (CompletableFuture<SendResult> future : futures) {
            try {
                results.add(future.get(30, TimeUnit.SECONDS));
            } catch (Exception e) {
                logger.error("批量发送消息异常", e);
                results.add(SendResult.failure("BATCH_SEND_ERROR", "批量发送异常: " + e.getMessage()));
            }
        }
        
        long successCount = results.stream().mapToLong(r -> r.isSuccess() ? 1 : 0).sum();
        logger.debug("批量发送消息完成: total={}, success={}", results.size(), successCount);
        
        return results;
    }
    
    @Override
    public SendResult sendTransactionalMessage(String exchangeName, String routingKey, Object message, 
                                              Map<String, Object> properties, TransactionCallback transactionCallback) {
        logger.debug("发送事务消息: exchangeName={}, routingKey={}", exchangeName, routingKey);
        
        long startTime = System.currentTimeMillis();
        
        try {
            // 执行事务回调
            boolean transactionSuccess = transactionCallback.execute();
            
            if (transactionSuccess) {
                // 事务成功，发送消息
                return sendMessage(exchangeName, routingKey, message, properties);
            } else {
                // 事务失败，不发送消息
                SendResult result = SendResult.failure("TRANSACTION_FAILED", "事务执行失败");
                result.setExecutionTimeMs(System.currentTimeMillis() - startTime);
                return result;
            }
            
        } catch (Exception e) {
            logger.error("发送事务消息异常: exchangeName={}, routingKey={}", exchangeName, routingKey, e);
            
            SendResult result = SendResult.failure("TRANSACTION_ERROR", "事务消息异常: " + e.getMessage());
            result.setExecutionTimeMs(System.currentTimeMillis() - startTime);
            return result;
        }
    }
    
    /**
     * 构建RabbitMQ消息
     */
    private Message buildMessage(Object messageBody, String messageId, Map<String, Object> properties) {
        // 序列化消息体
        String jsonBody;
        if (messageBody instanceof String) {
            jsonBody = (String) messageBody;
        } else {
            jsonBody = JsonUtils.toJson(messageBody);
        }
        
        // 创建消息属性
        MessageProperties messageProperties = new MessageProperties();
        messageProperties.setMessageId(messageId);
        messageProperties.setTimestamp(new Date());
        messageProperties.setContentType("application/json");
        messageProperties.setContentEncoding("UTF-8");
        
        // 设置自定义属性
        if (properties != null) {
            for (Map.Entry<String, Object> entry : properties.entrySet()) {
                String key = entry.getKey();
                Object value = entry.getValue();
                
                // 处理特殊属性
                switch (key) {
                    case "priority":
                        if (value instanceof Integer) {
                            messageProperties.setPriority((Integer) value);
                        }
                        break;
                    case "expiration":
                        if (value instanceof String) {
                            messageProperties.setExpiration((String) value);
                        } else if (value instanceof Long) {
                            messageProperties.setExpiration(String.valueOf(value));
                        }
                        break;
                    case "x-delay":
                        if (value instanceof Long) {
                            messageProperties.setDelay(((Long) value).intValue());
                        }
                        break;
                    default:
                        messageProperties.setHeader(key, value);
                        break;
                }
            }
        }
        
        return new Message(jsonBody.getBytes(), messageProperties);
    }
    
    /**
     * 等待消息确认
     */
    private SendResult waitForConfirmation(String messageId, long startTime) {
        // 将消息ID加入待确认列表
        SendResult pendingResult = SendResult.success(messageId);
        pendingConfirms.put(messageId, pendingResult);
        
        try {
            // 等待确认（最多等待5秒）
            for (int i = 0; i < 50; i++) {
                SendResult result = pendingConfirms.get(messageId);
                if (result != null && (result.isSuccess() || result.getErrorCode() != null)) {
                    // 已收到确认
                    pendingConfirms.remove(messageId);
                    result.setExecutionTimeMs(System.currentTimeMillis() - startTime);
                    return result;
                }
                Thread.sleep(100); // 等待100ms
            }
            
            // 超时未收到确认
            pendingConfirms.remove(messageId);
            SendResult result = SendResult.failure("CONFIRM_TIMEOUT", "等待消息确认超时");
            result.setExecutionTimeMs(System.currentTimeMillis() - startTime);
            return result;
            
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            pendingConfirms.remove(messageId);
            SendResult result = SendResult.failure("CONFIRM_INTERRUPTED", "等待确认被中断");
            result.setExecutionTimeMs(System.currentTimeMillis() - startTime);
            return result;
        }
    }
    
    /**
     * 处理消息确认
     */
    public void handleConfirmation(String messageId, boolean ack, String cause) {
        SendResult result = pendingConfirms.get(messageId);
        if (result != null) {
            if (ack) {
                result.setSuccess(true);
            } else {
                result.setSuccess(false);
                result.setErrorCode("CONFIRM_NACK");
                result.setErrorMessage("消息确认失败: " + cause);
            }
        }
    }
    
    /**
     * 处理消息返回
     */
    public void handleReturn(String messageId, String replyText) {
        SendResult result = pendingConfirms.get(messageId);
        if (result != null) {
            result.setSuccess(false);
            result.setErrorCode("MESSAGE_RETURNED");
            result.setErrorMessage("消息被返回: " + replyText);
        }
    }
    
    /**
     * 更新统计信息
     */
    private void updateStatistics(String target, boolean success, long executionTimeMs) {
        ProducerStatistics stats = statisticsMap.computeIfAbsent(target, 
            k -> new ProducerStatistics(target));
        
        stats.incrementTotalCount();
        stats.addExecutionTime(executionTimeMs);
        
        if (success) {
            stats.incrementSuccessCount();
        } else {
            stats.incrementFailureCount();
        }
    }
    
    /**
     * 获取生产者统计信息
     */
    public Map<String, ProducerStatistics> getProducerStatistics() {
        return new ConcurrentHashMap<>(statisticsMap);
    }
    
    /**
     * 重置统计信息
     */
    public void resetStatistics() {
        statisticsMap.clear();
        logger.info("生产者统计信息已重置");
    }
    
    /**
     * 生产者统计信息
     */
    public static class ProducerStatistics {
        private final String target;
        private long totalCount = 0;
        private long successCount = 0;
        private long failureCount = 0;
        private long totalExecutionTimeMs = 0;
        private long maxExecutionTimeMs = 0;
        private long minExecutionTimeMs = Long.MAX_VALUE;
        
        public ProducerStatistics(String target) {
            this.target = target;
        }
        
        public synchronized void incrementTotalCount() {
            this.totalCount++;
        }
        
        public synchronized void incrementSuccessCount() {
            this.successCount++;
        }
        
        public synchronized void incrementFailureCount() {
            this.failureCount++;
        }
        
        public synchronized void addExecutionTime(long executionTimeMs) {
            this.totalExecutionTimeMs += executionTimeMs;
            this.maxExecutionTimeMs = Math.max(this.maxExecutionTimeMs, executionTimeMs);
            this.minExecutionTimeMs = Math.min(this.minExecutionTimeMs, executionTimeMs);
        }
        
        public double getSuccessRate() {
            return totalCount > 0 ? (double) successCount / totalCount : 0.0;
        }
        
        public double getAverageExecutionTimeMs() {
            return totalCount > 0 ? (double) totalExecutionTimeMs / totalCount : 0.0;
        }
        
        // Getters
        public String getTarget() {
            return target;
        }
        
        public long getTotalCount() {
            return totalCount;
        }
        
        public long getSuccessCount() {
            return successCount;
        }
        
        public long getFailureCount() {
            return failureCount;
        }
        
        public long getTotalExecutionTimeMs() {
            return totalExecutionTimeMs;
        }
        
        public long getMaxExecutionTimeMs() {
            return maxExecutionTimeMs;
        }
        
        public long getMinExecutionTimeMs() {
            return minExecutionTimeMs == Long.MAX_VALUE ? 0 : minExecutionTimeMs;
        }
    }
    
    @PreDestroy
    public void destroy() {
        logger.info("关闭RabbitMQ消息生产者");
        executorService.shutdown();
        try {
            if (!executorService.awaitTermination(5, TimeUnit.SECONDS)) {
                executorService.shutdownNow();
            }
        } catch (InterruptedException e) {
            executorService.shutdownNow();
            Thread.currentThread().interrupt();
        }
    }
}