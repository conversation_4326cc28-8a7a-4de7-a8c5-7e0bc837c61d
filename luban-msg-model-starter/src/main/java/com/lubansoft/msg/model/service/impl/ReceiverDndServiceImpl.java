package com.lubansoft.msg.model.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lubansoft.msg.model.repository.entity.ReceiverDnd;
import com.lubansoft.msg.model.repository.repo.ReceiverDndRepo;
import com.lubansoft.msg.model.service.ReceiverDndService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户免打扰设置服务实现类
 */
@Service
public class ReceiverDndServiceImpl implements ReceiverDndService {

    @Autowired
    private ReceiverDndRepo receiverDndRepo;

    @Override
    public ReceiverDnd save(ReceiverDnd receiverDnd) {
        receiverDnd.setCreatedAt(LocalDateTime.now());
        receiverDnd.setUpdatedAt(LocalDateTime.now());
        receiverDndRepo.insert(receiverDnd);
        return receiverDnd;
    }

    @Override
    public ReceiverDnd update(ReceiverDnd receiverDnd) {
        receiverDnd.setUpdatedAt(LocalDateTime.now());
        receiverDndRepo.updateById(receiverDnd);
        return receiverDnd;
    }

    @Override
    public boolean deleteById(Long id) {
        return receiverDndRepo.deleteById(id) > 0;
    }

    @Override
    public ReceiverDnd findById(Long id) {
        return receiverDndRepo.selectById(id);
    }

    @Override
    public List<ReceiverDnd> findByReceiverId(String receiverId) {
        return receiverDndRepo.findByReceiverId(receiverId);
    }

    @Override
    public IPage<ReceiverDnd> findByPage(Page<ReceiverDnd> page, String receiverId) {
        QueryWrapper<ReceiverDnd> queryWrapper = new QueryWrapper<>();
        if (receiverId != null && !receiverId.isEmpty()) {
            queryWrapper.eq("receiver_id", receiverId);
        }
        return receiverDndRepo.findByPage(page, queryWrapper);
    }
}