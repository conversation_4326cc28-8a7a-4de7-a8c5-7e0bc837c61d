package com.lubansoft.msg.model.repository.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lubansoft.msg.common.enums.MessageStatus;
import com.lubansoft.msg.common.model.ChannelType;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import com.lubansoft.msg.model.repository.entity.MessageRecord;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 消息记录Repository
 */
@Mapper
public interface MessageRecordMapper extends BaseMapper<MessageRecord> {
    
    /**
     * 根据消息ID查找消息记录
     */
    @Select("SELECT * FROM message_record WHERE message_id = #{messageId}")
    List<MessageRecord> findByMessageId(@Param("messageId") String messageId);
    
    /**
     * 根据请求ID查找消息记录
     */
    @Select("SELECT * FROM message_record WHERE request_id = #{requestId}")
    List<MessageRecord> findByRequestId(@Param("requestId") String requestId);
    
    /**
     * 根据接收者查找消息记录（分页）
     */
    @Select("SELECT * FROM message_record WHERE receiver = #{receiver}")
    IPage<MessageRecord> findByReceiver(@Param("receiver") String receiver, Page<?> pageNum);
    
    /**
     * 根据渠道查找消息记录（分页）
     */
    @Select("SELECT * FROM message_record WHERE channel = #{channel}")
    IPage<MessageRecord> findByChannel(@Param("channel") String channel, Page<?> pageNum);

    /**
     * 根据状态查找消息记录（分页）
     */
    @Select("SELECT * FROM message_record WHERE status = #{status}")
    IPage<MessageRecord> findByStatus(@Param("status") String status, Page<?> pageNum);
    
    /**
     * 根据模板ID查找消息记录（分页）
     */
    @Select("SELECT * FROM message_record WHERE template_id = #{templateId}")
    IPage<MessageRecord> findByTemplateId(@Param("templateId") String templateId, Page<?> pageNum);
    
    /**
     * 查找所有消息记录（分页）
     */
    default IPage<MessageRecord> findAll(Page<MessageRecord> pageNum) {
        return selectPage(pageNum, null);
    }
    
    /**
     * 根据创建时间范围查找消息记录（分页）
     */
    @Select("SELECT * FROM message_record WHERE created_at BETWEEN #{startTime} AND #{endTime}")
    IPage<MessageRecord> findByCreatedAtBetween(@Param("startTime") LocalDateTime startTime, 
                                                  @Param("endTime") LocalDateTime endTime, 
                                                  Page<?> pageNum);
    
    /**
     * 统计指定时间范围内的消息数量
     */
    @Select("SELECT COUNT(*) FROM message_record WHERE created_at BETWEEN #{startTime} AND #{endTime}")
    Long countByCreatedAtBetween(@Param("startTime") LocalDateTime startTime, 
                                   @Param("endTime") LocalDateTime endTime);
    
    /**
     * 根据状态统计消息数量
     */
    @Select("SELECT COUNT(*) FROM message_record WHERE status = #{status}")
    Long countByStatus(@Param("status") String status);

    /**
     * 根据渠道统计消息数量
     */
    @Select("SELECT COUNT(*) FROM message_record WHERE channel = #{channel}")
    Long countByChannel(@Param("channel") String channel);

    /**
     * 查找可重试的消息
     */
    @Select("SELECT * FROM message_record WHERE status = #{status} AND retry_count < #{maxRetryCount}")
    List<MessageRecord> findRetryableMessages(@Param("status") String status,
                                              @Param("maxRetryCount") int maxRetryCount);

    /**
     * 统计各状态的消息数量
     */
    @Select("SELECT status, COUNT(*) as count FROM message_record GROUP BY status")
    List<StatusCount> countGroupByStatus();

    /**
     * 统计各渠道的消息数量
     */
    @Select("SELECT channel, COUNT(*) as count FROM message_record GROUP BY channel")
    List<ChannelCount> countGroupByChannel();

    /**
     * 状态统计结果
     */
    class StatusCount {
        private String status;
        private Long count;

        public StatusCount() {}

        public StatusCount(String status, Long count) {
            this.status = status;
            this.count = count;
        }

        public String getStatus() { return status; }
        public void setStatus(String status) { this.status = status; }
        public Long getCount() { return count; }
        public void setCount(Long count) { this.count = count; }
    }

    /**
     * 渠道统计结果
     */
    class ChannelCount {
        private String channel;
        private Long count;

        public ChannelCount() {}

        public ChannelCount(String channel, Long count) {
            this.channel = channel;
            this.count = count;
        }

        public String getChannel() { return channel; }
        public void setChannel(String channel) { this.channel = channel; }
        public Long getCount() { return count; }
        public void setCount(Long count) { this.count = count; }
    }
}