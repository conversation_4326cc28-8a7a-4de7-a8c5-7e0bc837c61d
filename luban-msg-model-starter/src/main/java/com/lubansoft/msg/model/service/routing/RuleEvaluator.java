package com.lubansoft.msg.model.service.routing;

import com.lubansoft.msg.model.model.MessageRequestDTO;
import com.lubansoft.msg.model.repository.entity.RoutingRule;
import com.lubansoft.msg.model.service.processor.MessageContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.expression.Expression;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.regex.Pattern;

/**
 * 规则评估器
 */
@Component
public class RuleEvaluator {
    
    private static final Logger logger = LoggerFactory.getLogger(RuleEvaluator.class);
    
    private final ExpressionParser expressionParser = new SpelExpressionParser();
    private final Map<String, Expression> expressionCache = new ConcurrentHashMap<>();
    private final Map<String, Pattern> patternCache = new ConcurrentHashMap<>();
    
    /**
     * 评估路由规则（使用MessageContext）
     *
     * @param rule 路由规则
     * @param messageContext 消息上下文
     * @param routingContext 路由上下文
     * @return 是否匹配
     */
    public boolean evaluate(RoutingRule rule, MessageContext messageContext, RoutingContext routingContext) {
        if (messageContext == null || messageContext.getMessageRequest() == null) {
            logger.info("MessageContext或MessageRequest为null: ruleId={}", rule.getRuleId());
            return false;
        }

        return evaluate(rule, messageContext.getMessageRequest(), routingContext);
    }

    /**
     * 评估路由规则
     *
     * @param rule 路由规则
     * @param request 消息请求
     * @param context 路由上下文
     * @return 是否匹配
     */
    public boolean evaluate(RoutingRule rule, MessageRequestDTO request, RoutingContext context) {
        String condition = rule.getCondition();
        if (condition == null || condition.trim().isEmpty()) {
            logger.info("路由规则条件为空: ruleId={}", rule.getRuleId());
            return false;
        }
        
        try {
            // 添加null检查
            if (request == null) {
                logger.info("MessageRequestDTO为null: ruleId={}", rule.getRuleId());
                return false;
            }

            // 判断条件类型并选择合适的评估方式
            if (isSpelExpression(condition)) {
                return evaluateSpelExpression(rule.getRuleId(), condition, request, context);
            } else if (isRegexPattern(condition)) {
                return evaluateRegexPattern(rule.getRuleId(), condition, request, context);
            } else {
                return evaluateSimpleCondition(condition, request, context);
            }
        } catch (Exception e) {
            logger.error("规则评估异常: ruleId={}, condition={}, request={}", rule.getRuleId(), condition, request != null ? request.getMessageId() : "null", e);
            return false;
        }
    }
    
    /**
     * 判断是否为SpEL表达式
     */
    private boolean isSpelExpression(String condition) {
        // SpEL表达式通常包含变量引用、方法调用等
        return condition.contains("#") || condition.contains(".") || condition.contains("(");
    }
    
    /**
     * 判断是否为正则表达式模式
     */
    private boolean isRegexPattern(String condition) {
        // 正则表达式模式以regex:开头
        return condition.startsWith("regex:");
    }
    
    /**
     * 评估SpEL表达式
     */
    private boolean evaluateSpelExpression(String ruleId, String condition,
                                           MessageRequestDTO request, RoutingContext context) {
        // 从缓存获取或创建表达式
        Expression expression = expressionCache.computeIfAbsent(ruleId, 
            k -> expressionParser.parseExpression(condition));
        
        // 创建评估上下文
        StandardEvaluationContext spelContext = createSpelContext(request, context);
        
        // 评估表达式
        Object result = expression.getValue(spelContext);
        
        // 转换为布尔值
        return convertToBoolean(result);
    }
    
    /**
     * 评估正则表达式模式
     */
    private boolean evaluateRegexPattern(String ruleId, String condition,
                                         MessageRequestDTO request, RoutingContext context) {
        // 提取正则表达式
        String regex = condition.substring(6); // 去掉"regex:"前缀
        
        // 从缓存获取或创建Pattern
        Pattern pattern = patternCache.computeIfAbsent(ruleId, k -> Pattern.compile(regex));
        
        // 对模板ID进行匹配（添加null检查）
        return request.getTemplateId() != null && pattern.matcher(request.getTemplateId()).matches();
    }
    
    /**
     * 评估简单条件
     */
    private boolean evaluateSimpleCondition(String condition, MessageRequestDTO request,
                                          RoutingContext context) {
        // 处理简单的字符串匹配条件（添加null检查）
        if (condition.startsWith("templateId=")) {
            String expectedTemplateId = condition.substring(11);
            return request.getTemplateId() != null && expectedTemplateId.equals(request.getTemplateId());
        }

        if (condition.startsWith("templateId.startsWith(")) {
            String prefix = extractStringParameter(condition, "templateId.startsWith(");
            return request.getTemplateId() != null && request.getTemplateId().startsWith(prefix);
        }

        if (condition.startsWith("templateId.contains(")) {
            String substring = extractStringParameter(condition, "templateId.contains(");
            return request.getTemplateId() != null && request.getTemplateId().contains(substring);
        }
        
        if (condition.startsWith("appId=")) {
            String expectedAppId = condition.substring(6);
            return context != null && expectedAppId.equals(context.getAppId());
        }
        
        if (condition.startsWith("priority>=")) {
            int minPriority = Integer.parseInt(condition.substring(10));
            return request.getPriority() != null && request.getPriority() >= minPriority;
        }
        
        if (condition.startsWith("priority<=")) {
            int maxPriority = Integer.parseInt(condition.substring(10));
            return request.getPriority() != null && request.getPriority() <= maxPriority;
        }
        
        if (condition.startsWith("receiverCount>=")) {
            int minCount = Integer.parseInt(condition.substring(15));
            return request.getReceivers() != null && request.getReceivers().size() >= minCount;
        }

        if (condition.startsWith("receiverCount<=")) {
            int maxCount = Integer.parseInt(condition.substring(15));
            return request.getReceivers() != null && request.getReceivers().size() <= maxCount;
        }
        
        // 时间条件
        if (condition.startsWith("hour>=")) {
            int minHour = Integer.parseInt(condition.substring(6));
            return LocalDateTime.now().getHour() >= minHour;
        }
        
        if (condition.startsWith("hour<=")) {
            int maxHour = Integer.parseInt(condition.substring(6));
            return LocalDateTime.now().getHour() <= maxHour;
        }
        
        // 默认返回false
        logger.info("未识别的简单条件: {}", condition);
        return false;
    }
    
    /**
     * 创建SpEL评估上下文
     */
    private StandardEvaluationContext createSpelContext(MessageRequestDTO request, RoutingContext context) {
        StandardEvaluationContext spelContext = new StandardEvaluationContext();

        // 添加null检查，设置消息请求相关变量
        spelContext.setVariable("templateId", request != null ? request.getTemplateId() : null);
        spelContext.setVariable("priority", request != null ? request.getPriority() : null);
        spelContext.setVariable("receiverCount", request != null && request.getReceivers() != null ? request.getReceivers().size() : 0);
        spelContext.setVariable("globalParam", request != null ? request.getGlobalParam() : null);

        // 设置消息请求对象本身（用于直接属性访问）
        spelContext.setVariable("request", request);
        spelContext.setRootObject(request);

        // 设置上下文相关变量
        spelContext.setVariable("appId", context != null ? context.getAppId() : null);
        spelContext.setVariable("requestId", context != null ? context.getRequestId() : null);
        spelContext.setVariable("context", context);
        
        // 设置时间相关变量
        LocalDateTime now = LocalDateTime.now();
        spelContext.setVariable("currentTime", now);
        spelContext.setVariable("currentHour", now.getHour());
        spelContext.setVariable("currentMinute", now.getMinute());
        spelContext.setVariable("currentSecond", now.getSecond());
        spelContext.setVariable("dayOfWeek", now.getDayOfWeek().getValue());
        spelContext.setVariable("dayOfMonth", now.getDayOfMonth());
        spelContext.setVariable("month", now.getMonthValue());
        spelContext.setVariable("year", now.getYear());
        
        // 设置接收者信息
        if (request != null && request.getReceivers() != null && !request.getReceivers().isEmpty()) {
            String firstReceiver = request.getReceivers().get(0);
            spelContext.setVariable("firstReceiver", firstReceiver);
            // 判断接收者类型
            spelContext.setVariable("isPhone", firstReceiver != null ? firstReceiver.matches("^1\\d{10}$") : false);
            spelContext.setVariable("isEmail", firstReceiver != null ? firstReceiver.contains("@") : false);
            spelContext.setVariable("isUserId", firstReceiver != null ? (!firstReceiver.matches("^1\\d{10}$") && !firstReceiver.contains("@")) : false);
        } else {
            spelContext.setVariable("firstReceiver", null);
            spelContext.setVariable("isPhone", false);
            spelContext.setVariable("isEmail", false);
            spelContext.setVariable("isUserId", false);
        }
        
        // 设置上下文属性
        if (context != null && context.getAttributes() != null) {
            for (Map.Entry<String, Object> entry : context.getAttributes().entrySet()) {
                spelContext.setVariable(entry.getKey(), entry.getValue());
            }
        }
        
        // 注册自定义函数
        registerCustomFunctions(spelContext);
        
        return spelContext;
    }

    /**
     * 创建SpEL评估上下文（使用MessageContext）
     */
    private StandardEvaluationContext createSpelContext(MessageContext messageContext, RoutingContext routingContext) {
        StandardEvaluationContext spelContext = new StandardEvaluationContext();

        // 从MessageContext中获取MessageRequest
        MessageRequestDTO request = messageContext != null ? messageContext.getMessageRequest() : null;

        // 设置消息请求相关变量（添加null检查）
        spelContext.setVariable("templateId", request != null ? request.getTemplateId() : null);
        spelContext.setVariable("priority", request != null ? request.getPriority() : null);
        spelContext.setVariable("receiverCount", request != null && request.getReceivers() != null ? request.getReceivers().size() : 0);
        spelContext.setVariable("globalParam", request != null ? request.getGlobalParam() : null);

        // 设置消息请求对象本身
        spelContext.setVariable("request", request);
        spelContext.setRootObject(request);

        // 设置MessageContext相关变量
        if (messageContext != null) {
            spelContext.setVariable("messageContext", messageContext);
            spelContext.setVariable("appId", messageContext.getAppId());
            spelContext.setVariable("requestId", messageContext.getRequestId());

            // 设置MessageContext中的属性
            if (messageContext.getAttributes() != null) {
                for (Map.Entry<String, Object> entry : messageContext.getAttributes().entrySet()) {
                    spelContext.setVariable(entry.getKey(), entry.getValue());
                }
            }
        }

        // 设置路由上下文相关变量
        if (routingContext != null) {
            spelContext.setVariable("routingContext", routingContext);
            if (routingContext.getAttributes() != null) {
                for (Map.Entry<String, Object> entry : routingContext.getAttributes().entrySet()) {
                    spelContext.setVariable("routing_" + entry.getKey(), entry.getValue());
                }
            }
        }

        // 设置时间相关变量
        LocalDateTime now = LocalDateTime.now();
        spelContext.setVariable("currentTime", now);
        spelContext.setVariable("currentHour", now.getHour());
        spelContext.setVariable("currentMinute", now.getMinute());
        spelContext.setVariable("currentSecond", now.getSecond());
        spelContext.setVariable("dayOfWeek", now.getDayOfWeek().getValue());
        spelContext.setVariable("dayOfMonth", now.getDayOfMonth());
        spelContext.setVariable("month", now.getMonthValue());
        spelContext.setVariable("year", now.getYear());

        // 设置接收者信息（添加null检查）
        if (request != null && request.getReceivers() != null && !request.getReceivers().isEmpty()) {
            String firstReceiver = request.getReceivers().get(0);
            spelContext.setVariable("firstReceiver", firstReceiver);
            // 判断接收者类型
            spelContext.setVariable("isPhone", firstReceiver != null ? firstReceiver.matches("^1\\d{10}$") : false);
            spelContext.setVariable("isEmail", firstReceiver != null ? firstReceiver.contains("@") : false);
            spelContext.setVariable("isUserId", firstReceiver != null ? (!firstReceiver.matches("^1\\d{10}$") && !firstReceiver.contains("@")) : false);
        } else {
            spelContext.setVariable("firstReceiver", null);
            spelContext.setVariable("isPhone", false);
            spelContext.setVariable("isEmail", false);
            spelContext.setVariable("isUserId", false);
        }

        // 注册自定义函数
        registerCustomFunctions(spelContext);

        return spelContext;
    }
    
    /**
     * 注册自定义函数
     */
    private void registerCustomFunctions(StandardEvaluationContext context) {
        try {
            // 注册字符串工具函数
            context.registerFunction("isEmpty", 
                String.class.getDeclaredMethod("isEmpty"));
            context.registerFunction("startsWith", 
                String.class.getDeclaredMethod("startsWith", String.class));
            context.registerFunction("contains", 
                String.class.getDeclaredMethod("contains", CharSequence.class));
            context.registerFunction("matches", 
                String.class.getDeclaredMethod("matches", String.class));
        } catch (NoSuchMethodException e) {
            logger.error("注册自定义函数失败", e);
        }
    }
    
    /**
     * 提取字符串参数
     */
    private String extractStringParameter(String condition, String prefix) {
        int startIndex = condition.indexOf(prefix) + prefix.length();
        int endIndex = condition.lastIndexOf(")");
        String parameter = condition.substring(startIndex, endIndex);
        
        // 去掉引号
        if (parameter.startsWith("'") && parameter.endsWith("'")) {
            parameter = parameter.substring(1, parameter.length() - 1);
        } else if (parameter.startsWith("\"") && parameter.endsWith("\"")) {
            parameter = parameter.substring(1, parameter.length() - 1);
        }
        
        return parameter;
    }
    
    /**
     * 转换为布尔值
     */
    private boolean convertToBoolean(Object result) {
        if (result instanceof Boolean) {
            return (Boolean) result;
        } else if (result instanceof Number) {
            return ((Number) result).doubleValue() != 0.0;
        } else if (result instanceof String) {
            String str = (String) result;
            return !str.isEmpty() && !"false".equalsIgnoreCase(str) && !"0".equals(str);
        } else if (result != null) {
            return true;
        } else {
            return false;
        }
    }
}