package com.lubansoft.msg.model.model;

import com.lubansoft.msg.model.repository.entity.ReceiverDnd;
import com.lubansoft.msg.model.repository.entity.ReceiverSub;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.time.LocalTime;
import java.util.List;

/**
 * 用户接收设置DTO
 */
@Schema(description = "用户接收设置信息")
public class ReceiverSettingDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "用户ID")
    private String receiverId;

    @Schema(description = "渠道订阅设置列表")
    private List<ChannelSubscription> subscriptions;

    @Schema(description = "免打扰设置列表")
    private List<DoNotDisturbSetting> doNotDisturbSettings;

    // Getters and Setters

    public String getReceiverId() {
        return receiverId;
    }

    public void setReceiverId(String receiverId) {
        this.receiverId = receiverId;
    }

    public List<ChannelSubscription> getSubscriptions() {
        return subscriptions;
    }

    public void setSubscriptions(List<ChannelSubscription> subscriptions) {
        this.subscriptions = subscriptions;
    }

    public List<DoNotDisturbSetting> getDoNotDisturbSettings() {
        return doNotDisturbSettings;
    }

    public void setDoNotDisturbSettings(List<DoNotDisturbSetting> doNotDisturbSettings) {
        this.doNotDisturbSettings = doNotDisturbSettings;
    }

    /**
     * 渠道订阅设置
     */
    @Schema(description = "渠道订阅设置")
    public static class ChannelSubscription implements Serializable {
        private static final long serialVersionUID = 1L;

        @Schema(description = "渠道名称")
        private String channel;

        @Schema(description = "是否订阅")
        private Boolean isSub;

        @Schema(description = "联系方式")
        private String contact;

        // Constructors
        public ChannelSubscription() {}

        public ChannelSubscription(ReceiverSub receiverSub) {
            if (receiverSub != null) {
                this.channel = receiverSub.getChannel();
                this.isSub = receiverSub.getIsSub();
                this.contact = receiverSub.getContact();
            }
        }

        // Getters and Setters
        public String getChannel() {
            return channel;
        }

        public void setChannel(String channel) {
            this.channel = channel;
        }

        public Boolean getIsSub() {
            return isSub;
        }

        public void setIsSub(Boolean isSub) {
            this.isSub = isSub;
        }

        public String getContact() {
            return contact;
        }

        public void setContact(String contact) {
            this.contact = contact;
        }
    }

    /**
     * 免打扰设置
     */
    @Schema(description = "免打扰设置")
    public static class DoNotDisturbSetting implements Serializable {
        private static final long serialVersionUID = 1L;

        @Schema(description = "设置ID")
        private Long id;

        @Schema(description = "策略")
        private String policy;

        @Schema(description = "开始时间")
        private LocalTime startTime;

        @Schema(description = "结束时间")
        private LocalTime endTime;

        @Schema(description = "星期几列表(1-7代表周一到周日)")
        private Integer[] days;

        // Constructors
        public DoNotDisturbSetting() {}

        public DoNotDisturbSetting(ReceiverDnd receiverDnd) {
            if (receiverDnd != null) {
                this.id = receiverDnd.getId();
                this.policy = receiverDnd.getPolicy();
                this.startTime = receiverDnd.getStartTime();
                this.endTime = receiverDnd.getEndTime();
                this.days = receiverDnd.getDays();
            }
        }

        // Getters and Setters
        public Long getId() {
            return id;
        }

        public void setId(Long id) {
            this.id = id;
        }

        public String getPolicy() {
            return policy;
        }

        public void setPolicy(String policy) {
            this.policy = policy;
        }

        public LocalTime getStartTime() {
            return startTime;
        }

        public void setStartTime(LocalTime startTime) {
            this.startTime = startTime;
        }

        public LocalTime getEndTime() {
            return endTime;
        }

        public void setEndTime(LocalTime endTime) {
            this.endTime = endTime;
        }

        public Integer[] getDays() {
            return days;
        }

        public void setDays(Integer[] days) {
            this.days = days;
        }
    }
}