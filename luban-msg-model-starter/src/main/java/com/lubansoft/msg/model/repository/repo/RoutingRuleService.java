package com.lubansoft.msg.model.repository.repo;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lubansoft.msg.model.model.MessageRequestDTO;
import jakarta.annotation.PostConstruct;
import com.lubansoft.msg.model.repository.entity.RoutingRule;
import com.lubansoft.msg.model.repository.mapper.RoutingRuleMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.locks.ReentrantReadWriteLock;
import java.util.stream.Collectors;

/**
 * 路由规则服务类
 * 提供路由规则的数据访问和业务逻辑处理
 */
@Service
public class RoutingRuleService extends ServiceImpl<RoutingRuleMapper, RoutingRule> {
    
    private static final Logger logger = LoggerFactory.getLogger(RoutingRuleService.class);
    
    // RoutingRuleService专注于数据访问，缓存管理交给RoutingRuleManager


    
    /**
     * 初始化规则服务
     */
    @PostConstruct
    public void initialize() {
        logger.info("初始化路由规则数据访问服务");
        // RoutingRuleService专注于数据访问，不再管理缓存
        // 缓存管理由RoutingRuleManager负责
    }
    
    /**
     * 查找所有启用的路由规则，按优先级排序
     */
    public List<RoutingRule> findEnabledRulesOrderByPriority() {
        return list(new LambdaQueryWrapper<RoutingRule>()
                .eq(RoutingRule::getEnabled, true)
                .orderByAsc(RoutingRule::getPriority));
    }
    
    /**
     * 根据规则名称查找路由规则
     */
    public Optional<RoutingRule> findByRuleName(String ruleName) {
        RoutingRule rule = getOne(new LambdaQueryWrapper<RoutingRule>()
                .eq(RoutingRule::getRuleName, ruleName));
        return Optional.ofNullable(rule);
    }
    
    /**
     * 根据启用状态查找路由规则
     */
    public List<RoutingRule> findByEnabledOrderByPriority(Boolean enabled) {
        return list(new LambdaQueryWrapper<RoutingRule>()
                .eq(RoutingRule::getEnabled, enabled)
                .orderByAsc(RoutingRule::getPriority));
    }
    
    /**
     * 根据模板ID查找启用的路由规则（包括通用规则）
     * 
     * @param templateId 模板ID
     * @return 路由规则列表，按优先级排序
     */
    public List<RoutingRule> findByTemplateIdAndEnabled(String templateId) {
        return list(new LambdaQueryWrapper<RoutingRule>()
                .eq(RoutingRule::getEnabled, true)
                .and(wrapper -> wrapper
                    .eq(RoutingRule::getTemplateId, templateId)
                    .or()
                    .isNull(RoutingRule::getTemplateId)
                )
                .orderByAsc(RoutingRule::getPriority));
    }
    
    /**
     * 根据模板ID查找专用的路由规则（不包括通用规则）
     * 
     * @param templateId 模板ID
     * @return 路由规则列表，按优先级排序
     */
    public List<RoutingRule> findByTemplateIdOnly(String templateId) {
        return list(new LambdaQueryWrapper<RoutingRule>()
                .eq(RoutingRule::getEnabled, true)
                .eq(RoutingRule::getTemplateId, templateId)
                .orderByAsc(RoutingRule::getPriority));
    }
    
    /**
     * 查找通用路由规则（templateId为空的规则）
     * 
     * @return 通用路由规则列表，按优先级排序
     */
    public List<RoutingRule> findGeneralRules() {
        return list(new LambdaQueryWrapper<RoutingRule>()
                .eq(RoutingRule::getEnabled, true)
                .isNull(RoutingRule::getTemplateId)
                .orderByAsc(RoutingRule::getPriority));
    }
    
    /**
     * 查找指定优先级范围内的路由规则
     */
    public List<RoutingRule> findByPriorityBetweenAndEnabled(Integer minPriority, Integer maxPriority) {
        return list(new LambdaQueryWrapper<RoutingRule>()
                .between(RoutingRule::getPriority, minPriority, maxPriority)
                .eq(RoutingRule::getEnabled, true)
                .orderByAsc(RoutingRule::getPriority));
    }
    
    /**
     * 统计启用的路由规则数量
     */
    public long countEnabledRules() {
        return count(new LambdaQueryWrapper<RoutingRule>()
                .eq(RoutingRule::getEnabled, true));
    }
    
    /**
     * 更新路由规则启用状态
     */
    public boolean updateEnabledByRuleId(String ruleId, Boolean enabled) {
        return update(new LambdaUpdateWrapper<RoutingRule>()
                .eq(RoutingRule::getRuleId, ruleId)
                .set(RoutingRule::getEnabled, enabled)
                .set(RoutingRule::getUpdatedAt, LocalDateTime.now()));
    }
    
    /**
     * 批量更新路由规则启用状态
     */
    public boolean updateEnabledByRuleIdIn(List<String> ruleIds, Boolean enabled) {
        return update(new LambdaUpdateWrapper<RoutingRule>()
                .in(RoutingRule::getRuleId, ruleIds)
                .set(RoutingRule::getEnabled, enabled)
                .set(RoutingRule::getUpdatedAt, LocalDateTime.now()));
    }
    
    /**
     * 更新路由规则优先级
     */
    public boolean updatePriorityByRuleId(String ruleId, Integer priority) {
        return update(new LambdaUpdateWrapper<RoutingRule>()
                .eq(RoutingRule::getRuleId, ruleId)
                .set(RoutingRule::getPriority, priority)
                .set(RoutingRule::getUpdatedAt, LocalDateTime.now()));
    }
    
    /**
     * 删除指定时间之前的禁用路由规则
     */
    public boolean deleteDisabledRulesBeforeTime(LocalDateTime beforeTime) {
        return remove(new LambdaQueryWrapper<RoutingRule>()
                .eq(RoutingRule::getEnabled, false)
                .lt(RoutingRule::getUpdatedAt, beforeTime));
    }
    
    /**
     * 查找最高优先级的启用规则
     */
    public Optional<RoutingRule> findTopEnabledRuleByPriority() {
        RoutingRule rule = getOne(new LambdaQueryWrapper<RoutingRule>()
                .eq(RoutingRule::getEnabled, true)
                .orderByAsc(RoutingRule::getPriority)
                .last("LIMIT 1"));
        return Optional.ofNullable(rule);
    }
    
    /**
     * 查找最低优先级的启用规则
     */
    public Optional<RoutingRule> findBottomEnabledRuleByPriority() {
        RoutingRule rule = getOne(new LambdaQueryWrapper<RoutingRule>()
                .eq(RoutingRule::getEnabled, true)
                .orderByDesc(RoutingRule::getPriority)
                .last("LIMIT 1"));
        return Optional.ofNullable(rule);
    }
    
    /**
     * 查找指定优先级之后的规则（用于优先级调整）
     */
    public List<RoutingRule> findByPriorityGreaterThanAndEnabled(Integer priority) {
        return list(new LambdaQueryWrapper<RoutingRule>()
                .gt(RoutingRule::getPriority, priority)
                .eq(RoutingRule::getEnabled, true)
                .orderByAsc(RoutingRule::getPriority));
    }
    
    /**
     * 查找指定优先级之前的规则（用于优先级调整）
     */
    public List<RoutingRule> findByPriorityLessThanAndEnabled(Integer priority) {
        return list(new LambdaQueryWrapper<RoutingRule>()
                .lt(RoutingRule::getPriority, priority)
                .eq(RoutingRule::getEnabled, true)
                .orderByDesc(RoutingRule::getPriority));
    }
    
    /**
     * 根据启用状态分页查询路由规则
     */
    public IPage<RoutingRule> findByEnabledWithPage(Boolean enabled, Page<RoutingRule> pageNum) {
        return page(pageNum, new LambdaQueryWrapper<RoutingRule>()
                .eq(RoutingRule::getEnabled, enabled)
                .orderByAsc(RoutingRule::getPriority));
    }
    
    /**
     * 根据规则名称模糊查询（忽略大小写）
     */
    public List<RoutingRule> findByRuleNameContainingIgnoreCase(String ruleName) {
        return list(new LambdaQueryWrapper<RoutingRule>()
                .like(RoutingRule::getRuleName, ruleName));
    }
    
    /**
     * 根据规则名称模糊查询
     */
    public List<RoutingRule> findByRuleNameContaining(String ruleName) {
        return findByRuleNameContainingIgnoreCase(ruleName);
    }
    
    /**
     * 查找最大优先级
     */
    public Integer findMaxPriority() {
        QueryWrapper<RoutingRule> wrapper = new QueryWrapper<>();
        wrapper.select("MAX(priority) as maxPriority");
        RoutingRule result = getOne(wrapper);
        return result != null ? result.getPriority() : 0;
    }
    
    /**
     * 创建路由规则（纯数据访问）
     *
     * @param rule 路由规则
     * @return 创建后的路由规则
     */
    @Transactional
    public RoutingRule createRule(RoutingRule rule) {
        logger.info("创建路由规则: ruleName={}", rule.getRuleName());

        if (rule.getPriority() == null) {
            // 设置默认优先级为最大优先级+1
            Integer maxPriority = findMaxPriority();
            rule.setPriority(maxPriority != null ? maxPriority + 1 : 1);
        }
        rule.setUpdatedAt(LocalDateTime.now());
        saveOrUpdate(rule);

        return rule;
    }

    /**
     * 更新路由规则（纯数据访问）
     *
     * @param ruleId 规则ID
     * @param rule   路由规则
     * @return 更新后的路由规则
     */
    @Transactional
    public RoutingRule updateRule(String ruleId, RoutingRule rule) {
        logger.info("更新路由规则: ruleId={}", ruleId);
        rule.setRuleId(ruleId);
        rule.setUpdatedAt(LocalDateTime.now());
        saveOrUpdate(rule);

        return rule;
    }

    /**
     * 删除路由规则（纯数据访问）
     *
     * @param ruleId 规则ID
     */
    @Transactional
    public void deleteRule(String ruleId) {
        logger.info("删除路由规则: ruleId={}", ruleId);
        removeById(ruleId);
    }
    
    /**
     * 根据ID查找路由规则（纯数据访问）
     *
     * @param ruleId 规则ID
     * @return 路由规则
     */
    public Optional<RoutingRule> findById(String ruleId) {
        return Optional.ofNullable(getById(ruleId));
    }
    
    /**
     * 分页查找路由规则
     *
     * @param pageNum   页码
     * @param pageSize   每页大小
     * @param enabled 启用状态
     * @return 路由规则分页结果
     */
    public IPage<RoutingRule> findRules(int pageNum, int pageSize, Boolean enabled) {
        Page<RoutingRule> pageObj = new Page<>(pageNum, pageSize);
        if (enabled != null) {
            return findByEnabledWithPage(enabled, pageObj);
        } else {
            return page(pageObj);
        }
    }
    
    /**
     * 查找所有启用的路由规则（纯数据访问）
     *
     * @return 路由规则列表
     */
    public List<RoutingRule> findEnabledRules() {
        return list(new LambdaQueryWrapper<RoutingRule>()
                .eq(RoutingRule::getEnabled, true)
                .orderByAsc(RoutingRule::getPriority));
    }
    
    /**
     * 更新路由规则状态
     *
     * @param ruleId  规则ID
     * @param enabled 启用状态
     */
    @Transactional
    public void updateRuleStatus(String ruleId, boolean enabled) {
        logger.info("更新路由规则状态: ruleId={}, enabled={}", ruleId, enabled);
        
        // 检查规则是否存在
        RoutingRule rule = getById(ruleId);
        if (rule == null) {
            throw new IllegalArgumentException("路由规则不存在: " + ruleId);
        }
        
        // 更新状态
        updateEnabledByRuleId(ruleId, enabled);
    }
    

    









}