package com.lubansoft.msg.model.service;

import jakarta.annotation.PostConstruct;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.*;
import org.springframework.amqp.rabbit.core.RabbitAdmin;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 队列配置管理器
 * 预定义四种队列类型，与渠道解耦
 */
@Component
public class QueueConfigManager {
    
    private static final Logger logger = LoggerFactory.getLogger(QueueConfigManager.class);
    
    @Autowired
    private RabbitAdmin rabbitAdmin;
    
    @Autowired
    private RabbitTemplate rabbitTemplate;

    @Value("${message.queue.ttl:1800000}") // 30分钟TTL
    private long messageTtl;
    
    @Value("${message.queue.max-length:100000}")
    private int maxQueueLength;
    
    @Value("${message.queue.max-retry:3}")
    private int maxRetryCount;
    
    // 预定义队列配置缓存 - 按队列类型存储
    private final Map<QueueType, QueueConfig> predefinedQueues = new ConcurrentHashMap<>();
    
    // 交换机配置
    public static final String MESSAGE_EXCHANGE = "message.exchange";
    public static final String DELAY_EXCHANGE = "message.delay.exchange";
    public static final String DLQ_EXCHANGE = "message.dlq.exchange";
    
    // 预定义队列名称
    public static final String HIGH_PRIORITY_QUEUE = "message.queue.high-priority";
    public static final String NORMAL_QUEUE = "message.queue.normal";
    public static final String DELAY_QUEUE = "message.queue.delay";
    public static final String DLQ_QUEUE = "message.queue.dlq";
    
    @PostConstruct
    public void initializeQueues() {
        logger.info("开始初始化预定义队列配置");
        
        try {
            // 测试RabbitMQ连接
            if (!testRabbitMQConnectionWithRetry()) {
                logger.error("RabbitMQ连接测试失败，跳过队列初始化");
                return;
            }

            // 初始化交换机
            initializeExchanges();
            
            // 初始化预定义队列
            initializePredefinedQueues();
            
            logger.info("预定义队列配置初始化完成: totalQueues={}", predefinedQueues.size());
            
        } catch (Exception e) {
            logger.error("队列配置初始化失败", e);
        }
    }
    
    /**
     * 初始化交换机
     */
    private void initializeExchanges() {
        try {
            // 主交换机
            TopicExchange messageExchange = ExchangeBuilder
                .topicExchange(MESSAGE_EXCHANGE)
                .durable(true)
                .build();
            rabbitAdmin.declareExchange(messageExchange);
            
            // 延时交换机
            TopicExchange delayExchange = ExchangeBuilder
                .topicExchange(DELAY_EXCHANGE)
                .durable(true)
                .build();
            rabbitAdmin.declareExchange(delayExchange);
            
            // 死信交换机
            TopicExchange dlqExchange = ExchangeBuilder
                .topicExchange(DLQ_EXCHANGE)
                .durable(true)
                .build();
            rabbitAdmin.declareExchange(dlqExchange);
            
            logger.info("交换机初始化完成");
            
        } catch (Exception e) {
            logger.error("交换机初始化失败", e);
            throw e;
        }
    }
    
    /**
     * 初始化预定义队列
     */
    private void initializePredefinedQueues() {
        // 初始化高优先级队列
        initializeHighPriorityQueue();
        
        // 初始化普通队列
        initializeNormalQueue();
        
        // 初始化延时队列
        initializeDelayQueue();
        
        // 初始化死信队列
        initializeDlqQueue();
    }
    
    /**
     * 初始化高优先级队列
     */
    private void initializeHighPriorityQueue() {
        try {
            Map<String, Object> args = new HashMap<>();
            args.put("x-max-priority", 10);
            args.put("x-message-ttl", messageTtl);
            args.put("x-max-length", maxQueueLength);
            args.put("x-dead-letter-exchange", DLQ_EXCHANGE);
            args.put("x-dead-letter-routing-key", "dlq");
            
            createPredefinedQueue(QueueType.HIGH_PRIORITY, HIGH_PRIORITY_QUEUE, 
                                MESSAGE_EXCHANGE, "high-priority", args, 3);
            
            logger.info("高优先级队列初始化完成");
            
        } catch (Exception e) {
            logger.error("高优先级队列初始化失败", e);
            throw e;
        }
    }
    
    /**
     * 初始化普通队列
     */
    private void initializeNormalQueue() {
        try {
            Map<String, Object> args = new HashMap<>();
            args.put("x-message-ttl", messageTtl);
            args.put("x-max-length", maxQueueLength);
            args.put("x-dead-letter-exchange", DLQ_EXCHANGE);
            args.put("x-dead-letter-routing-key", "dlq");
            
            createPredefinedQueue(QueueType.NORMAL, NORMAL_QUEUE, 
                                MESSAGE_EXCHANGE, "normal", args, 5);
            
            logger.info("普通队列初始化完成");
            
        } catch (Exception e) {
            logger.error("普通队列初始化失败", e);
            throw e;
        }
    }
    
    /**
     * 初始化延时队列
     */
    private void initializeDelayQueue() {
        try {
            Map<String, Object> args = new HashMap<>();
            args.put("x-dead-letter-exchange", MESSAGE_EXCHANGE);
            args.put("x-dead-letter-routing-key", "normal");
            
            createPredefinedQueue(QueueType.DELAY, DELAY_QUEUE, 
                                DELAY_EXCHANGE, "delay", args, 2);
            
            logger.info("延时队列初始化完成");
            
        } catch (Exception e) {
            logger.error("延时队列初始化失败", e);
            throw e;
        }
    }
    
    /**
     * 初始化死信队列
     */
    private void initializeDlqQueue() {
        try {
            Map<String, Object> args = new HashMap<>();
            // 死信队列不设置TTL，需要人工处理
            
            createPredefinedQueue(QueueType.DLQ, DLQ_QUEUE, 
                                DLQ_EXCHANGE, "dlq", args, 1);
            
            logger.info("死信队列初始化完成");
            
        } catch (Exception e) {
            logger.error("死信队列初始化失败", e);
            throw e;
        }
    }
    
    /**
     * 创建预定义队列
     */
    private void createPredefinedQueue(QueueType queueType, String queueName, 
                                     String exchangeName, String routingKey, 
                                     Map<String, Object> args, int concurrency) {
        int retryCount = 0;
        int maxRetries = 3;
        long retryDelay = 3000; // 3秒重试间隔
        
        while (retryCount < maxRetries) {
            try {
                // 检查队列是否已存在
                if (queueExists(queueName)) {
                    logger.info("队列已存在，跳过创建: {}", queueName);
                    // 仍然需要保存配置
                    saveQueueConfig(queueType, queueName, exchangeName, routingKey, concurrency, args);
                    return;
                }
                
                // 创建队列
                Queue queue = QueueBuilder
                    .durable(queueName)
                    .withArguments(args)
                    .build();
                rabbitAdmin.declareQueue(queue);
                
                // 绑定队列到交换机
                Binding binding = BindingBuilder
                    .bind(queue)
                    .to(new TopicExchange(exchangeName))
                    .with(routingKey);
                rabbitAdmin.declareBinding(binding);
                
                // 保存队列配置
                saveQueueConfig(queueType, queueName, exchangeName, routingKey, concurrency, args);
                
                logger.info("预定义队列创建成功: queueType={}, queueName={}", queueType, queueName);
                return;
                
            } catch (Exception e) {
                logger.error("预定义队列创建失败 (尝试 {}): queueType={}, queueName={}, error={}", 
                            retryCount + 1, queueType, queueName, e.getMessage(), e);
                retryCount++;
                
                if (retryCount < maxRetries) {
                    try {
                        logger.info("{}秒后重试队列创建...", retryDelay/1000);
                        Thread.sleep(retryDelay);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        logger.error("队列创建重试中断", ie);
                        return;
                    }
                }
            }
        }
        
        logger.error("达到最大重试次数，队列创建失败: queueType={}, queueName={}", queueType, queueName);
        throw new RuntimeException("队列创建失败: " + queueName);
    }
    
    /**
     * 保存队列配置
     */
    private void saveQueueConfig(QueueType queueType, String queueName, String exchangeName, 
                               String routingKey, int concurrency, Map<String, Object> args) {
        QueueConfig config = new QueueConfig();
        config.setQueueType(queueType);
        config.setQueueName(queueName);
        config.setExchangeName(exchangeName);
        config.setRoutingKey(routingKey);
        config.setConcurrency(concurrency);
        config.setProperties(args != null ? new HashMap<>(args) : new HashMap<>());
        
        predefinedQueues.put(queueType, config);
        
        logger.debug("队列配置已保存: queueType={}, queueName={}", queueType, queueName);
    }

    /**
     * 根据队列类型获取队列配置
     */
    public QueueConfig getQueueConfigByType(QueueType queueType) {
        return predefinedQueues.get(queueType);
    }

    /**
     * 获取所有预定义队列配置
     */
    public Map<QueueType, QueueConfig> getAllPredefinedQueues() {
        return new HashMap<>(predefinedQueues);
    }

    /**
     * 获取队列名称（根据队列类型）
     */
    public String getQueueNameByType(QueueType queueType) {
        QueueConfig config = predefinedQueues.get(queueType);
        return config != null ? config.getQueueName() : null;
    }

    /**
     * 获取路由键（根据队列类型）
     */
    public String getRoutingKeyByType(QueueType queueType) {
        QueueConfig config = predefinedQueues.get(queueType);
        return config != null ? config.getRoutingKey() : null;
    }

    /**
     * 获取交换机名称（根据队列类型）
     */
    public String getExchangeNameByType(QueueType queueType) {
        QueueConfig config = predefinedQueues.get(queueType);
        return config != null ? config.getExchangeName() : null;
    }

    /**
     * 带重试机制的RabbitMQ连接测试
     */
    private boolean testRabbitMQConnectionWithRetry() {
        int retryCount = 0;
        int maxRetries = 3;
        long retryDelay = 5000; // 5秒重试间隔

        while (retryCount < maxRetries) {
            try {
                boolean connected = testRabbitMQConnection();
                if (connected) {
                    return true;
                }
                logger.info("RabbitMQ连接测试失败，{}秒后重试...", retryDelay/1000);
                Thread.sleep(retryDelay);
                retryCount++;
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                logger.error("RabbitMQ连接测试中断", e);
                return false;
            } catch (Exception e) {
                logger.error("RabbitMQ连接测试异常: {}", e.getMessage(), e);
                try {
                    Thread.sleep(retryDelay);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    return false;
                }
                retryCount++;
            }
        }
        logger.error("RabbitMQ连接测试失败，已重试{}次", maxRetries);
        return false;
    }

    /**
     * 测试RabbitMQ连接
     */
    private boolean testRabbitMQConnection() {
        try {
            // 尝试执行一个简单的操作来测试连接
            rabbitTemplate.execute(channel -> {
                channel.getConnection().isOpen();
                return true;
            });
            logger.debug("RabbitMQ连接测试成功");
            return true;
        } catch (Exception e) {
            logger.error("RabbitMQ连接测试失败: ", e);
            return false;
        }
    }

    /**
     * 检查队列是否存在
     */
    public boolean queueExists(String queueName) {
        int retryCount = 0;
        int maxRetries = 3;

        while (retryCount < maxRetries) {
            try {
                return rabbitAdmin.getQueueProperties(queueName) != null;
            } catch (Exception e) {
                logger.error("检查队列存在状态失败 (尝试 {}): queueName={}, error={}",
                           retryCount + 1, queueName, e.getMessage(), e);
                retryCount++;

                if (retryCount < maxRetries) {
                    try {
                        long retryDelay = 2000; // 2秒重试间隔
                        logger.info("{}秒后重试检查队列存在状态...", retryDelay/1000);
                        Thread.sleep(retryDelay);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        logger.error("检查队列存在状态重试中断", ie);
                        return false;
                    }
                }
            }
        }

        return false;
    }

    /**
     * 队列配置类
     */
    public static class QueueConfig {
        private QueueType queueType;
        private String queueName;
        private String exchangeName;
        private String routingKey;
        private int concurrency = 5;
        private Map<String, Object> properties = new HashMap<>();

        // Getters and Setters
        public QueueType getQueueType() {
            return queueType;
        }

        public void setQueueType(QueueType queueType) {
            this.queueType = queueType;
        }

        public String getQueueName() {
            return queueName;
        }

        public void setQueueName(String queueName) {
            this.queueName = queueName;
        }

        public String getExchangeName() {
            return exchangeName;
        }

        public void setExchangeName(String exchangeName) {
            this.exchangeName = exchangeName;
        }

        public String getRoutingKey() {
            return routingKey;
        }

        public void setRoutingKey(String routingKey) {
            this.routingKey = routingKey;
        }

        public int getConcurrency() {
            return concurrency;
        }

        public void setConcurrency(int concurrency) {
            this.concurrency = concurrency;
        }

        public Map<String, Object> getProperties() {
            return properties;
        }

        public void setProperties(Map<String, Object> properties) {
            this.properties = properties;
        }
    }

    /**
     * 队列类型枚举
     */
    public enum QueueType {
        HIGH_PRIORITY,  // 高优先级队列
        NORMAL,         // 普通队列
        DELAY,          // 延时队列
        DLQ             // 死信队列
    }
}
