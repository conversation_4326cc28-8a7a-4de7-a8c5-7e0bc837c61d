package com.lubansoft.msg.model.controller.admin;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lubansoft.base.common.model.ResponseEntity;
import com.lubansoft.msg.common.enums.MessageStatus;
import com.lubansoft.msg.common.model.ChannelType;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import com.lubansoft.msg.model.repository.entity.MessageRecord;
import com.lubansoft.msg.model.repository.mapper.MessageRecordMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 消息记录控制器
 */
@RestController
@RequestMapping("/records")
@Tag(name = "消息记录", description = "消息发送记录查询接口")
public class MessageRecordController {
    
    private static final Logger logger = LoggerFactory.getLogger(MessageRecordController.class);
    
    @Autowired
    private MessageRecordMapper recordRepository;
    
    @Operation(summary = "获取消息记录", description = "根据消息ID获取消息记录")
    @GetMapping("/{messageId}")
    public ResponseEntity<MessageRecord> getMessageRecord(
            @Parameter(description = "消息ID") @PathVariable String messageId) {
        
        logger.info("获取消息记录: messageId={}", messageId);
        
        try {
            List<MessageRecord> records = recordRepository.findByMessageId(messageId);
            if (records.isEmpty()) {
                logger.info("消息记录不存在: {}", messageId);
                return ResponseEntity.fail(404, "消息记录不存在");
            }

            // 返回第一条记录（如果有多条记录，可能需要根据业务逻辑调整）
            MessageRecord record = records.get(0);
            
            return ResponseEntity.success(record);
            
        } catch (Exception e) {
            logger.error("获取消息记录失败: messageId={}", messageId, e);
            return ResponseEntity.fail(500, "获取消息记录失败");
        }
    }
    
    @Operation(summary = "根据请求ID查询消息记录", description = "获取指定请求ID的所有消息记录")
    @GetMapping("/request/{requestId}")
    public ResponseEntity<List<MessageRecord>> getMessageRecordsByRequestId(
            @Parameter(description = "请求ID") @PathVariable String requestId) {
        
        logger.info("根据请求ID查询消息记录: requestId={}", requestId);
        
        try {
            List<MessageRecord> records = recordRepository.findByRequestId(requestId);
            if (!records.isEmpty()) {
                logger.info("查询消息记录成功: requestId={}, count={}", requestId, records.size());
                return ResponseEntity.success(records);
            } else {
                logger.info("未找到消息记录: requestId={}", requestId);
                return ResponseEntity.success(List.of());
            }
            
        } catch (Exception e) {
            logger.error("根据请求ID查询消息记录失败: requestId={}", requestId, e);
            return ResponseEntity.fail(500, "查询消息记录失败");
        }
    }
    
    @Operation(summary = "分页查询消息记录", description = "分页查询消息记录列表")
    @GetMapping
    public ResponseEntity<IPage<MessageRecord>> getMessageRecords(
            @Parameter(description = "页码") @RequestParam(defaultValue = "0") int pageNum,
            @Parameter(description = "页大小") @RequestParam(defaultValue = "20") int pageSize,
            @Parameter(description = "接收者") @RequestParam(required = false) String receiver,
            @Parameter(description = "渠道类型") @RequestParam(required = false) String channel,
            @Parameter(description = "消息状态") @RequestParam(required = false) String status,
            @Parameter(description = "模板ID") @RequestParam(required = false) String templateId) {
        
        logger.info("分页查询消息记录: pageNum={}, size={}, receiver={}, channel={}, status={}, templateId={}",
                pageNum, pageSize, receiver, channel, status, templateId);
        
        try {
            Page<MessageRecord> mybatisPage = 
                new Page<>(pageNum + 1, pageSize);
            IPage<MessageRecord> records;
            
            if (receiver != null) {
                records = recordRepository.findByReceiver(receiver, mybatisPage);
            } else if (channel != null) {
                records = recordRepository.findByChannel(channel, mybatisPage);
            } else if (status != null) {
                records = recordRepository.findByStatus(status, mybatisPage);
            } else if (templateId != null) {
                records = recordRepository.findByTemplateId(templateId, mybatisPage);
            } else {
                records = recordRepository.findAll(mybatisPage);
            }
            
            logger.info("分页查询消息记录成功: totalElements={}", records.getTotal());
            return ResponseEntity.success(records);
            
        } catch (Exception e) {
            logger.error("分页查询消息记录失败", e);
            return ResponseEntity.fail(500, "分页查询消息记录失败");
        }
    }
    
    @Operation(summary = "根据时间范围查询消息记录", description = "查询指定时间范围内的消息记录")
    @GetMapping("/time-range")
    public ResponseEntity<IPage<MessageRecord>> getMessageRecordsByTimeRange(
            @Parameter(description = "开始时间") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startTime,
            @Parameter(description = "结束时间") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime,
            @Parameter(description = "页码") @RequestParam(defaultValue = "0") int pageNum,
            @Parameter(description = "页大小") @RequestParam(defaultValue = "20") int pageSize) {
        
        logger.info("根据时间范围查询消息记录: startTime={}, endTime={}, pageNum={}, size={}",
                   startTime, endTime, pageNum, pageSize);
        
        try {
            Page<MessageRecord> mybatisPage = 
                new Page<>(pageNum + 1, pageSize);
            IPage<MessageRecord> records = 
                recordRepository.findByCreatedAtBetween(startTime, endTime, mybatisPage);
            
            logger.info("根据时间范围查询消息记录成功: totalElements={}", records.getTotal());
            return ResponseEntity.success(records);
            
        } catch (Exception e) {
            logger.error("根据时间范围查询消息记录失败", e);
            return ResponseEntity.fail(500, "根据时间范围查询消息记录失败");
        }
    }
    
    @Operation(summary = "获取消息统计信息", description = "获取消息发送统计信息")
    @GetMapping("/statistics")
    public ResponseEntity<Map<String, Object>> getMessageStatistics(
            @Parameter(description = "开始时间") @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startTime,
            @Parameter(description = "结束时间") @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime) {
        
        logger.info("获取消息统计信息: startTime={}, endTime={}", startTime, endTime);
        
        try {
            // 如果没有指定时间范围，默认查询最近24小时
            if (startTime == null) {
                startTime = LocalDateTime.now().minusDays(1);
            }
            if (endTime == null) {
                endTime = LocalDateTime.now();
            }
            
            // 总消息数
            long totalCount = recordRepository.countByCreatedAtBetween(startTime, endTime);
            
            // 各状态消息数
            long pendingCount = recordRepository.countByStatus("PENDING");
            long sentCount = recordRepository.countByStatus("SENT");
            long failedCount = recordRepository.countByStatus("FAILED");
            
            // 各渠道消息数
            long smsCount = recordRepository.countByChannel(null);
            long emailCount = recordRepository.countByChannel(null);
            long pushCount = recordRepository.countByChannel(null);
            
            Map<String, Object> statistics = Map.of(
                "totalCount", totalCount,
                "statusStatistics", Map.of(
                    "pending", pendingCount,
                    "sent", sentCount,
                    "failed", failedCount
                ),
                "channelStatistics", Map.of(
                    "sms", smsCount,
                    "email", emailCount,
                    "push", pushCount
                ),
                "timeRange", Map.of(
                    "startTime", startTime,
                    "endTime", endTime
                )
            );
            
            logger.info("获取消息统计信息成功: totalCount={}", totalCount);
            return ResponseEntity.success(statistics);
            
        } catch (Exception e) {
            logger.error("获取消息统计信息失败", e);
            return ResponseEntity.fail(500, "获取消息统计信息失败");
        }
    }
    
    @Operation(summary = "获取失败消息列表", description = "获取发送失败的消息列表")
    @GetMapping("/failed")
    public ResponseEntity<IPage<MessageRecord>> getFailedMessages(
            @Parameter(description = "页码") @RequestParam(defaultValue = "0") int pageNum,
            @Parameter(description = "页大小") @RequestParam(defaultValue = "20") int pageSize) {
        
        logger.info("获取失败消息列表: pageNum={}, size={}", pageNum, pageSize);
        
        try {
            Page<MessageRecord> mybatisPage = 
                new Page<>(pageNum + 1, pageSize);
            IPage<MessageRecord> failedMessages = 
                recordRepository.findByStatus("FAILED", mybatisPage);
            
            logger.info("获取失败消息列表成功: totalElements={}", failedMessages.getTotal());
            return ResponseEntity.success(failedMessages);
            
        } catch (Exception e) {
            logger.error("获取失败消息列表失败", e);
            return ResponseEntity.fail(500, "获取失败消息列表失败");
        }
    }
    
    @Operation(summary = "获取可重试消息列表", description = "获取可以重试的失败消息列表")
    @GetMapping("/retryable")
    public ResponseEntity<List<MessageRecord>> getRetryableMessages(
            @Parameter(description = "最大重试次数") @RequestParam(defaultValue = "3") int maxRetryCount) {
        
        logger.info("获取可重试消息列表: maxRetryCount={}", maxRetryCount);
        
        try {
            List<MessageRecord> retryableMessages = recordRepository.findRetryableMessages("FAILED", maxRetryCount);
            
            logger.info("获取可重试消息列表成功: count={}", retryableMessages.size());
            return ResponseEntity.success(retryableMessages);
            
        } catch (Exception e) {
            logger.error("获取可重试消息列表失败", e);
            return ResponseEntity.fail(500, "获取可重试消息列表失败");
        }
    }
}