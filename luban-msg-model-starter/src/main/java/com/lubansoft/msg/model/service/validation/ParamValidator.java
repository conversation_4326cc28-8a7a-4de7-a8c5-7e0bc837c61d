package com.lubansoft.msg.model.service.validation;

import com.lubansoft.msg.model.repository.entity.MessageTemplate;
import com.lubansoft.msg.model.repository.repo.MessageTemplateRepo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Optional;

@Service
public class ParamValidator implements ValidContext.ValidHandle {

    @Autowired
    private MessageTemplateRepo messageTemplateRepo;

    @Override
    public ValidResult valid(ValidContext validContext) {
        String templateId = validContext.getTemplateId();
        Optional<MessageTemplate> optionalMessageTemplate = messageTemplateRepo.findById(templateId);
        if(optionalMessageTemplate.isPresent()) {
            return ValidResult.success();
        }
        return ValidResult.fail("模板不存在");
    }
}
