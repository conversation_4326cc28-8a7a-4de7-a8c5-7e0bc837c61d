package com.lubansoft.msg.model.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lubansoft.msg.model.repository.entity.ReceiverSub;
import com.lubansoft.msg.model.repository.repo.ReceiverSubRepo;
import com.lubansoft.msg.model.service.ReceiverSubService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户订阅设置服务实现类
 */
@Service
public class ReceiverSubServiceImpl implements ReceiverSubService {

    @Autowired
    private ReceiverSubRepo receiverSubRepo;

    @Override
    public ReceiverSub save(ReceiverSub receiverSub) {
        receiverSub.setCreatedAt(LocalDateTime.now());
        receiverSub.setUpdatedAt(LocalDateTime.now());
        receiverSubRepo.insert(receiverSub);
        return receiverSub;
    }

    @Override
    public ReceiverSub update(ReceiverSub receiverSub) {
        receiverSub.setUpdatedAt(LocalDateTime.now());
        receiverSubRepo.updateById(receiverSub);
        return receiverSub;
    }

    @Override
    public boolean deleteById(Long id) {
        return receiverSubRepo.deleteById(id) > 0;
    }

    @Override
    public ReceiverSub findById(Long id) {
        return receiverSubRepo.selectById(id);
    }

    @Override
    public List<ReceiverSub> findByReceiverId(String receiverId) {
        return receiverSubRepo.findByReceiverId(receiverId);
    }

    @Override
    public ReceiverSub findByReceiverIdAndChannel(String receiverId, String channel) {
        return receiverSubRepo.findByReceiverIdAndChannel(receiverId, channel);
    }

    @Override
    public IPage<ReceiverSub> findByPage(Page<ReceiverSub> page, String receiverId, String channel) {
        QueryWrapper<ReceiverSub> queryWrapper = new QueryWrapper<>();
        if (receiverId != null && !receiverId.isEmpty()) {
            queryWrapper.eq("receiver_id", receiverId);
        }
        if (channel != null && !channel.isEmpty()) {
            queryWrapper.eq("channel", channel);
        }
        return receiverSubRepo.findByPage(page, queryWrapper);
    }

    @Override
    public boolean updateSubStatus(String receiverId, String channel, Boolean isSub) {
        return receiverSubRepo.updateSubStatus(receiverId, channel, isSub) > 0;
    }
}