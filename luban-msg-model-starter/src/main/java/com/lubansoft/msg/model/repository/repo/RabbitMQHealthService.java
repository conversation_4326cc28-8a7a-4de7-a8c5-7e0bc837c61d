package com.lubansoft.msg.model.repository.repo;

import com.lubansoft.msg.model.service.QueueConfigManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.core.RabbitAdmin;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.Properties;

/**
 * RabbitMQ健康检查服务
 */
@Service
public class RabbitMQHealthService {
    
    private static final Logger logger = LoggerFactory.getLogger(RabbitMQHealthService.class);
    
    @Autowired
    private ConnectionFactory connectionFactory;
    
    @Autowired
    private RabbitAdmin rabbitAdmin;
    
    @Autowired
    private QueueConfigManager queueConfigManager;
    
    /**
     * 检查RabbitMQ连接健康状态
     */
    public HealthStatus checkConnectionHealth() {
        try {
            // 检查连接是否可用
            connectionFactory.createConnection().close();
            
            return HealthStatus.healthy("RabbitMQ连接正常");
            
        } catch (Exception e) {
            logger.error("RabbitMQ连接检查失败", e);
            return HealthStatus.unhealthy("RabbitMQ连接失败: " + e.getMessage());
        }
    }
    
    /**
     * 检查队列健康状态
     */
    public Map<String, HealthStatus> checkQueuesHealth() {
        Map<String, HealthStatus> queueHealthMap = new HashMap<>();
        
        Map<QueueConfigManager.QueueType, QueueConfigManager.QueueConfig> queueConfigs = queueConfigManager.getAllPredefinedQueues();

        for (Map.Entry<QueueConfigManager.QueueType, QueueConfigManager.QueueConfig> entry : queueConfigs.entrySet()) {
            QueueConfigManager.QueueType queueType = entry.getKey();
            QueueConfigManager.QueueConfig config = entry.getValue();
            
            try {
                Properties queueProperties = rabbitAdmin.getQueueProperties(config.getQueueName());
                
                if (queueProperties != null) {
                    int messageCount = getIntProperty(queueProperties, "QUEUE_MESSAGE_COUNT");
                    int consumerCount = getIntProperty(queueProperties, "QUEUE_CONSUMER_COUNT");
                    
                    // 检查队列是否堆积过多消息
                    if (messageCount > 10000) {
                        queueHealthMap.put(queueType.name(), HealthStatus.warning(
                            String.format("队列消息堆积: messageCount=%d, consumerCount=%d",
                                        messageCount, consumerCount)));
                    } else if (consumerCount == 0 && messageCount > 0) {
                        queueHealthMap.put(queueType.name(), HealthStatus.warning(
                            String.format("队列无消费者: messageCount=%d", messageCount)));
                    } else {
                        queueHealthMap.put(queueType.name(), HealthStatus.healthy(
                            String.format("队列正常: messageCount=%d, consumerCount=%d",
                                        messageCount, consumerCount)));
                    }
                } else {
                    queueHealthMap.put(queueType.name(), HealthStatus.unhealthy("队列不存在"));
                }

            } catch (Exception e) {
                logger.error("检查队列健康状态失败: queueType={}", queueType, e);
                queueHealthMap.put(queueType.name(), HealthStatus.unhealthy("队列检查异常: " + e.getMessage()));
            }
        }
        
        return queueHealthMap;
    }
    
    /**
     * 获取RabbitMQ整体健康状态
     */
    public OverallHealthStatus getOverallHealth() {
        OverallHealthStatus overallStatus = new OverallHealthStatus();
        
        // 检查连接健康状态
        HealthStatus connectionHealth = checkConnectionHealth();
        overallStatus.setConnectionHealth(connectionHealth);
        
        // 检查队列健康状态
        Map<String, HealthStatus> queueHealthMap = checkQueuesHealth();
        overallStatus.setQueueHealthMap(queueHealthMap);
        
        // 计算整体状态
        boolean hasUnhealthy = !connectionHealth.isHealthy() || 
                              queueHealthMap.values().stream().anyMatch(h -> !h.isHealthy());
        boolean hasWarning = connectionHealth.getStatus() == HealthStatus.Status.WARNING ||
                            queueHealthMap.values().stream().anyMatch(h -> h.getStatus() == HealthStatus.Status.WARNING);
        
        if (hasUnhealthy) {
            overallStatus.setOverallStatus(HealthStatus.Status.UNHEALTHY);
            overallStatus.setOverallMessage("RabbitMQ存在不健康的组件");
        } else if (hasWarning) {
            overallStatus.setOverallStatus(HealthStatus.Status.WARNING);
            overallStatus.setOverallMessage("RabbitMQ存在警告状态的组件");
        } else {
            overallStatus.setOverallStatus(HealthStatus.Status.HEALTHY);
            overallStatus.setOverallMessage("RabbitMQ整体状态正常");
        }
        
        return overallStatus;
    }
    
    /**
     * 获取RabbitMQ统计信息
     */
    public Map<String, Object> getStatistics() {
        Map<String, Object> stats = new HashMap<>();
        
        try {
            Map<QueueConfigManager.QueueType, QueueConfigManager.QueueConfig> queueConfigs = queueConfigManager.getAllPredefinedQueues();
            
            int totalQueues = queueConfigs.size();
            int totalMessages = 0;
            int totalConsumers = 0;
            int healthyQueues = 0;
            int warningQueues = 0;
            int unhealthyQueues = 0;
            
            for (QueueConfigManager.QueueConfig config : queueConfigs.values()) {
                try {
                    Properties queueProperties = rabbitAdmin.getQueueProperties(config.getQueueName());
                    
                    if (queueProperties != null) {
                        int messageCount = getIntProperty(queueProperties, "QUEUE_MESSAGE_COUNT");
                        int consumerCount = getIntProperty(queueProperties, "QUEUE_CONSUMER_COUNT");
                        
                        totalMessages += messageCount;
                        totalConsumers += consumerCount;
                        
                        if (messageCount > 10000) {
                            warningQueues++;
                        } else if (consumerCount == 0 && messageCount > 0) {
                            warningQueues++;
                        } else {
                            healthyQueues++;
                        }
                    } else {
                        unhealthyQueues++;
                    }
                } catch (Exception e) {
                    unhealthyQueues++;
                }
            }
            
            stats.put("totalQueues", totalQueues);
            stats.put("totalMessages", totalMessages);
            stats.put("totalConsumers", totalConsumers);
            stats.put("healthyQueues", healthyQueues);
            stats.put("warningQueues", warningQueues);
            stats.put("unhealthyQueues", unhealthyQueues);
            stats.put("connectionStatus", checkConnectionHealth().isHealthy() ? "UP" : "DOWN");
            
        } catch (Exception e) {
            logger.error("获取RabbitMQ统计信息失败", e);
            stats.put("error", "统计信息获取失败: " + e.getMessage());
        }
        
        return stats;
    }
    
    /**
     * 重置队列（清空消息）
     */
    public boolean resetQueue(String queueName) {
        try {
            rabbitAdmin.purgeQueue(queueName);
            logger.info("队列重置成功: queueName={}", queueName);
            return true;
        } catch (Exception e) {
            logger.error("队列重置失败: queueName={}", queueName, e);
            return false;
        }
    }
    
    /**
     * 获取整数属性值
     */
    private int getIntProperty(Properties properties, String key) {
        Object value = properties.get(key);
        if (value instanceof Number) {
            return ((Number) value).intValue();
        }
        return 0;
    }
    
    /**
     * 健康状态类
     */
    public static class HealthStatus {
        public enum Status {
            HEALTHY, WARNING, UNHEALTHY
        }
        
        private Status status;
        private String message;
        private long timestamp;
        
        public HealthStatus(Status status, String message) {
            this.status = status;
            this.message = message;
            this.timestamp = System.currentTimeMillis();
        }
        
        public static HealthStatus healthy(String message) {
            return new HealthStatus(Status.HEALTHY, message);
        }
        
        public static HealthStatus warning(String message) {
            return new HealthStatus(Status.WARNING, message);
        }
        
        public static HealthStatus unhealthy(String message) {
            return new HealthStatus(Status.UNHEALTHY, message);
        }
        
        public boolean isHealthy() {
            return status == Status.HEALTHY;
        }
        
        // Getters and Setters
        public Status getStatus() {
            return status;
        }
        
        public void setStatus(Status status) {
            this.status = status;
        }
        
        public String getMessage() {
            return message;
        }
        
        public void setMessage(String message) {
            this.message = message;
        }
        
        public long getTimestamp() {
            return timestamp;
        }
        
        public void setTimestamp(long timestamp) {
            this.timestamp = timestamp;
        }
    }
    
    /**
     * 整体健康状态类
     */
    public static class OverallHealthStatus {
        private HealthStatus.Status overallStatus;
        private String overallMessage;
        private HealthStatus connectionHealth;
        private Map<String, HealthStatus> queueHealthMap;
        private long timestamp;
        
        public OverallHealthStatus() {
            this.timestamp = System.currentTimeMillis();
        }
        
        // Getters and Setters
        public HealthStatus.Status getOverallStatus() {
            return overallStatus;
        }
        
        public void setOverallStatus(HealthStatus.Status overallStatus) {
            this.overallStatus = overallStatus;
        }
        
        public String getOverallMessage() {
            return overallMessage;
        }
        
        public void setOverallMessage(String overallMessage) {
            this.overallMessage = overallMessage;
        }
        
        public HealthStatus getConnectionHealth() {
            return connectionHealth;
        }
        
        public void setConnectionHealth(HealthStatus connectionHealth) {
            this.connectionHealth = connectionHealth;
        }
        
        public Map<String, HealthStatus> getQueueHealthMap() {
            return queueHealthMap;
        }
        
        public void setQueueHealthMap(Map<String, HealthStatus> queueHealthMap) {
            this.queueHealthMap = queueHealthMap;
        }
        
        public long getTimestamp() {
            return timestamp;
        }
        
        public void setTimestamp(long timestamp) {
            this.timestamp = timestamp;
        }
    }
}