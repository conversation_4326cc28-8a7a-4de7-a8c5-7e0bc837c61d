package com.lubansoft.msg.model.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * 模板渠道映射配置DTO
 */
@Data
@Schema(description = "模板渠道映射配置")
public class TemplateChannelMappingDTO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    @Schema(description = "映射配置ID")
    private String mappingId;
    
    @Schema(description = "模板ID", required = true)
    @NotBlank(message = "模板ID不能为空")
    private String templateId;
    
    @Schema(description = "渠道类型", required = true)
    @NotBlank(message = "渠道类型不能为空")
    private String channelType;
    
    @Schema(description = "参数映射配置", example = "{\"code\":\"channelCode\", \"name\":\"channelName\"}")
    private Map<String, String> parameterMapping;
    
    @Schema(description = "渠道特定的模板内容")
    private String channelTemplateContent;
    
    @Schema(description = "渠道特定的模板变量")
    private String channelTemplateVariables;
    

    
    @Schema(description = "扩展配置")
    private Map<String, Object> extendedConfig;
    
    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;
    
    @Schema(description = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;
    
    @Schema(description = "创建者")
    private String createdBy;
    
    @Schema(description = "更新者")
    private String updatedBy;
    
    /**
     * 创建映射配置请求DTO
     */
    @Data
    @Schema(description = "创建模板渠道映射配置请求")
    public static class CreateRequest implements Serializable {
        
        private static final long serialVersionUID = 1L;
        
        @Schema(description = "模板ID", required = true)
        @NotBlank(message = "模板ID不能为空")
        private String templateId;
        
        @Schema(description = "渠道ID", required = true)
        @NotBlank(message = "渠道ID不能为空")
        private String channelId;
        
        @Schema(description = "渠道类型", required = true)
        @NotBlank(message = "渠道类型不能为空")
        private String channelType;
        
        @Schema(description = "映射名称")
        @Size(max = 255, message = "映射名称长度不能超过255个字符")
        private String mappingName;
        
        @Schema(description = "参数映射配置", example = "{\"code\":\"channelCode\", \"name\":\"channelName\"}")
        private Map<String, String> parameterMapping;
        
        @Schema(description = "渠道特定的模板内容")
        private String channelTemplateContent;
        
        @Schema(description = "渠道特定的模板变量")
        private String channelTemplateVariables;
        
        @Schema(description = "是否启用")
        private Boolean enabled = true;
        
        @Schema(description = "优先级，数字越小优先级越高")
        private Integer priority = 0;
        
        @Schema(description = "映射描述")
        @Size(max = 500, message = "描述长度不能超过500个字符")
        private String description;
        
        @Schema(description = "扩展配置")
        private Map<String, Object> extendedConfig;
    }
    
    /**
     * 更新映射配置请求DTO
     */
    @Data
    @Schema(description = "更新模板渠道映射配置请求")
    public static class UpdateRequest implements Serializable {
        
        private static final long serialVersionUID = 1L;
        
        @Schema(description = "模板ID", required = true)
        @NotBlank(message = "模板ID不能为空")
        private String templateId;
        
        @Schema(description = "渠道ID", required = true)
        @NotBlank(message = "渠道ID不能为空")
        private String channelId;
        
        @Schema(description = "渠道类型", required = true)
        @NotBlank(message = "渠道类型不能为空")
        private String channelType;
        
        @Schema(description = "映射名称")
        @Size(max = 255, message = "映射名称长度不能超过255个字符")
        private String mappingName;
        
        @Schema(description = "参数映射配置", example = "{\"code\":\"channelCode\", \"name\":\"channelName\"}")
        private Map<String, String> parameterMapping;
        
        @Schema(description = "渠道特定的模板内容")
        private String channelTemplateContent;
        
        @Schema(description = "渠道特定的模板变量")
        private String channelTemplateVariables;
        
        @Schema(description = "是否启用")
        private Boolean enabled;
        
        @Schema(description = "优先级，数字越小优先级越高")
        private Integer priority;
        
        @Schema(description = "映射描述")
        @Size(max = 500, message = "描述长度不能超过500个字符")
        private String description;
        
        @Schema(description = "扩展配置")
        private Map<String, Object> extendedConfig;
    }
    
    /**
     * 批量操作请求DTO
     */
    @Data
    @Schema(description = "批量操作请求")
    public static class BatchOperationRequest implements Serializable {
        
        private static final long serialVersionUID = 1L;
        
        @Schema(description = "映射ID列表", required = true)
        @NotNull(message = "映射ID列表不能为空")
        private java.util.List<String> mappingIds;
        
        @Schema(description = "操作类型：enable-启用, disable-禁用, delete-删除", required = true)
        @NotBlank(message = "操作类型不能为空")
        private String operation;
        
        @Schema(description = "优先级（仅当操作类型为updatePriority时使用）")
        private Integer priority;
    }
    
    /**
     * 查询条件DTO
     */
    @Data
    @Schema(description = "模板渠道映射查询条件")
    public static class QueryCondition implements Serializable {
        
        private static final long serialVersionUID = 1L;
        
        @Schema(description = "模板ID")
        private String templateId;
        
        @Schema(description = "渠道ID")
        private String channelId;
        
        @Schema(description = "渠道类型")
        private String channelType;
        
        @Schema(description = "映射名称关键词")
        private String mappingName;
        
        @Schema(description = "启用状态")
        private Boolean enabled;
        
        @Schema(description = "页码，从1开始")
        private Integer pageNum = 1;
        
        @Schema(description = "页大小")
        private Integer pageSize = 10;
    }
    
    /**
     * 参数映射应用请求DTO
     */
    @Data
    @Schema(description = "参数映射应用请求")
    public static class ParameterMappingRequest implements Serializable {
        
        private static final long serialVersionUID = 1L;
        
        @Schema(description = "模板ID", required = true)
        @NotBlank(message = "模板ID不能为空")
        private String templateId;
        
        @Schema(description = "渠道ID", required = true)
        @NotBlank(message = "渠道ID不能为空")
        private String channelId;
        
        @Schema(description = "模板参数", required = true)
        @NotNull(message = "模板参数不能为空")
        private Map<String, Object> templateParams;
    }
    
    /**
     * 参数映射应用响应DTO
     */
    @Data
    @Schema(description = "参数映射应用响应")
    public static class ParameterMappingResponse implements Serializable {
        
        private static final long serialVersionUID = 1L;
        
        @Schema(description = "转换后的渠道参数")
        private Map<String, Object> channelParams;
        
        @Schema(description = "是否应用了映射规则")
        private Boolean mappingApplied;
        
        @Schema(description = "使用的映射配置ID")
        private String mappingId;
        
        @Schema(description = "渠道特定的模板内容")
        private String channelTemplateContent;
    }
}
