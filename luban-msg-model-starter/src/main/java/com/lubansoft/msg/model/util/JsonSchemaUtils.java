package com.lubansoft.msg.model.util;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.fge.jsonschema.core.report.LogLevel;
import com.github.fge.jsonschema.core.report.ProcessingMessage;
import com.github.fge.jsonschema.core.report.ProcessingReport;
import com.github.fge.jsonschema.main.JsonSchemaFactory;
import com.lubansoft.base.common.exception.BusinessException;

import java.util.Iterator;
import java.util.Map;

public class JsonSchemaUtils {

    public static void validate(Map<String, Object> jsonSchema, Map<String, Object> configVal) {
        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode schemaNode = objectMapper.valueToTree(jsonSchema);
        JsonNode jsonNode = objectMapper.valueToTree(configVal);

        ProcessingReport report = JsonSchemaFactory.byDefault().getValidator().validateUnchecked(schemaNode, jsonNode);
        if (!report.isSuccess()) {
            Iterator<ProcessingMessage> it = report.iterator();
            StringBuilder ms = new StringBuilder();
            ms.append("输入校验不通过: ");
            while (it.hasNext()) {
                ProcessingMessage pm = it.next();
                if (!LogLevel.WARNING.equals(pm.getLogLevel())) {
                    JsonNode pmNode = pm.asJson();
                    String instancePath = "";
                    String message = "";
                    
                    if (pmNode.get("instance") != null && pmNode.get("instance").get("pointer") != null) {
                        instancePath = pmNode.get("instance").get("pointer").asText();
                    }
                    
                    if (pmNode.get("message") != null) {
                        message = pmNode.get("message").asText();
                    }
                    
                    // 提取字段路径
                    String[] fieldPath = instancePath.split("/");

                    // 递归查找 title
                    String title = findTitleByPath(schemaNode, fieldPath, 1);

                    ms.append(String.format("参数[%s]: 格式错误 - %s ", title != null ? title : instancePath, message));
                }
            }
            throw new BusinessException(ms.toString());
        }
    }

    /**
     * 递归查找 JSON Schema 中的 title
     *
     * @param node      当前节点
     * @param fieldPath 字段路径数组
     * @param index     当前路径索引
     * @return          匹配的 title，如果没有找到则返回 null
     */
    private static String findTitleByPath(JsonNode node, String[] fieldPath, int index) {
        if (node == null || index >= fieldPath.length) {
            return null;
        }

        String currentField = fieldPath[index];
        JsonNode nextNode = node.get("properties") != null ? node.get("properties").get(currentField) : null;

        if (nextNode != null) {
            // 如果当前字段是嵌套对象，继续递归查找
            if (nextNode.has("properties")) {
                String result = findTitleByPath(nextNode, fieldPath, index + 1);
                if (result != null) {
                    return result;
                }
            }

            // 如果当前字段有 description，返回它
            if (nextNode.has("description")) {
                return nextNode.get("description").asText();
            }
        }

        return null;
    }
}
