package com.lubansoft.msg.model.repository.entity;

import com.baomidou.mybatisplus.annotation.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 消息统计实体
 * 用于记录每日消息发送统计数据
 */
@TableName("message_statistics")
public class MessageStatistics {
    
    /**
     * 统计ID
     */
    @TableId(value = "id")
    private Long id;
    
    /**
     * 统计日期
     */
    @NotNull(message = "统计日期不能为空")
    @TableField("statistics_date")
    private LocalDate statisticsDate;
    
    /**
     * 应用ID
     */
    @NotBlank(message = "应用ID不能为空")
    @TableField("app_id")
    private String appId;
    
    /**
     * 模板ID
     */
    @TableField("template_id")
    private String templateId;
    
    /**
     * 发送渠道
     */
    @TableField("channel")
    private String channel;
    
    /**
     * 总发送数
     */
    @TableField("total_count")
    private Long totalCount = 0L;
    
    /**
     * 成功数
     */
    @TableField("success_count")
    private Long successCount = 0L;
    
    /**
     * 失败数
     */
    @TableField("failure_count")
    private Long failureCount = 0L;
    
    /**
     * 待发送数
     */
    @TableField("pending_count")
    private Long pendingCount = 0L;
    
    /**
     * 发送中数
     */
    @TableField("sending_count")
    private Long sendingCount = 0L;
    
    /**
     * 总费用（分）
     */
    @TableField("total_cost")
    private Long totalCost = 0L;
    
    /**
     * 平均响应时间（毫秒）
     */
    @TableField("avg_response_time")
    private Long avgResponseTime = 0L;
    
    /**
     * 最大响应时间（毫秒）
     */
    @TableField("max_response_time")
    private Long maxResponseTime = 0L;
    
    /**
     * 最小响应时间（毫秒）
     */
    @TableField("min_response_time")
    private Long minResponseTime = 0L;
    
    /**
     * 创建时间
     */
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;
    
    // Constructors
    public MessageStatistics() {}
    
    public MessageStatistics(LocalDate statisticsDate, String appId, String templateId, String channel) {
        this.statisticsDate = statisticsDate;
        this.appId = appId;
        this.templateId = templateId;
        this.channel = channel;
    }
    

    
    // Getters and Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public LocalDate getStatisticsDate() {
        return statisticsDate;
    }
    
    public void setStatisticsDate(LocalDate statisticsDate) {
        this.statisticsDate = statisticsDate;
    }
    
    public String getAppId() {
        return appId;
    }
    
    public void setAppId(String appId) {
        this.appId = appId;
    }
    
    public String getTemplateId() {
        return templateId;
    }
    
    public void setTemplateId(String templateId) {
        this.templateId = templateId;
    }
    
    public String getChannel() {
        return channel;
    }
    
    public void setChannel(String channel) {
        this.channel = channel;
    }
    
    public Long getTotalCount() {
        return totalCount;
    }
    
    public void setTotalCount(Long totalCount) {
        this.totalCount = totalCount;
    }
    
    public Long getSuccessCount() {
        return successCount;
    }
    
    public void setSuccessCount(Long successCount) {
        this.successCount = successCount;
    }
    
    public Long getFailureCount() {
        return failureCount;
    }
    
    public void setFailureCount(Long failureCount) {
        this.failureCount = failureCount;
    }
    
    public Long getPendingCount() {
        return pendingCount;
    }
    
    public void setPendingCount(Long pendingCount) {
        this.pendingCount = pendingCount;
    }
    
    public Long getSendingCount() {
        return sendingCount;
    }
    
    public void setSendingCount(Long sendingCount) {
        this.sendingCount = sendingCount;
    }
    
    public Long getTotalCost() {
        return totalCost;
    }
    
    public void setTotalCost(Long totalCost) {
        this.totalCost = totalCost;
    }
    
    public Long getAvgResponseTime() {
        return avgResponseTime;
    }
    
    public void setAvgResponseTime(Long avgResponseTime) {
        this.avgResponseTime = avgResponseTime;
    }
    
    public Long getMaxResponseTime() {
        return maxResponseTime;
    }
    
    public void setMaxResponseTime(Long maxResponseTime) {
        this.maxResponseTime = maxResponseTime;
    }
    
    public Long getMinResponseTime() {
        return minResponseTime;
    }
    
    public void setMinResponseTime(Long minResponseTime) {
        this.minResponseTime = minResponseTime;
    }
    
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
    
    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }
    
    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }
    
    /**
     * 获取成功率（百分比）
     */
    public double getSuccessRate() {
        if (totalCount == null || totalCount == 0) {
            return 0.0;
        }
        return (double) successCount / totalCount * 100;
    }
    
    /**
     * 获取失败率（百分比）
     */
    public double getFailureRate() {
        if (totalCount == null || totalCount == 0) {
            return 0.0;
        }
        return (double) failureCount / totalCount * 100;
    }
    
    /**
     * 增加总数
     */
    public void incrementTotalCount() {
        this.totalCount++;
    }
    
    /**
     * 增加成功数
     */
    public void incrementSuccessCount() {
        this.successCount++;
    }
    
    /**
     * 增加失败数
     */
    public void incrementFailureCount() {
        this.failureCount++;
    }
    
    /**
     * 增加待发送数
     */
    public void incrementPendingCount() {
        this.pendingCount++;
    }
    
    /**
     * 增加发送中数
     */
    public void incrementSendingCount() {
        this.sendingCount++;
    }
    
    /**
     * 减少待发送数
     */
    public void decrementPendingCount() {
        if (this.pendingCount > 0) {
            this.pendingCount--;
        }
    }
    
    /**
     * 减少发送中数
     */
    public void decrementSendingCount() {
        if (this.sendingCount > 0) {
            this.sendingCount--;
        }
    }
    
    /**
     * 增加费用
     */
    public void addCost(Long cost) {
        if (cost != null && cost > 0) {
            this.totalCost += cost;
        }
    }
    
    /**
     * 更新响应时间统计
     */
    public void updateResponseTime(long responseTime) {
        if (this.minResponseTime == 0 || responseTime < this.minResponseTime) {
            this.minResponseTime = responseTime;
        }
        if (responseTime > this.maxResponseTime) {
            this.maxResponseTime = responseTime;
        }
        
        // 简单的平均值计算，实际应用中可能需要更复杂的算法
        long processedCount = this.successCount + this.failureCount;
        if (processedCount > 0) {
            this.avgResponseTime = (this.avgResponseTime * (processedCount - 1) + responseTime) / processedCount;
        }
    }
    
    @Override
    public String toString() {
        return "MessageStatistics{" +
                "id=" + id +
                ", statisticsDate=" + statisticsDate +
                ", appId='" + appId + '\'' +
                ", templateId='" + templateId + '\'' +
                ", channel='" + channel + '\'' +
                ", totalCount=" + totalCount +
                ", successCount=" + successCount +
                ", failureCount=" + failureCount +
                ", pendingCount=" + pendingCount +
                ", sendingCount=" + sendingCount +
                ", totalCost=" + totalCost +
                ", avgResponseTime=" + avgResponseTime +
                ", maxResponseTime=" + maxResponseTime +
                ", minResponseTime=" + minResponseTime +
                ", createdAt=" + createdAt +
                ", updatedAt=" + updatedAt +
                '}';
    }
}