package com.lubansoft.msg.model.repository.repo;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lubansoft.msg.common.enums.MessageStatus;
import com.lubansoft.msg.model.repository.entity.MessageRecord;
import com.lubansoft.msg.model.repository.mapper.MessageRecordMapper;
import org.springframework.stereotype.Service;

/**
 * 消息记录服务类
 */
@Service
public class MessageRecordRepo extends ServiceImpl<MessageRecordMapper, MessageRecord> {
    
    /**
     * 更新消息错误信息
     */
    public boolean updateStatusAndError(String requestId, MessageStatus status, String errorMessage) {
        return update(new LambdaUpdateWrapper<MessageRecord>()
                .eq(MessageRecord::getRequestId, requestId)
                .set(MessageRecord::getStatus, status)
                .set(MessageRecord::getErrorMessage, errorMessage)
                .setSql("retry_count = retry_count + 1"));
    }
    
}