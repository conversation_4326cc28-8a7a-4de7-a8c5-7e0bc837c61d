package com.lubansoft.msg.model.controller.system;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lubansoft.base.common.model.ResponseEntity;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import jakarta.validation.Valid;
import com.lubansoft.msg.model.repository.entity.RoutingRule;
import com.lubansoft.msg.model.service.routing.RoutingRuleManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;

/**
 * 路由规则管理控制器
 */
@RestController
@RequestMapping("/routing-rules")
@Tag(name = "路由规则管理", description = "路由规则的增删改查接口")
public class RoutingRuleController {
    
    private static final Logger logger = LoggerFactory.getLogger(RoutingRuleController.class);
    
    @Autowired
    private RoutingRuleManager routingRuleManager;
    
    /**
     * 创建路由规则
     */
    @Operation(summary = "创建路由规则", description = "创建新的路由规则")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "创建成功", 
                    content = @Content(mediaType = "application/json", 
                            schema = @Schema(implementation = ResponseEntity.class))),
            @ApiResponse(responseCode = "400", description = "请求参数错误"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @PostMapping
    public ResponseEntity<RoutingRule> createRule(
            @Parameter(description = "路由规则信息") 
            @Valid @RequestBody RoutingRule rule) {
        logger.info("创建路由规则: ruleName={}", rule.getRuleName());
        
        try {
            RoutingRule createdRule = routingRuleManager.addRule(rule);
            return ResponseEntity.success(createdRule);
        } catch (Exception e) {
            logger.error("创建路由规则异常", e);
            return ResponseEntity.fail(500, "创建规则失败");
        }
    }
    
    /**
     * 更新路由规则
     */
    @Operation(summary = "更新路由规则", description = "根据规则ID更新路由规则信息")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "更新成功", 
                    content = @Content(mediaType = "application/json", 
                            schema = @Schema(implementation = ResponseEntity.class))),
            @ApiResponse(responseCode = "400", description = "请求参数错误"),
            @ApiResponse(responseCode = "404", description = "规则不存在"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @PutMapping("/{ruleId}")
    public ResponseEntity<RoutingRule> updateRule(
            @Parameter(description = "规则ID") @PathVariable String ruleId,
            @Parameter(description = "路由规则信息") 
            @Valid @RequestBody RoutingRule rule) {
        logger.info("更新路由规则: ruleId={}", ruleId);
        
        try {
            RoutingRule updatedRule = routingRuleManager.updateRule(ruleId, rule);
            return ResponseEntity.success(updatedRule);
        } catch (Exception e) {
            logger.error("更新路由规则异常", e);
            return ResponseEntity.fail(500, "更新规则失败");
        }
    }
    
    /**
     * 删除路由规则
     */
    @Operation(summary = "删除路由规则", description = "根据规则ID删除路由规则")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "删除成功", 
                    content = @Content(mediaType = "application/json", 
                            schema = @Schema(implementation = ResponseEntity.class))),
            @ApiResponse(responseCode = "404", description = "规则不存在"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @DeleteMapping("/{ruleId}")
    public ResponseEntity<Void> deleteRule(
            @Parameter(description = "规则ID") @PathVariable String ruleId) {
        logger.info("删除路由规则: ruleId={}", ruleId);
        
        try {
            routingRuleManager.deleteRule(ruleId);
            return ResponseEntity.success();
        } catch (Exception e) {
            logger.error("删除路由规则异常", e);
            return ResponseEntity.fail(500, "删除规则失败");
        }
    }
    
    /**
     * 获取路由规则详情
     */
    @Operation(summary = "获取路由规则详情", description = "根据规则ID获取路由规则详细信息")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "查询成功", 
                    content = @Content(mediaType = "application/json", 
                            schema = @Schema(implementation = ResponseEntity.class))),
            @ApiResponse(responseCode = "404", description = "规则不存在"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @GetMapping("/{ruleId}")
    public ResponseEntity<RoutingRule> getRule(
            @Parameter(description = "规则ID") @PathVariable String ruleId) {
        logger.debug("获取路由规则详情: ruleId={}", ruleId);
        
        Optional<RoutingRule> rule = Optional.ofNullable(routingRuleManager.getRuleById(ruleId));
        return rule.map(ResponseEntity::success).orElseGet(() -> ResponseEntity.fail(404, "规则不存在"));
    }
    
    /**
     * 分页获取路由规则列表
     */
    @Operation(summary = "分页获取路由规则列表", description = "分页获取路由规则列表，支持启用状态筛选")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "查询成功", 
                    content = @Content(mediaType = "application/json", 
                            schema = @Schema(implementation = ResponseEntity.class))),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @GetMapping
    public ResponseEntity<IPage<RoutingRule>> getRules(
            @Parameter(description = "页码(从0开始)") @RequestParam(defaultValue = "0") int pageNum,
            @Parameter(description = "页大小") @RequestParam(defaultValue = "20") int pageSize,
            @Parameter(description = "启用状态") @RequestParam(required = false) Boolean enabled) {
        logger.debug("分页获取路由规则列表: pageNum={}, size={}, enabled={}", pageNum, pageSize, enabled);
        
        try {
            // 暂时使用简单实现，实际应该在RoutingRuleManager中实现分页查询
            List<RoutingRule> allRules = routingRuleManager.getEnabledRules();
            // 这里需要实现分页逻辑，暂时返回所有规则
            IPage<RoutingRule> rules = null; // TODO: 实现分页逻辑
            return ResponseEntity.success(rules);
        } catch (Exception e) {
            logger.error("分页获取路由规则列表异常", e);
            return ResponseEntity.fail(500, "获取规则列表失败");
        }
    }
    
    /**
     * 获取所有启用的路由规则
     */
    @Operation(summary = "获取所有启用的路由规则", description = "获取所有启用的路由规则列表")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "查询成功", 
                    content = @Content(mediaType = "application/json", 
                            schema = @Schema(implementation = ResponseEntity.class))),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @GetMapping("/enabled")
    public ResponseEntity<List<RoutingRule>> getEnabledRules() {
        logger.debug("获取所有启用的路由规则");
        
        try {
            List<RoutingRule> rules = routingRuleManager.getEnabledRules();
            return ResponseEntity.success(rules);
        } catch (Exception e) {
            logger.error("获取启用的路由规则异常", e);
            return ResponseEntity.fail(500, "获取启用规则失败");
        }
    }
    
    /**
     * 搜索路由规则
     */
    @Operation(summary = "搜索路由规则", description = "根据规则名称搜索路由规则")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "查询成功", 
                    content = @Content(mediaType = "application/json", 
                            schema = @Schema(implementation = ResponseEntity.class))),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @GetMapping("/search")
    public ResponseEntity<List<RoutingRule>> searchRules(
            @Parameter(description = "规则名称") @RequestParam(required = false) String ruleName) {
        logger.debug("搜索路由规则: ruleName={}", ruleName);
        
        try {
            // 暂时简化实现，返回所有启用的规则
            // TODO: 在RoutingRuleManager中实现按名称搜索功能
            List<RoutingRule> rules = routingRuleManager.getEnabledRules();
            return ResponseEntity.success(rules);
        } catch (Exception e) {
            logger.error("搜索路由规则异常", e);
            return ResponseEntity.fail(500, "搜索规则失败");
        }
    }
    
    /**
     * 更新路由规则状态
     */
    @Operation(summary = "更新路由规则状态", description = "根据规则ID更新路由规则状态")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "更新成功", 
                    content = @Content(mediaType = "application/json", 
                            schema = @Schema(implementation = ResponseEntity.class))),
            @ApiResponse(responseCode = "404", description = "规则不存在"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @PatchMapping("/{ruleId}/status")
    public ResponseEntity<Void> updateRuleStatus(
            @Parameter(description = "规则ID") @PathVariable String ruleId,
            @Parameter(description = "启用状态") @RequestParam boolean enabled) {
        logger.info("更新路由规则状态: ruleId={}, enabled={}", ruleId, enabled);
        
        try {
            // 暂时简化实现
            // TODO: 在RoutingRuleManager中实现updateRuleStatus方法
            // routingRuleManager.updateRuleStatus(ruleId, enabled);
            return ResponseEntity.success();
        } catch (Exception e) {
            logger.error("更新路由规则状态异常", e);
            return ResponseEntity.fail(500, "更新规则状态失败");
        }
    }
    
    /**
     * 批量更新规则优先级
     */
    @Operation(summary = "批量更新规则优先级", description = "批量更新路由规则优先级")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "更新成功", 
                    content = @Content(mediaType = "application/json", 
                            schema = @Schema(implementation = ResponseEntity.class))),
            @ApiResponse(responseCode = "400", description = "请求参数错误"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @PatchMapping("/priorities")
    public ResponseEntity<Void> updateRulePriorities(
            @Parameter(description = "规则优先级列表") 
            @RequestBody List<RoutingRuleManager.RulePriority> priorities) {
        logger.info("批量更新路由规则优先级: count={}", priorities.size());
        
        try {
            // 暂时注释掉，需要在RoutingRuleManager中实现
            // routingRuleManager.updateRulePriorities(priorities);
            return ResponseEntity.success();
        } catch (Exception e) {
            logger.error("批量更新规则优先级异常", e);
            return ResponseEntity.fail(500, "更新规则优先级失败");
        }
    }
    
    /**
     * 验证路由规则条件
     */
    @Operation(summary = "验证路由规则条件", description = "验证路由规则条件是否有效")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "验证成功", 
                    content = @Content(mediaType = "application/json", 
                            schema = @Schema(implementation = ResponseEntity.class))),
            @ApiResponse(responseCode = "400", description = "请求参数错误"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @PostMapping("/validate-condition")
    public ResponseEntity<RoutingRuleManager.RuleValidationResult> validateCondition(
            @Parameter(description = "验证条件请求") 
            @RequestBody ValidateConditionRequest request) {
        logger.debug("验证路由规则条件: condition={}", request.getCondition());
        
        try {
            // 暂时简化实现
            RoutingRuleManager.RuleValidationResult result =
                routingRuleManager.validateCondition(request.getCondition());
            return ResponseEntity.success(result);
        } catch (Exception e) {
            logger.error("验证路由规则条件异常", e);
            return ResponseEntity.fail(500, "验证条件失败");
        }
    }
    
    /**
     * 获取路由规则统计信息
     */
    @Operation(summary = "获取路由规则统计信息", description = "获取路由规则的统计信息")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "查询成功", 
                    content = @Content(mediaType = "application/json", 
                            schema = @Schema(implementation = ResponseEntity.class))),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @GetMapping("/statistics")
    public ResponseEntity<String> getStatistics() {
        logger.debug("获取路由规则统计信息");
        
        try {
            // 暂时简化实现，返回简单的统计信息
            return ResponseEntity.success("统计功能暂未实现");
        } catch (Exception e) {
            logger.error("获取路由规则统计信息异常", e);
            return ResponseEntity.fail(500, "获取统计信息失败");
        }
    }
    
    /**
     * 验证条件请求
     */
    public static class ValidateConditionRequest {
        private String condition;
        
        public String getCondition() {
            return condition;
        }
        
        public void setCondition(String condition) {
            this.condition = condition;
        }
    }
}