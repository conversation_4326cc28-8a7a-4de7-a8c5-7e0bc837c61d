package com.lubansoft.msg.model.service;

import com.lubansoft.msg.model.model.MessageReceiverDTO;
import com.lubansoft.msg.model.model.MessageRequestDTO;
import com.lubansoft.msg.common.model.RoutingResult;
import com.lubansoft.msg.common.model.SendResult;
import com.lubansoft.msg.model.repository.entity.MessageRecord;
import com.lubansoft.msg.model.repository.mapper.MessageRecordMapper;
import com.lubansoft.msg.model.repository.repo.MessageRequestService;
import com.lubansoft.msg.model.repository.entity.MessageRequestEntity;
import com.lubansoft.msg.model.service.consumer.ChannelParameterResult;
import com.lubansoft.msg.model.service.producer.MessageProducer;
import com.lubansoft.msg.model.service.producer.MessageProducerManager;
import com.lubansoft.msg.model.service.routing.RoutingEngineManager;
import com.lubansoft.msg.model.service.routing.RoutingContext;
import com.lubansoft.msg.model.service.processor.MessageContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 消息发送服务
 */
@Service
public class MessageSendService {

    private static final Logger logger = LoggerFactory.getLogger(MessageSendService.class);

    @Autowired
    private RoutingEngineManager routingEngineManager;

    @Autowired
    private MessageProducerManager messageProducerManager;

    @Autowired
    private MessageRecordMapper messageRecordRepository;

    @Autowired
    private MessageRequestService messageRequestService;

    @Autowired
    private ReceiverSubscriptionChecker receiverSubscriptionChecker;

    @Autowired
    private TemplateChannelMappingService templateChannelMappingService;

    /**
     * 发送单个消息（带MessageContext）
     *
     * @param messageRequestDTO 消息请求
     * @param appId             应用ID
     * @param requestId         请求ID
     * @param messageContext    消息上下文
     * @return 发送结果
     */
    public SendResult sendMessage(MessageRequestDTO messageRequestDTO, String appId, String requestId, MessageContext messageContext) {
        long startTime = System.currentTimeMillis();
        try {
            // 1. 尝试保存消息请求记录
            messageRequestService.saveRequest(requestId, messageRequestDTO,
                    MessageRequestEntity.RequestStatus.PROCESSING.getCode());

            // 2. 构建路由上下文
            RoutingContext routingContext = buildEnhancedRoutingContext(messageContext, appId, requestId);

            // 3. 路由匹配
            RoutingResult routingResult = routingEngineManager.route(messageRequestDTO, routingContext);

            if (!routingResult.isMatched()) {
                // 尝试更新请求状态为失败
                messageRequestService.updateStatus(requestId, MessageRequestEntity.RequestStatus.FAILED.getCode());

                // 保存失败记录
                saveMessageRecord(messageRequestDTO, appId, null, "ROUTING_FAILED", routingResult.getErrorMessage(), startTime);

                String errorCode = (String) routingResult.getContext("errorCode");
                return SendResult.failure(errorCode != null ? errorCode : "ROUTING_FAILED", routingResult.getErrorMessage());
            }

            List<String> targetChannels = routingResult.getTargetChannels();

            // 4. 根据渠道分组接收者，并应用订阅过滤规则
            Map<String, List<MessageReceiverDTO>> channelReceivers = groupReceiversByChannel(messageRequestDTO, targetChannels);

            // 为每个渠道准备特定的参数映射
            Map<String, ChannelParameterResult> channelParameterResults =
                    prepareChannelParameterResults(messageRequestDTO.getTemplateId(), targetChannels, messageRequestDTO.getGlobalParam());

            // 5. 构建消息属性（包含MessageContext信息）
            Map<String, Object> messageProperties = buildEnhancedMessageProperties(messageRequestDTO, messageContext, appId, requestId);

            // 将渠道参数结果添加到消息属性中
            messageProperties.put("channelParameterResults", channelParameterResults);

            // 6. 使用新的消息发送方式（按渠道分组发送）
            MessageProducer.SendResult sendResult = messageProducerManager.sendMessage(messageRequestDTO, targetChannels, channelReceivers, messageProperties);

            // 7. 转换发送结果
            SendResult finalResult = convertSendResult(sendResult, targetChannels);

            // 8. 保存消息记录
            saveMessageRecord(messageRequestDTO, appId, targetChannels, finalResult.isSuccess() ? "SUCCESS" : "FAILED", finalResult.getErrorMessage(), startTime);

            // 9. 尝试更新请求状态
            String requestStatus = finalResult.isSuccess() ? MessageRequestEntity.RequestStatus.COMPLETED.getCode() : MessageRequestEntity.RequestStatus.FAILED.getCode();
            messageRequestService.updateStatus(requestId, requestStatus);

            return finalResult;

        } catch (Exception e) {
            messageRequestService.updateStatus(requestId, MessageRequestEntity.RequestStatus.FAILED.getCode());
            saveMessageRecord(messageRequestDTO, appId, null, "ERROR", e.getMessage(), startTime);
            return SendResult.failure("SEND_ERROR", "发送异常: " + e.getMessage());
        }
    }

    /**
     * 构建消息属性
     */
    private Map<String, Object> buildMessageProperties(MessageRequestDTO messageRequestDTO,
                                                       String appId, String requestId) {
        Map<String, Object> properties = new HashMap<>();

        properties.put("messageId", messageRequestDTO.getMessageId());
        properties.put("templateId", messageRequestDTO.getTemplateId());
        properties.put("appId", appId);
        properties.put("requestId", requestId);

        if (messageRequestDTO.getPriority() != null) {
            properties.put("priority", messageRequestDTO.getPriority());
        }

        if (messageRequestDTO.getGlobalParam() != null) {
            properties.put("globalParams", messageRequestDTO.getGlobalParam());
        }

        return properties;
    }

    /**
     * 构建增强的消息属性（包含MessageContext信息）
     */
    private Map<String, Object> buildEnhancedMessageProperties(MessageRequestDTO messageRequestDTO,
                                                               MessageContext messageContext,
                                                               String appId, String requestId) {
        Map<String, Object> properties = buildMessageProperties(messageRequestDTO, appId, requestId);

        // 添加MessageContext中的属性
        if (messageContext != null && messageContext.getAttributes() != null) {
            for (Map.Entry<String, Object> entry : messageContext.getAttributes().entrySet()) {
                properties.put("context_" + entry.getKey(), entry.getValue());
            }

            // 添加MessageContext的基础信息
            properties.put("contextCreateTime", messageContext.getCreateTime());
        }

        return properties;
    }

    /**
     * 为每个渠道准备参数结果
     * 将模板参数根据模板渠道映射配置转换为每个渠道的特定参数结果
     *
     * @param templateId     模板ID
     * @param targetChannels 目标渠道列表
     * @param templateParam  模板参数（原始业务参数）
     * @return 每个渠道的参数结果 Map<渠道类型, ChannelParameterResult>
     */
    private Map<String, ChannelParameterResult> prepareChannelParameterResults(
            String templateId, List<String> targetChannels, Map<String, Object> templateParam) {
        logger.debug("开始为各渠道准备参数结果: templateId={}, channels={}, templateParamSize={}",
                templateId, targetChannels, templateParam != null ? templateParam.size() : 0);

        Map<String, ChannelParameterResult> channelParameterResults = new HashMap<>();

        // 为每个渠道应用参数映射
        for (String channelType : targetChannels) {
            ChannelParameterResult parameterResult =
                    templateChannelMappingService.applyChannelParameterMapping(templateId, channelType, templateParam);

            channelParameterResults.put(channelType, parameterResult);

            logger.debug("渠道参数结果准备完成: templateId={}, channelType={}, " +
                            "templateParamSize={}, globalParamSize={}",
                    templateId, channelType,
                    parameterResult.getTemplateParam().size(),
                    parameterResult.getGlobalParam().size());

        }
        return channelParameterResults;
    }

    /**
     * 构建增强的路由上下文（利用MessageContext）
     */
    private RoutingContext buildEnhancedRoutingContext(MessageContext messageContext, String appId, String requestId) {
        RoutingContext routingContext = new RoutingContext();

        // 设置基础信息
        routingContext.setAttribute("appId", appId);
        routingContext.setAttribute("requestId", requestId);

        // 从MessageContext中提取信息
        if (messageContext != null) {
            // 添加MessageContext中的所有属性
            if (messageContext.getAttributes() != null) {
                for (Map.Entry<String, Object> entry : messageContext.getAttributes().entrySet()) {
                    routingContext.setAttribute(entry.getKey(), entry.getValue());
                }
            }

            // 添加MessageContext的基础信息
            routingContext.setAttribute("messageContextCreateTime", messageContext.getCreateTime());

            // 如果MessageContext中有appId和requestId，优先使用
            if (messageContext.getAppId() != null) {
                routingContext.setAttribute("appId", messageContext.getAppId());
            }
            if (messageContext.getRequestId() != null) {
                routingContext.setAttribute("requestId", messageContext.getRequestId());
            }
        }

        return routingContext;
    }

    /**
     * 转换发送结果（从单个SendResult到SendResult）
     */
    private SendResult convertSendResult(MessageProducer.SendResult producerResult, List<String> targetChannels) {
        if (producerResult.isSuccess()) {
            return SendResult.success("消息发送成功，目标渠道: " + targetChannels);
        } else {
            return SendResult.failure(producerResult.getErrorCode(), "消息发送失败: " + producerResult.getErrorMessage());
        }
    }

    /**
     * 保存消息记录（为每个接收者和渠道创建记录）
     */
    private void saveMessageRecord(MessageRequestDTO messageRequestDTO, String appId,
                                   List<String> targetChannels, String status,
                                   String errorMessage, long startTime) {
        try {
            // 为每个接收者和每个渠道创建记录
            for (String channel : targetChannels) {
                for (var receiver : messageRequestDTO.getReceivers()) {
                    createMessageRecord(messageRequestDTO, channel, receiver, status, errorMessage);
                }
            }

        } catch (Exception e) {
            logger.error("保存消息记录失败: messageId={}", messageRequestDTO.getMessageId(), e);
        }
    }

    /**
     * 创建单条消息记录
     */
    private void createMessageRecord(MessageRequestDTO messageRequestDTO, String channel,
                                     String receiver, String status, String errorMessage) {
        MessageRecord record = new MessageRecord();
        record.setRequestId(messageRequestDTO.getRequestId());
        record.setTemplateId(messageRequestDTO.getTemplateId());
        record.setReceiver(receiver);
        record.setChannel(channel);
        record.setStatus(status); // 直接使用字符串状态
        record.setErrorMessage(errorMessage);
        record.setCreatedAt(LocalDateTime.now());

        if ("SUCCESS".equals(status) || "DELAY_SUCCESS".equals(status)) {
            record.setSentTime(LocalDateTime.now());
        }

        messageRecordRepository.insert(record);
    }

    /**
     * 根据渠道分组接收者，并应用订阅过滤规则
     *
     * @param messageRequestDTO 消息请求
     * @param targetChannels    目标渠道列表
     * @return 按渠道分组的接收者映射
     */
    private Map<String, List<MessageReceiverDTO>> groupReceiversByChannel(
            MessageRequestDTO messageRequestDTO, List<String> targetChannels) {

        Map<String, List<MessageReceiverDTO>> channelReceivers = new HashMap<>();

        for (String channel : targetChannels) {
            // 正常模式，根据订阅配置过滤接收者
            List<MessageReceiverDTO> filteredReceivers = receiverSubscriptionChecker
                    .filterReceiversBySubscription(messageRequestDTO, channel);
            channelReceivers.put(channel, filteredReceivers);
        }

        return channelReceivers;
    }

}