package com.lubansoft.msg.model.service;

import com.lubansoft.msg.common.model.MessageReceiver;
import com.lubansoft.msg.model.model.MessageReceiverDTO;
import com.lubansoft.msg.model.model.MessageRequestDTO;
import com.lubansoft.msg.model.repository.entity.ReceiverDnd;
import com.lubansoft.msg.model.repository.entity.ReceiverSub;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 接收者订阅检查服务
 * 用于根据用户订阅配置过滤接收者列表
 */
@Service
public class ReceiverSubscriptionChecker {

    @Autowired
    private ReceiverSubService receiverSubService;
    
    @Autowired
    private ReceiverDndService receiverDndService;

    /**
     * 根据用户订阅配置过滤接收者列表
     *
     * @param request 消息请求
     * @param channel 渠道名称
     * @return 过滤后的接收者列表
     */
    public List<MessageReceiverDTO> filterReceiversBySubscription(MessageRequestDTO request, String channel) {
        // 强制发送
        boolean forceSend = request.getForceSend() != null && request.getForceSend();

        List<MessageReceiverDTO> filteredReceivers = new ArrayList<>();
        Map<String, ReceiverSub> receiverSubMap = new ConcurrentHashMap<>();
        Map<String, List<ReceiverDnd>> receiverDndMap = new ConcurrentHashMap<>();

        // 批量获取所有接收者的订阅信息
        List<String> receiverIds = request.getReceivers().stream()
                .distinct()
                .toList();

        for (String receiverId : receiverIds) {
            // 获取订阅信息
            List<ReceiverSub> subs = receiverSubService.findByReceiverId(receiverId);
            for (ReceiverSub sub : subs) {
                if (channel.equals(sub.getChannel())) {
                    receiverSubMap.put(receiverId, sub);
                    break;
                }
            }
            
            // 获取免打扰信息
            List<ReceiverDnd> dnds = receiverDndService.findByReceiverId(receiverId);
            receiverDndMap.put(receiverId, dnds);
        }

        // 根据订阅配置过滤接收者
        for (String receiver : request.getReceivers()) {
            ReceiverSub subscription = receiverSubMap.get(receiver);
            
            // 检查用户是否订阅了该渠道
            if (subscription != null && subscription.getIsSub() != null && subscription.getIsSub()) {
                // 检查是否处于免打扰时间
                List<ReceiverDnd> dnds = receiverDndMap.get(receiver);
                if (isInDndPeriod(dnds) && !forceSend) {
                    continue;
                }
                
                // 用户已订阅该渠道，且不在免打扰时间，添加联系方式
                filteredReceivers.add(new MessageReceiverDTO(subscription.getReceiverId(), subscription.getContact()));
            }
        }

        return filteredReceivers;
    }
    
    /**
     * 检查用户是否处于免打扰时间
     * 
     * @param dnds 用户的免打扰设置列表
     * @return 是否处于免打扰时间
     */
    private boolean isInDndPeriod(List<ReceiverDnd> dnds) {
        if (dnds == null || dnds.isEmpty()) {
            return false;
        }
        
        LocalTime now = LocalTime.now();
        LocalDate today = LocalDate.now();
        DayOfWeek dayOfWeek = today.getDayOfWeek();
        int dayValue = dayOfWeek.getValue();
        
        for (ReceiverDnd dnd : dnds) {
            // 检查星期几是否匹配
            if (dnd.getDays() != null) {
                boolean dayMatch = false;
                for (Integer day : dnd.getDays()) {
                    if (day != null && day == dayValue) {
                        dayMatch = true;
                        break;
                    }
                }
                if (!dayMatch) {
                    continue;
                }
            }
            
            // 检查时间是否在免打扰时间段内
            LocalTime startTime = dnd.getStartTime();
            LocalTime endTime = dnd.getEndTime();
            
            if (startTime != null && endTime != null) {
                // 处理跨天情况（如22:00到06:00）
                if (startTime.isAfter(endTime)) {
                    // 跨天情况
                    if (now.isAfter(startTime) || now.isBefore(endTime)) {
                        return true;
                    }
                } else {
                    // 不跨天情况
                    if (!now.isBefore(startTime) && now.isBefore(endTime)) {
                        return true;
                    }
                }
            }
        }
        
        return false;
    }
}