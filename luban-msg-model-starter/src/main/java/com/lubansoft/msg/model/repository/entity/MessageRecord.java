package com.lubansoft.msg.model.repository.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.lubansoft.msg.common.enums.MessageStatus;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 消息记录实体（对应message_record表）
 */
@Data
@TableName("message_record")
public class MessageRecord {

    /**
     * 记录ID（主键）
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 请求ID
     */
    @NotBlank(message = "请求ID不能为空")
    @TableField("request_id")
    private String requestId;
    
    /**
     * 模板ID
     */
    @TableField("template_id")
    private String templateId;
    
    /**
     * 接收者
     */
    @NotBlank(message = "接收者不能为空")
    @TableField("receiver")
    private String receiver;
    
    /**
     * 发送渠道
     */
    @NotNull(message = "发送渠道不能为空")
    @TableField("channel")
    private String channel;
    
    /**
     * 消息状态
     */
    @NotNull(message = "消息状态不能为空")
    @TableField("status")
    private String status;
    
    /**
     * 创建时间
     */
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;
    
    /**
     * 发送时间
     */
    @TableField("sent_time")
    private LocalDateTime sentTime;
    
    /**
     * 错误信息
     */
    @TableField("error_message")
    private String errorMessage;
    
    /**
     * 重试次数
     */
    @TableField("retry_count")
    private Integer retryCount = 0;
    
    // 构造函数
    public MessageRecord() {}

    /**
     * 获取消息状态枚举
     *
     * @return MessageStatus枚举
     */
    public MessageStatus getStatusEnum() {
        return status != null ? MessageStatus.fromCode(status) : null;
    }

    /**
     * 设置消息状态枚举
     *
     * @param status MessageStatus枚举
     */
    public void setStatusEnum(MessageStatus status) {
        this.status = status != null ? status.getCode() : null;
    }

    @Override
    public String toString() {
        return "MessageRecord{" +
                "id=" + id +
                ", requestId='" + requestId + '\'' +
                ", templateId='" + templateId + '\'' +
                ", receiver='" + receiver + '\'' +
                ", channel='" + channel + '\'' +
                ", status='" + status + '\'' +
                ", createdAt=" + createdAt +
                ", sentTime=" + sentTime +
                ", errorMessage='" + errorMessage + '\'' +
                ", retryCount=" + retryCount +
                '}';
    }
}