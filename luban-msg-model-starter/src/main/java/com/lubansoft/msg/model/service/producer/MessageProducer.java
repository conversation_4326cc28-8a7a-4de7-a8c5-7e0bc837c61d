package com.lubansoft.msg.model.service.producer;

import com.lubansoft.msg.common.model.MessageReceiver;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 消息生产者接口
 */
public interface MessageProducer {
    
    /**
     * 发送消息到指定交换机
     * 
     * @param exchangeName 交换机名称
     * @param routingKey 路由键
     * @param message 消息内容
     * @param properties 消息属性
     * @return 发送结果
     */
    SendResult sendMessage(String exchangeName, String routingKey, Object message, Map<String, Object> properties);
    
    /**
     * 发送消息到指定交换机（统一方法，支持渠道列表和按渠道分组的接收者）
     * 
     * @param exchangeName 交换机名称
     * @param routingKey 路由键
     * @param message 消息内容
     * @param targetChannels 目标渠道列表（可选）
     * @param channelReceivers 按渠道分组的接收者列表（可选）
     * @param properties 消息属性
     * @return 发送结果
     */
    SendResult sendMessage(String exchangeName, String routingKey, Object message,
                          List<String> targetChannels,
                          Map<String, List<MessageReceiver>> channelReceivers,
                          Map<String, Object> properties);
    
    /**
     * 发送延时消息
     * 
     * @param exchangeName 交换机名称
     * @param routingKey 路由键
     * @param message 消息内容
     * @param delayMillis 延时毫秒数
     * @param properties 消息属性
     * @return 发送结果
     */
    SendResult sendDelayMessage(String exchangeName, String routingKey, Object message, 
                               long delayMillis, Map<String, Object> properties);
    
    /**
     * 发送延时消息（按渠道分组接收者）
     * 
     * @param exchangeName 交换机名称
     * @param routingKey 路由键
     * @param message 消息内容
     * @param channelReceivers 按渠道分组的接收者列表
     * @param delayMillis 延时毫秒数
     * @param properties 消息属性
     * @return 发送结果
     */
    SendResult sendDelayMessage(String exchangeName, String routingKey, Object message, 
                               Map<String, List<MessageReceiver>> channelReceivers,
                               long delayMillis, Map<String, Object> properties);
    
    /**
     * 发送定时消息
     * 
     * @param exchangeName 交换机名称
     * @param routingKey 路由键
     * @param message 消息内容
     * @param scheduleTime 定时时间
     * @param properties 消息属性
     * @return 发送结果
     */
    SendResult sendScheduledMessage(String exchangeName, String routingKey, Object message, 
                                   LocalDateTime scheduleTime, Map<String, Object> properties);
    
    /**
     * 批量发送消息
     * 
     * @param messages 消息列表
     * @return 发送结果列表
     */
    List<SendResult> sendBatchMessages(List<MessageTask> messages);
    
    /**
     * 发送事务消息
     * 
     * @param exchangeName 交换机名称
     * @param routingKey 路由键
     * @param message 消息内容
     * @param properties 消息属性
     * @param transactionCallback 事务回调
     * @return 发送结果
     */
    SendResult sendTransactionalMessage(String exchangeName, String routingKey, Object message, 
                                       Map<String, Object> properties, TransactionCallback transactionCallback);
    
    /**
     * 消息任务
     */
    class MessageTask {
        private String exchangeName;
        private String routingKey;
        private Object message;
        private Map<String, Object> properties;
        private Long delayMillis;
        private LocalDateTime scheduleTime;
        
        public MessageTask() {}
        
        public MessageTask(String exchangeName, String routingKey, Object message) {
            this.exchangeName = exchangeName;
            this.routingKey = routingKey;
            this.message = message;
        }
        
        public MessageTask(String exchangeName, String routingKey, Object message, Map<String, Object> properties) {
            this.exchangeName = exchangeName;
            this.routingKey = routingKey;
            this.message = message;
            this.properties = properties;
        }
        
        // Getters and Setters
        public String getExchangeName() {
            return exchangeName;
        }
        
        public void setExchangeName(String exchangeName) {
            this.exchangeName = exchangeName;
        }
        
        public String getRoutingKey() {
            return routingKey;
        }
        
        public void setRoutingKey(String routingKey) {
            this.routingKey = routingKey;
        }
        
        public Object getMessage() {
            return message;
        }
        
        public void setMessage(Object message) {
            this.message = message;
        }
        
        public Map<String, Object> getProperties() {
            return properties;
        }
        
        public void setProperties(Map<String, Object> properties) {
            this.properties = properties;
        }
        
        public Long getDelayMillis() {
            return delayMillis;
        }
        
        public void setDelayMillis(Long delayMillis) {
            this.delayMillis = delayMillis;
        }
        
        public LocalDateTime getScheduleTime() {
            return scheduleTime;
        }
        
        public void setScheduleTime(LocalDateTime scheduleTime) {
            this.scheduleTime = scheduleTime;
        }
    }
    
    /**
     * 发送结果
     */
    class SendResult {
        private boolean success;
        private String messageId;
        private String errorCode;
        private String errorMessage;
        private long executionTimeMs;
        
        // 静态工厂方法
        public static SendResult success(String messageId) {
            SendResult result = new SendResult();
            result.success = true;
            result.messageId = messageId;
            return result;
        }
        
        public static SendResult failure(String errorCode, String errorMessage) {
            SendResult result = new SendResult();
            result.success = false;
            result.errorCode = errorCode;
            result.errorMessage = errorMessage;
            return result;
        }
        
        // Getters and Setters
        public boolean isSuccess() {
            return success;
        }
        
        public void setSuccess(boolean success) {
            this.success = success;
        }
        
        public String getMessageId() {
            return messageId;
        }
        
        public void setMessageId(String messageId) {
            this.messageId = messageId;
        }
        
        public String getErrorCode() {
            return errorCode;
        }
        
        public void setErrorCode(String errorCode) {
            this.errorCode = errorCode;
        }
        
        public String getErrorMessage() {
            return errorMessage;
        }
        
        public void setErrorMessage(String errorMessage) {
            this.errorMessage = errorMessage;
        }
        
        public long getExecutionTimeMs() {
            return executionTimeMs;
        }
        
        public void setExecutionTimeMs(long executionTimeMs) {
            this.executionTimeMs = executionTimeMs;
        }
    }
    
    /**
     * 事务回调接口
     */
    @FunctionalInterface
    interface TransactionCallback {
        /**
         * 执行事务操作
         * 
         * @return 是否成功
         */
        boolean execute();
    }
}