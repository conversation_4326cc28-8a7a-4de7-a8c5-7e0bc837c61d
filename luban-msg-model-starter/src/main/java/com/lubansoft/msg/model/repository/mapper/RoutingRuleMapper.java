package com.lubansoft.msg.model.repository.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import com.lubansoft.msg.model.repository.entity.RoutingRule;

import java.util.List;

/**
 * 路由规则Repository
 */
@Mapper
public interface RoutingRuleMapper extends BaseMapper<RoutingRule> {
    
    /**
     * 查找最大优先级
     * 
     * @return 最大优先级值
     */
    @Select("SELECT MAX(priority) FROM routing_rule WHERE deleted = 0")
    Integer findMaxPriority();
    
    /**
     * 查找所有启用的路由规则，按优先级升序排列
     * 
     * @return 启用的路由规则列表
     */
    @Select("SELECT * FROM routing_rule WHERE enabled = 1 AND deleted = 0 ORDER BY priority ASC")
    List<RoutingRule> findByEnabledTrueOrderByPriorityAsc();
    
    /**
     * 根据启用状态分页查询路由规则
     * 
     * @param pageNum 分页对象
     * @param enabled 启用状态
     * @return 分页结果
     */
    @Select("SELECT * FROM routing_rule WHERE enabled = #{enabled} AND deleted = 0 ORDER BY priority ASC")
    IPage<RoutingRule> findByEnabled(IPage<RoutingRule> pageNum, @Param("enabled") Boolean enabled);
    
    /**
     * 根据规则名称模糊查询（忽略大小写）
     * 
     * @param ruleName 规则名称
     * @return 匹配的规则列表
     */
    @Select("SELECT * FROM routing_rule WHERE LOWER(rule_name) LIKE LOWER(CONCAT('%', #{ruleName}, '%')) AND deleted = 0 ORDER BY priority ASC")
    List<RoutingRule> findByRuleNameContainingIgnoreCase(@Param("ruleName") String ruleName);
    
    /**
     * 统计启用的路由规则数量
     * 
     * @return 启用的规则数量
     */
    @Select("SELECT COUNT(*) FROM routing_rule WHERE enabled = 1 AND deleted = 0")
    long countByEnabledTrue();
    
    /**
     * 更新路由规则的启用状态
     * 
     * @param ruleId 规则ID
     * @param enabled 启用状态
     * @return 更新的记录数
     */
    @Update("UPDATE routing_rule SET enabled = #{enabled}, updated_at = NOW() WHERE rule_id = #{ruleId} AND deleted = 0")
    int updateEnabledByRuleId(@Param("ruleId") String ruleId, @Param("enabled") boolean enabled);
    
    /**
     * 更新路由规则的优先级
     * 
     * @param ruleId 规则ID
     * @param priority 优先级
     * @return 更新的记录数
     */
    @Update("UPDATE routing_rule SET priority = #{priority}, updated_at = NOW() WHERE rule_id = #{ruleId} AND deleted = 0")
    int updatePriorityByRuleId(@Param("ruleId") String ruleId, @Param("priority") Integer priority);
    
    // BaseMapper已提供基本的CRUD操作
    // 复杂查询请使用 RoutingRuleService
}