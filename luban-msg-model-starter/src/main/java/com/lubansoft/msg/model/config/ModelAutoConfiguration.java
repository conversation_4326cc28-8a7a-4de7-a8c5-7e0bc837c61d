package com.lubansoft.msg.model.config;

import org.mybatis.spring.annotation.MapperScan;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;

/**
 * Spring boot 自动配置类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Configuration
@EnableAsync
@ComponentScan(basePackages = {"com.lubansoft.msg.model"})
@MapperScan(basePackages = {"com.lubansoft.msg.model.repository.mapper"})
public class ModelAutoConfiguration {

    private static Logger logger = LoggerFactory.getLogger(ModelAutoConfiguration.class);


}