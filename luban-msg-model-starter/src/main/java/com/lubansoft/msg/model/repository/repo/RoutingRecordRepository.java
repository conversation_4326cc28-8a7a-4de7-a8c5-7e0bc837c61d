package com.lubansoft.msg.model.repository.repo;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lubansoft.msg.model.model.MessageRequestDTO;
import com.lubansoft.msg.model.repository.entity.RoutingRecord;
import com.lubansoft.msg.model.repository.mapper.RoutingRecordMapper;
import com.lubansoft.msg.model.service.routing.RoutingContext;
import com.lubansoft.msg.common.model.RoutingResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * 路由记录数据库服务
 */
@Service
public class RoutingRecordRepository extends ServiceImpl<RoutingRecordMapper, RoutingRecord> {

    private static final Logger logger = LoggerFactory.getLogger(RoutingRecordRepository.class);
    
    /**
     * 记录路由执行结果
     * 
     * @param request 消息请求
     * @param context 路由上下文
     * @param result 路由结果
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void recordRouting(MessageRequestDTO request, RoutingContext context, RoutingResult result) {
        try {
            RoutingRecord record = new RoutingRecord();
            
            // 基础信息（暂时使用固定值，待MessageRequestDTO结构确定后修复）
            record.setRequestId("temp-request-id"); // TODO: 修复为 request.getRequestId()
            record.setTemplateId("temp-template-id"); // TODO: 修复为 request.getTemplateId()
            record.setRouteSuccess(result.isMatched());
            record.setExecutionTimeMs((int) result.getExecutionTimeMs());
            
            if (result.isMatched()) {
                // 成功路由的信息
                record.setTargetChannels(result.getTargetChannels());
                
                // 单个规则匹配的情况（从上下文获取）
                Object matchedRuleId = result.getContext("matchedRuleId");
                Object matchedRuleName = result.getContext("matchedRuleName");
                if (matchedRuleId instanceof String) {
                    record.setMatchedRuleId((String) matchedRuleId);
                }
                if (matchedRuleName instanceof String) {
                    record.setMatchedRuleName((String) matchedRuleName);
                }
                
                // 多规则匹配的情况
                Object matchedRuleIds = result.getContext("matchedRuleIds");
                Object matchedRuleNames = result.getContext("matchedRuleNames");
                Object matchedRulesCount = result.getContext("matchedRulesCount");
                
                if (matchedRuleIds instanceof List) {
                    @SuppressWarnings("unchecked")
                    List<String> ruleIds = (List<String>) matchedRuleIds;
                    record.setMatchedRuleIds(ruleIds);
                    record.setMatchedRulesCount(ruleIds.size());
                }
                
                if (matchedRuleNames instanceof List) {
                    @SuppressWarnings("unchecked")
                    List<String> ruleNames = (List<String>) matchedRuleNames;
                    record.setMatchedRuleNames(ruleNames);
                }
                
                if (matchedRulesCount instanceof Integer) {
                    record.setMatchedRulesCount((Integer) matchedRulesCount);
                }
            }
            
            // 评估的规则数量
            Object evaluatedRules = result.getContext("evaluatedRules");
            if (evaluatedRules instanceof Integer) {
                record.setEvaluatedRulesCount((Integer) evaluatedRules);
            }
            
            // 保存记录
            save(record);
            
            logger.debug("路由记录保存成功: success={}, executionTime={}ms",
                        result.isMatched(), result.getExecutionTimeMs());
            
        } catch (Exception e) {
            logger.error("保存路由记录失败", e);
        }
    }
    
    /**
     * 根据请求ID查找路由记录
     * 
     * @param requestId 请求ID
     * @return 路由记录列表
     */
    public List<RoutingRecord> findByRequestId(String requestId) {
        return baseMapper.findByRequestId(requestId);
    }
    
    /**
     * 根据模板ID查找路由记录（分页）
     * 
     * @param templateId 模板ID
     * @param pageNum 页码
     * @param pageSize 页大小
     * @return 分页结果
     */
    public IPage<RoutingRecord> findByTemplateId(String templateId, int pageNum, int pageSize) {
        Page<RoutingRecord> page = new Page<>(pageNum, pageSize);
        return baseMapper.findByTemplateId(templateId, page);
    }
    
    /**
     * 根据路由结果查找记录（分页）
     * 
     * @param routeSuccess 路由是否成功
     * @param pageNum 页码
     * @param pageSize 页大小
     * @return 分页结果
     */
    public IPage<RoutingRecord> findByRouteSuccess(Boolean routeSuccess, int pageNum, int pageSize) {
        Page<RoutingRecord> page = new Page<>(pageNum, pageSize);
        return baseMapper.findByRouteSuccess(routeSuccess, page);
    }
    
    /**
     * 根据时间范围查找路由记录（分页）
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param pageNum 页码
     * @param pageSize 页大小
     * @return 分页结果
     */
    public IPage<RoutingRecord> findByTimeRange(Date startTime, Date endTime, int pageNum, int pageSize) {
        Page<RoutingRecord> page = new Page<>(pageNum, pageSize);
        return baseMapper.findByTimeRange(startTime, endTime, page);
    }
    
    /**
     * 根据匹配的规则ID查找记录
     * 
     * @param ruleId 规则ID
     * @return 路由记录列表
     */
    public List<RoutingRecord> findByMatchedRuleId(String ruleId) {
        return baseMapper.findByMatchedRuleId(ruleId);
    }
    
    /**
     * 根据目标渠道查找记录
     * 
     * @param channel 渠道名称
     * @return 路由记录列表
     */
    public List<RoutingRecord> findByTargetChannel(String channel) {
        return baseMapper.findByTargetChannel(channel);
    }
    
    /**
     * 获取路由统计信息
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 统计信息
     */
    public RoutingRecordMapper.RoutingStatistics getRoutingStatistics(Date startTime, Date endTime) {
        return baseMapper.getRoutingStatistics(startTime, endTime);
    }
    
    /**
     * 获取平均执行时间
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 平均执行时间（毫秒）
     */
    public Double getAverageExecutionTime(Date startTime, Date endTime) {
        return baseMapper.getAverageExecutionTime(startTime, endTime);
    }
    
    /**
     * 获取各模板的路由统计
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 模板统计列表
     */
    public List<RoutingRecordMapper.TemplateRoutingStatistics> getTemplateRoutingStatistics(Date startTime, Date endTime) {
        return baseMapper.getTemplateRoutingStatistics(startTime, endTime);
    }
    
    /**
     * 获取各规则的匹配统计
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 规则统计列表
     */
    public List<RoutingRecordMapper.RuleMatchStatistics> getRuleMatchStatistics(Date startTime, Date endTime) {
        return baseMapper.getRuleMatchStatistics(startTime, endTime);
    }
    
    /**
     * 查找路由失败的记录
     * 
     * @return 失败记录列表
     */
    public List<RoutingRecord> findFailedRoutings() {
        return list(new LambdaQueryWrapper<RoutingRecord>()
                .eq(RoutingRecord::getRouteSuccess, false)
                .orderByDesc(RoutingRecord::getCreatedAt));
    }
    
    /**
     * 删除过期的路由记录
     * 
     * @param beforeTime 删除此时间之前的记录
     * @return 删除的记录数
     */
    @Transactional
    public int deleteExpiredRecords(Date beforeTime) {
        logger.info("删除过期的路由记录: beforeTime={}", beforeTime);
        
        int deleted = baseMapper.deleteExpiredRecords(beforeTime);
        
        logger.info("删除过期路由记录完成: count={}", deleted);
        return deleted;
    }
}
