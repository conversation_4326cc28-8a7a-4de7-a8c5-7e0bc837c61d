package com.lubansoft.msg.model.service.routing;

import com.lubansoft.msg.common.model.RoutingResult;
import com.lubansoft.msg.model.model.MessageRequestDTO;
import com.lubansoft.msg.model.repository.entity.RoutingRule;
import com.lubansoft.msg.model.service.ReceiverSubscriptionChecker;
import com.lubansoft.msg.model.repository.repo.RoutingRuleService;
import lombok.Getter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 基于SpEL的路由引擎实现
 */
@Component
public class SpelRoutingEngine implements RoutingEngine {
    
    private static final Logger logger = LoggerFactory.getLogger(SpelRoutingEngine.class);
    
    @Autowired
    private RuleEvaluator ruleEvaluator;
    
    @Autowired
    private RoutingRuleService routingRuleService;

    // 配置开关
    private boolean cacheEnabled = false;
    private boolean statisticsEnabled = false;
    
    // 路由统计信息
    private final Map<String, RoutingStatistics> statisticsMap = new ConcurrentHashMap<>();
    
    @Override
    public RoutingResult route(MessageRequestDTO request, RoutingContext context) {
        logger.debug("开始路由消息: templateId={}, appId={}", request.getTemplateId(), context.getAppId());
        
        long startTime = System.currentTimeMillis();
        
        try {
            // 根据模板ID获取对应的路由规则
            List<RoutingRule> rules = getRoutingRulesForTemplate(request.getTemplateId());
            
            if (rules.isEmpty()) {
                logger.info("没有可用的路由规则: templateId={}", request.getTemplateId());
                RoutingResult result = RoutingResult.failure("没有找到模板对应的路由规则: " + request.getTemplateId());
                result.addContext("errorCode", "NO_ROUTING_RULES");
                return result;
            }
            
            logger.debug("找到{}条路由规则: templateId={}", rules.size(), request.getTemplateId());

            // 收集所有匹配的规则和渠道
            List<String> allMatchedChannels = new ArrayList<>();
            List<String> matchedRuleIds = new ArrayList<>();
            List<String> matchedRuleNames = new ArrayList<>();
            int highestPriority = 0;

            // 按优先级顺序评估所有规则
            for (RoutingRule rule : rules) {
                try {
                    boolean matched = ruleEvaluator.evaluate(rule, request, context);

                    if (matched) {
                        // 获取规则绑定的渠道
                        String boundChannel = rule.getTargetChannel();

                        if (boundChannel == null) {
                            logger.info("规则匹配但绑定渠道为空: ruleId={}", rule.getRuleId());
                            continue;
                        }

                        // 收集匹配的规则信息
                        matchedRuleIds.add(rule.getRuleId());
                        matchedRuleNames.add(rule.getRuleName());


                        // 添加到匹配渠道列表（避免重复）
                        if (!allMatchedChannels.contains(boundChannel)) {
                            allMatchedChannels.add(boundChannel);
                        }

                        // 记录最高优先级
                        if (rule.getPriority() > highestPriority) {
                            highestPriority = rule.getPriority();
                        }

                        // 更新统计信息
                        updateStatistics(rule.getRuleId(), true, 0);

                        logger.debug("规则匹配成功: ruleId={}, boundChannel={}, priority={}",
                                   rule.getRuleId(), boundChannel, rule.getPriority());
                    }
                } catch (Exception e) {
                    logger.error("规则评估异常: ruleId={}", rule.getRuleId(), e);
                    updateStatistics(rule.getRuleId(), false, 0);
                    // 继续尝试下一个规则
                }
            }

            // 检查是否有匹配的规则
            if (!allMatchedChannels.isEmpty()) {
                long executionTime = System.currentTimeMillis() - startTime;

                RoutingResult result = RoutingResult.success(
                    String.join(",", matchedRuleIds),
                    String.join(",", matchedRuleNames),
                    allMatchedChannels,
                    highestPriority
                );
                result.setExecutionTimeMs(executionTime);
                result.addContext("evaluatedRules", rules.size());
                result.addContext("matchedRulesCount", matchedRuleIds.size());
                result.addContext("matchedRuleIds", matchedRuleIds);
                result.addContext("matchedRuleNames", matchedRuleNames);

                // 记录路由信息到数据库（替代日志打印）
                // routingRecordRepository.recordRouting(request, context, result);

                return result;
            }
            
            // 没有匹配的规则
            long executionTime = System.currentTimeMillis() - startTime;
            logger.info("没有匹配的路由规则: templateId={}, appId={}, evaluatedRules={}", 
                       request.getTemplateId(), context.getAppId(), rules.size());
            
            RoutingResult result = RoutingResult.failure("没有匹配的路由规则");
            result.addContext("errorCode", "NO_MATCHING_RULE");
            result.setExecutionTimeMs(executionTime);
            result.addContext("evaluatedRules", rules.size());
            
            // 记录路由失败信息到数据库
            // routingRecordRepository.recordRouting(request, context, result);
            
            return result;
            
        } catch (Exception e) {
            long executionTime = System.currentTimeMillis() - startTime;
            logger.error("路由引擎执行异常: templateId={}, appId={}", 
                        request.getTemplateId(), context.getAppId(), e);
            
            RoutingResult result = RoutingResult.failure("路由引擎执行异常: " + e.getMessage());
            result.addContext("errorCode", "ROUTING_ENGINE_ERROR");
            result.setExecutionTimeMs(executionTime);
            
            // 记录路由异常信息到数据库
            // routingRecordRepository.recordRouting(request, context, result);
            
            return result;
        }
    }
    
    @Override
    public List<RoutingResult> routeBatch(List<MessageRequestDTO> requests, RoutingContext context) {
        logger.debug("开始批量路由: requestCount={}, appId={}", requests.size(), context.getAppId());
        
        List<RoutingResult> results = new ArrayList<>();
        
        for (MessageRequestDTO request : requests) {
            try {
                RoutingResult result = route(request, context);
                results.add(result);
            } catch (Exception e) {
                logger.error("批量路由中单个请求失败: templateId={}", request.getTemplateId(), e);
                
                RoutingResult errorResult = RoutingResult.failure("批量路由异常: " + e.getMessage());
                errorResult.addContext("errorCode", "BATCH_ROUTING_ERROR");
                results.add(errorResult);
            }
        }
        
        logger.debug("批量路由完成: requestCount={}, successCount={}", 
                    requests.size(),
                    results.stream().mapToLong(r -> r.isMatched() ? 1 : 0).sum());
        
        // 记录批量路由信息（逐个记录）
        // for (int i = 0; i < requests.size() && i < results.size(); i++) {
        //     routingRecordRepository.recordRouting(requests.get(i), context, results.get(i));
        // }
        
        return results;
    }
    
    @Override
    public void reloadRules() {
        logger.info("重新加载路由规则");
        
        long startTime = System.currentTimeMillis();
        
        try {
            // 清除缓存
            // if (cacheEnabled) {
            //     cacheService.delete(RULES_CACHE_KEY);
            //     logger.debug("清除路由规则缓存");
            // }
            
            // 预加载规则到缓存
            List<RoutingRule> rules = loadRulesFromDatabase();
            // if (cacheEnabled && !rules.isEmpty()) {
            //     cacheService.set(RULES_CACHE_KEY, rules, Duration.ofMinutes(RULES_CACHE_EXPIRE_SECONDS));
            //     logger.debug("预加载路由规则到缓存: count={}", rules.size());
            // }
            
            logger.info("路由规则重新加载完成: count={}", rules.size());
            
            // 记录规则重新加载（暂时使用日志，后续可扩展为专门的记录表）
            logger.info("路由规则重新加载记录: count={}, duration={}ms", rules.size(), System.currentTimeMillis() - startTime);
            
        } catch (Exception e) {
            logger.error("重新加载路由规则失败", e);
        }
    }
    
    @Override
    @SuppressWarnings("unchecked")
    public List<RoutingRule> getEnabledRules() {
        // if (!cacheEnabled) {
            return loadRulesFromDatabase();
        // }
        
        // try {
        //     // 先从缓存获取
        //     List<RoutingRule> cachedRules = (List<RoutingRule>) cacheService.get(RULES_CACHE_KEY);
        //     
        //     if (cachedRules != null) {
        //         logger.debug("从缓存获取路由规则: count={}", cachedRules.size());
        //         return cachedRules;
        //     }
        //     
        //     // 缓存未命中，从数据库加载
        //     List<RoutingRule> rules = loadRulesFromDatabase();
        //     
        //     // 存入缓存
        //     if (!rules.isEmpty()) {
        //         cacheService.set(RULES_CACHE_KEY, rules, Duration.ofMinutes(RULES_CACHE_EXPIRE_SECONDS));
        //         logger.debug("路由规则存入缓存: count={}", rules.size());
        //     }
        //     
        //     return rules;
        //     
        // } catch (Exception e) {
        //     logger.error("获取路由规则失败，尝试从数据库直接加载", e);
        //     return loadRulesFromDatabase();
        // }
    }
    
    /**
     * 从数据库加载路由规则
     */
    private List<RoutingRule> loadRulesFromDatabase() {
        try {
            List<RoutingRule> rules = routingRuleService.findEnabledRulesOrderByPriority();
            logger.debug("从数据库加载路由规则: count={}", rules.size());
            return rules;
        } catch (Exception e) {
            logger.error("从数据库加载路由规则失败", e);
            return new ArrayList<>();
        }
    }
    
    /**
     * 根据模板ID获取对应的路由规则
     * 
     * @param templateId 模板ID
     * @return 路由规则列表，按优先级排序
     */
    private List<RoutingRule> getRoutingRulesForTemplate(String templateId) {
        if (templateId == null || templateId.trim().isEmpty()) {
            logger.info("模板ID为空，使用通用路由规则");
            return routingRuleService.findGeneralRules();
        }
        
        try {
            // 获取模板专用规则和通用规则
            List<RoutingRule> rules = routingRuleService.findByTemplateIdAndEnabled(templateId);
            
            logger.debug("获取模板路由规则: templateId={}, count={}", templateId, rules.size());
            
            return rules;
            
        } catch (Exception e) {
            logger.error("获取模板路由规则失败: templateId={}", templateId, e);
            // 降级到通用规则
            return routingRuleService.findGeneralRules();
        }
    }
    
    /**
     * 更新统计信息
     */
    private void updateStatistics(String ruleId, boolean success, long executionTime) {
        if (!statisticsEnabled) {
            return;
        }
        
        try {
            RoutingStatistics stats = statisticsMap.computeIfAbsent(ruleId, 
                k -> new RoutingStatistics(ruleId));
            
            stats.incrementTotalCount();
            stats.addExecutionTime(executionTime);
            
            if (success) {
                stats.incrementSuccessCount();
            } else {
                stats.incrementFailureCount();
            }
            
        } catch (Exception e) {
            logger.error("更新路由统计信息失败: ruleId={}", ruleId, e);
        }
    }
    
    /**
     * 获取路由统计信息
     */
    public Map<String, RoutingStatistics> getRoutingStatistics() {
        return new HashMap<>(statisticsMap);
    }
    
    /**
     * 重置统计信息
     */
    public void resetStatistics() {
        statisticsMap.clear();
        logger.info("路由统计信息已重置");
    }
    
    /**
     * 获取路由引擎状态信息
     */
    public Map<String, Object> getEngineStatus() {
        Map<String, Object> status = new HashMap<>();
        status.put("cacheEnabled", cacheEnabled);
        status.put("statisticsEnabled", statisticsEnabled);
        status.put("rulesCount", getEnabledRules().size());
        status.put("statisticsCount", statisticsMap.size());
        
        // 计算总体统计
        long totalRequests = statisticsMap.values().stream()
                .mapToLong(RoutingStatistics::getTotalCount)
                .sum();
        long totalSuccess = statisticsMap.values().stream()
                .mapToLong(RoutingStatistics::getSuccessCount)
                .sum();
        
        status.put("totalRequests", totalRequests);
        status.put("totalSuccess", totalSuccess);
        status.put("overallSuccessRate", totalRequests > 0 ? (double) totalSuccess / totalRequests : 0.0);
        
        return status;
    }
    
    /**
     * 路由统计信息
     */
    public static class RoutingStatistics {
        // Getters
        @Getter
        private final String ruleId;
        @Getter
        private long totalCount = 0;
        @Getter
        private long successCount = 0;
        private long failureCount = 0;
        private long totalExecutionTime = 0;
        private long maxExecutionTime = 0;
        private long minExecutionTime = Long.MAX_VALUE;
        
        public RoutingStatistics(String ruleId) {
            this.ruleId = ruleId;
        }
        
        public synchronized void incrementTotalCount() {
            this.totalCount++;
        }
        
        public synchronized void incrementSuccessCount() {
            this.successCount++;
        }
        
        public synchronized void incrementFailureCount() {
            this.failureCount++;
        }
        
        public synchronized void addExecutionTime(long executionTime) {
            this.totalExecutionTime += executionTime;
            this.maxExecutionTime = Math.max(this.maxExecutionTime, executionTime);
            this.minExecutionTime = Math.min(this.minExecutionTime, executionTime);
        }
        
        public double getSuccessRate() {
            return totalCount > 0 ? (double) successCount / totalCount : 0.0;
        }
        
        public double getAverageExecutionTime() {
            return totalCount > 0 ? (double) totalExecutionTime / totalCount : 0.0;
        }

        public long getFailureCount() {
            return failureCount;
        }
        
        public long getTotalExecutionTime() {
            return totalExecutionTime;
        }
        
        public long getMaxExecutionTime() {
            return maxExecutionTime;
        }
        
        public long getMinExecutionTime() {
            return minExecutionTime == Long.MAX_VALUE ? 0 : minExecutionTime;
        }
    }
}