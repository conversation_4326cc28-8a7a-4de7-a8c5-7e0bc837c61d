package com.lubansoft.msg.model.service.processor;

import com.lubansoft.msg.model.model.MessageRequestDTO;
import com.lubansoft.msg.common.util.IdGenerator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * 消息预处理管道
 */
@Component
public class MessagePreprocessingPipeline {
    
    private static final Logger logger = LoggerFactory.getLogger(MessagePreprocessingPipeline.class);
    
    private final List<MessagePreprocessor> preprocessors = new ArrayList<>();
    
    public MessagePreprocessingPipeline() {
        // 注册预处理器（按执行顺序）
        preprocessors.add(new MessageIdPreprocessor());
        preprocessors.add(new DefaultValuePreprocessor());
        preprocessors.add(new ParameterValidationPreprocessor());
        preprocessors.add(new PriorityCalculationPreprocessor());
    }
    
    /**
     * 执行预处理管道
     * 
     * @param request 消息请求
     * @param context 处理上下文
     * @return 预处理结果
     */
    public PreprocessingResult process(MessageRequestDTO request, ProcessingContext context) {
        logger.debug("开始执行消息预处理管道: messageId={}", request.getMessageId());
        
        PreprocessingResult result = new PreprocessingResult();
        result.setRequest(request);
        result.setContext(context);
        
        for (MessagePreprocessor preprocessor : preprocessors) {
            try {
                logger.debug("执行预处理器: {}", preprocessor.getClass().getSimpleName());
                
                long startTime = System.currentTimeMillis();
                preprocessor.process(result);
                long duration = System.currentTimeMillis() - startTime;
                
                result.addExecutionTime(preprocessor.getClass().getSimpleName(), duration);
                
                // 如果预处理器返回错误，停止管道执行
                if (!result.isSuccess()) {
                    logger.info("预处理器执行失败: {}, error={}", 
                               preprocessor.getClass().getSimpleName(), result.getErrorMessage());
                    break;
                }
                
            } catch (Exception e) {
                logger.error("预处理器执行异常: {}", preprocessor.getClass().getSimpleName(), e);
                result.setError("预处理器执行异常: " + e.getMessage());
                break;
            }
        }
        
        logger.debug("消息预处理管道执行完成: success={}, totalTime={}ms", 
                    result.isSuccess(), result.getTotalExecutionTime());
        
        return result;
    }
    
    /**
     * 消息预处理器接口
     */
    public interface MessagePreprocessor {
        void process(PreprocessingResult result);
    }
    
    /**
     * 消息ID预处理器
     */
    public static class MessageIdPreprocessor implements MessagePreprocessor {
        @Override
        public void process(PreprocessingResult result) {
            MessageRequestDTO request = result.getRequest();
            if (request.getMessageId() == null || request.getMessageId().trim().isEmpty()) {
                String messageId = IdGenerator.generateId();
                request.setMessageId(messageId);
                result.addProcessingNote("生成消息ID: " + messageId);
            }
        }
    }
    
    /**
     * 默认值预处理器
     */
    public static class DefaultValuePreprocessor implements MessagePreprocessor {
        @Override
        public void process(PreprocessingResult result) {
            MessageRequestDTO request = result.getRequest();
            
            if (request.getPriority() == null) {
                request.setPriority(5);
                result.addProcessingNote("设置默认优先级: 5");
            }
        }
    }
    
    /**
     * 参数验证预处理器
     */
    public static class ParameterValidationPreprocessor implements MessagePreprocessor {
        @Override
        public void process(PreprocessingResult result) {
            MessageRequestDTO request = result.getRequest();
            
            // 修正优先级范围
            if (request.getPriority() < 1) {
                request.setPriority(1);
                result.addProcessingNote("优先级修正为最小值: 1");
            } else if (request.getPriority() > 10) {
                request.setPriority(10);
                result.addProcessingNote("优先级修正为最大值: 10");
            }
        }
    }
    
    /**
     * 优先级计算预处理器
     */
    public static class PriorityCalculationPreprocessor implements MessagePreprocessor {
        @Override
        public void process(PreprocessingResult result) {
            MessageRequestDTO request = result.getRequest();
            ProcessingContext context = result.getContext();
            
            int priorityScore = calculatePriorityScore(request);
            context.setPriorityScore(priorityScore);
            
            result.addProcessingNote("计算优先级分数: " + priorityScore);
        }
        
        private int calculatePriorityScore(MessageRequestDTO request) {
            int score = request.getPriority() * 10;
            
            // 批量消息根据数量调整优先级
            int receiverCount = request.getReceivers().size();
            if (receiverCount > 100) {
                score -= 10; // 大批量消息降低优先级
            } else if (receiverCount > 10) {
                score -= 5;  // 中等批量消息稍微降低优先级
            }
            
            return Math.max(score, 1); // 确保最小优先级为1
        }
    }
    
    /**
     * 预处理结果
     */
    public static class PreprocessingResult {
        private boolean success = true;
        private String errorMessage;
        private MessageRequestDTO request;
        private ProcessingContext context;
        private List<String> processingNotes = new ArrayList<>();
        private java.util.Map<String, Long> executionTimes = new java.util.HashMap<>();
        
        public void setError(String errorMessage) {
            this.success = false;
            this.errorMessage = errorMessage;
        }
        
        public void addProcessingNote(String note) {
            this.processingNotes.add(note);
        }
        
        public void addExecutionTime(String step, long duration) {
            this.executionTimes.put(step, duration);
        }
        
        public long getTotalExecutionTime() {
            return executionTimes.values().stream().mapToLong(Long::longValue).sum();
        }
        
        // Getters and Setters
        public boolean isSuccess() {
            return success;
        }
        
        public void setSuccess(boolean success) {
            this.success = success;
        }
        
        public String getErrorMessage() {
            return errorMessage;
        }
        
        public void setErrorMessage(String errorMessage) {
            this.errorMessage = errorMessage;
        }
        
        public MessageRequestDTO getRequest() {
            return request;
        }
        
        public void setRequest(MessageRequestDTO request) {
            this.request = request;
        }
        
        public ProcessingContext getContext() {
            return context;
        }
        
        public void setContext(ProcessingContext context) {
            this.context = context;
        }
        
        public List<String> getProcessingNotes() {
            return processingNotes;
        }
        
        public java.util.Map<String, Long> getExecutionTimes() {
            return executionTimes;
        }
    }
    
    /**
     * 处理上下文
     */
    public static class ProcessingContext {
        private String appId;
        private String requestId;
        private java.time.LocalDateTime requestTime;
        private int priorityScore;
        private java.util.Map<String, Object> attributes = new java.util.HashMap<>();
        
        public ProcessingContext(String appId) {
            this.appId = appId;
            this.requestTime = java.time.LocalDateTime.now();
        }
        
        public void setAttribute(String key, Object value) {
            attributes.put(key, value);
        }
        
        @SuppressWarnings("unchecked")
        public <T> T getAttribute(String key, Class<T> type) {
            Object value = attributes.get(key);
            if (value != null && type.isInstance(value)) {
                return (T) value;
            }
            return null;
        }
        
        // Getters and Setters
        public String getAppId() {
            return appId;
        }
        
        public void setAppId(String appId) {
            this.appId = appId;
        }
        
        public String getRequestId() {
            return requestId;
        }
        
        public void setRequestId(String requestId) {
            this.requestId = requestId;
        }
        
        public java.time.LocalDateTime getRequestTime() {
            return requestTime;
        }
        
        public void setRequestTime(java.time.LocalDateTime requestTime) {
            this.requestTime = requestTime;
        }
        
        public int getPriorityScore() {
            return priorityScore;
        }
        
        public void setPriorityScore(int priorityScore) {
            this.priorityScore = priorityScore;
        }
        
        public java.util.Map<String, Object> getAttributes() {
            return attributes;
        }
    }
}