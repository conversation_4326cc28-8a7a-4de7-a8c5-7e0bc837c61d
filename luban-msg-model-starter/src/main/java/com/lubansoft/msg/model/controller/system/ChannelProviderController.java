package com.lubansoft.msg.model.controller.system;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.lubansoft.msg.model.controller.system.request.RegisterParam;
import com.lubansoft.msg.model.repository.entity.ChannelConfig;
import com.lubansoft.msg.model.repository.entity.ChannelConfigData;
import com.lubansoft.msg.model.service.ChannelConfigService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/provider")
@Tag(name = "渠道提供者", description = "渠道提供者")
public class ChannelProviderController {

    @Autowired
    private ChannelConfigService channelConfigService;

    @Operation(summary = "注册")
    @ApiOperationSupport(order = 10)
    @RequestMapping(value = "/register", method = RequestMethod.POST, produces = "application/json")
    public String create(@Validated @RequestBody RegisterParam param) throws Exception {
        // 根据渠道类型查询是否已存在配置
        List<ChannelConfig> existingConfigs = channelConfigService.getChannelConfigsByType(param.getChannelType());
        
        ChannelConfig channelConfig;
        if (existingConfigs != null && !existingConfigs.isEmpty()) {
            // 如果存在相同类型的配置，则更新第一条记录
            channelConfig = existingConfigs.get(0);
            channelConfig.setChannelName(param.getChannelName());
            channelConfig.setEnabled(true);
        } else {
            // 如果不存在相同类型的配置，则创建新记录
            channelConfig = new ChannelConfig();
            channelConfig.setChannelName(param.getChannelName());
            channelConfig.setChannelType(param.getChannelType());
            channelConfig.setEnabled(true);
        }
        
        // 设置配置数据
        ChannelConfigData configData = new ChannelConfigData();
        configData.setBaseUrl(param.getBaseUrl());
        configData.setHealthApi(param.getHealthApi());
        configData.setMsgApi(param.getMsgApi());
        channelConfig.setConfigData(configData);
        
        // 保存或更新配置
        if (channelConfig.getChannelId() != null) {
            channelConfigService.updateChannelConfig(channelConfig);
        } else {
            channelConfigService.saveChannelConfig(channelConfig);
        }
        
        return "注册成功";
    }

}