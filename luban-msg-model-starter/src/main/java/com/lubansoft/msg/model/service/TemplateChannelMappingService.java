package com.lubansoft.msg.model.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lubansoft.msg.common.util.IdGenerator;
import com.lubansoft.msg.model.repository.entity.TemplateChannelMapping;
import com.lubansoft.msg.model.repository.mapper.TemplateChannelMappingMapper;
import com.lubansoft.msg.model.service.consumer.ChannelParameterResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 模板渠道映射配置服务类
 */
@Service
public class TemplateChannelMappingService extends ServiceImpl<TemplateChannelMappingMapper, TemplateChannelMapping> {
    
    private static final Logger logger = LoggerFactory.getLogger(TemplateChannelMappingService.class);
    
    /**
     * 创建模板渠道映射配置
     * 
     * @param mapping 映射配置
     * @return 创建的映射配置
     */
    @Transactional
    public TemplateChannelMapping createMapping(TemplateChannelMapping mapping) {
        logger.info("创建模板渠道映射配置: templateId={}, channelType={}",
                   mapping.getTemplateId(), mapping.getChannelType());
        
        // 验证映射配置
        validateMapping(mapping);
        
        // 检查是否已存在相同的映射
        if (existsMapping(mapping.getTemplateId(), mapping.getChannelType())) {
            throw new IllegalArgumentException("模板和渠道类型的映射关系已存在: templateId=" +
                                             mapping.getTemplateId() + ", channelType=" + mapping.getChannelType());
        }
        
        // 设置基础信息
        if (!StringUtils.hasText(mapping.getMappingId())) {
            mapping.setMappingId(IdGenerator.generateId());
        }
        

        
        mapping.setCreatedAt(LocalDateTime.now());
        mapping.setUpdatedAt(LocalDateTime.now());
        
        // 保存映射配置
        save(mapping);
        
        return mapping;
    }
    
    /**
     * 更新模板渠道映射配置
     * 
     * @param mappingId 映射ID
     * @param mapping 映射配置
     * @return 更新后的映射配置
     */
    @Transactional
    public TemplateChannelMapping updateMapping(String mappingId, TemplateChannelMapping mapping) {
        logger.info("更新模板渠道映射配置: mappingId={}", mappingId);
        
        // 查找现有映射配置
        TemplateChannelMapping existingMapping = getById(mappingId);
        if (existingMapping == null) {
            throw new IllegalArgumentException("映射配置不存在: " + mappingId);
        }
        
        // 验证映射配置
        validateMapping(mapping);
        
        // 如果模板ID或渠道类型发生变化，需要检查新的组合是否已存在
        if (!existingMapping.getTemplateId().equals(mapping.getTemplateId()) ||
            !existingMapping.getChannelType().equals(mapping.getChannelType())) {

            if (existsMapping(mapping.getTemplateId(), mapping.getChannelType())) {
                throw new IllegalArgumentException("模板和渠道类型的映射关系已存在: templateId=" +
                                                 mapping.getTemplateId() + ", channelType=" + mapping.getChannelType());
            }
        }
        
        // 更新映射信息
        existingMapping.setTemplateId(mapping.getTemplateId());
        existingMapping.setChannelType(mapping.getChannelType());
        existingMapping.setParameterMappingJson(mapping.getParameterMappingJson());
        existingMapping.setChannelTemplateContent(mapping.getChannelTemplateContent());
        existingMapping.setChannelTemplateVariablesJson(mapping.getChannelTemplateVariablesJson());
        existingMapping.setExtendedConfigJson(mapping.getExtendedConfigJson());
        existingMapping.setUpdatedBy(mapping.getUpdatedBy());
        existingMapping.setUpdatedAt(LocalDateTime.now());
        
        // 保存更新
        updateById(existingMapping);

        return existingMapping;
    }
    
    /**
     * 根据模板ID和渠道类型获取特定的映射配置
     *
     * @param templateId 模板ID
     * @param channelType 渠道类型
     * @return 映射配置
     */
    public Optional<TemplateChannelMapping> getMappingByTemplateAndChannelType(String templateId, String channelType) {
        TemplateChannelMapping mapping = baseMapper.findByTemplateIdAndChannelType(templateId, channelType);
        return Optional.ofNullable(mapping);
    }
    
    /**
     * 应用渠道参数映射
     * 根据模板ID和渠道类型，将模板参数转换为渠道参数，并获取全局参数
     *
     * @param templateId 模板ID
     * @param channelType 渠道类型
     * @param templateParams 模板参数
     * @return 渠道参数结果（包含转换后的模板参数和全局参数）
     */
    public ChannelParameterResult applyChannelParameterMapping(String templateId, String channelType, Map<String, Object> templateParams) {
//        Optional<TemplateChannelMapping> mappingOpt = getMappingByTemplateAndChannelType(templateId, channelType);
//
//        if (mappingOpt.isPresent() && mappingOpt.get().isValidMapping()) {
//            TemplateChannelMapping mapping = mappingOpt.get();
//
//            // 转换模板参数
//            Map<String, Object> channelTemplateParams = mapping.applyTemplateParameterMapping(templateParams);
//
//            // 获取全局参数
//            Map<String, Object> globalParams = mapping.getGlobalParam();
//
//            return new ChannelParameterResult(channelTemplateParams, globalParams);
//        } else {
//            Map<String, Object> originalParams = templateParams != null ? new HashMap<>(templateParams) : new HashMap<>();
//            return new ChannelParameterResult(originalParams, new HashMap<>());
//        }
        return null;
    }
    
    /**
     * 分页查询映射配置
     * 
     * @param pageNum 页码
     * @param pageSize 页大小
     * @param enabled 启用状态（可选）
     * @return 分页结果
     */
    public IPage<TemplateChannelMapping> getMappingsWithPaging(int pageNum, int pageSize, Boolean enabled) {
        logger.debug("分页查询映射配置: pageNum={}, pageSize={}, enabled={}", pageNum, pageSize, enabled);
        
        Page<TemplateChannelMapping> page = new Page<>(pageNum, pageSize);
        
        if (enabled != null) {
            return baseMapper.findByEnabledWithPaging(page, enabled);
        } else {
            return baseMapper.findAllEnabledWithPaging(page);
        }
    }
    
    /**
     * 根据映射名称搜索映射配置
     * 
     * @param mappingName 映射名称关键词
     * @return 映射配置列表
     */
    public List<TemplateChannelMapping> searchMappingsByName(String mappingName) {
        logger.debug("根据名称搜索映射配置: mappingName={}", mappingName);
        
        return baseMapper.findByMappingNameContaining(mappingName);
    }
    
    /**
     * 批量更新映射配置的启用状态
     * 
     * @param mappingIds 映射ID列表
     * @param enabled 启用状态
     * @param updatedBy 更新者
     * @return 更新的记录数
     */
    @Transactional
    public int batchUpdateEnabled(List<String> mappingIds, Boolean enabled, String updatedBy) {
        logger.info("批量更新映射配置启用状态: mappingIds={}, enabled={}, updatedBy={}", 
                   mappingIds.size(), enabled, updatedBy);
        
        if (mappingIds == null || mappingIds.isEmpty()) {
            return 0;
        }
        
        int updatedCount = baseMapper.batchUpdateEnabled(mappingIds, enabled, updatedBy);
        
        logger.info("批量更新映射配置启用状态完成: updatedCount={}", updatedCount);
        
        return updatedCount;
    }
    
    /**
     * 验证映射配置
     * 
     * @param mapping 映射配置
     */
    private void validateMapping(TemplateChannelMapping mapping) {
        if (mapping == null) {
            throw new IllegalArgumentException("映射配置不能为空");
        }
        
        if (!StringUtils.hasText(mapping.getTemplateId())) {
            throw new IllegalArgumentException("模板ID不能为空");
        }
        

        
        if (!StringUtils.hasText(mapping.getChannelType())) {
            throw new IllegalArgumentException("渠道类型不能为空");
        }
    }
    
    /**
     * 统计某个模板的渠道映射数量
     * 
     * @param templateId 模板ID
     * @return 映射数量
     */
    public int countMappingsByTemplateId(String templateId) {
        return baseMapper.countByTemplateId(templateId);
    }
    
    /**
     * 统计某个渠道的模板映射数量
     * 
     * @param channelId 渠道ID
     * @return 映射数量
     */
    public int countMappingsByChannelId(String channelId) {
        return baseMapper.countByChannelId(channelId);
    }
    
    /**
     * 统计启用的映射配置数量
     *
     * @return 启用的映射数量
     */
    public int countEnabledMappings() {
        return baseMapper.countEnabledMappings();
    }

    /**
     * 批量删除模板的所有映射配置
     *
     * @param templateId 模板ID
     * @param updatedBy 更新者
     * @return 删除的记录数
     */
    @Transactional
    public int deleteMappingsByTemplateId(String templateId, String updatedBy) {
        logger.info("删除模板的所有映射配置: templateId={}, updatedBy={}", templateId, updatedBy);

        int deletedCount = baseMapper.deleteByTemplateId(templateId, updatedBy);

        logger.info("删除模板映射配置完成: templateId={}, deletedCount={}", templateId, deletedCount);

        return deletedCount;
    }

    /**
     * 批量删除渠道的所有映射配置
     *
     * @param channelId 渠道ID
     * @param updatedBy 更新者
     * @return 删除的记录数
     */
    @Transactional
    public int deleteMappingsByChannelId(String channelId, String updatedBy) {
        logger.info("删除渠道的所有映射配置: channelId={}, updatedBy={}", channelId, updatedBy);

        int deletedCount = baseMapper.deleteByChannelId(channelId, updatedBy);

        logger.info("删除渠道映射配置完成: channelId={}, deletedCount={}", channelId, deletedCount);

        return deletedCount;
    }

    /**
     * 检查映射配置是否存在
     *
     * @param templateId 模板ID
     * @param channelType 渠道类型
     * @return 是否存在
     */
    public boolean existsMapping(String templateId, String channelType) {
        return getMappingByTemplateAndChannelType(templateId, channelType).isPresent();
    }

}
