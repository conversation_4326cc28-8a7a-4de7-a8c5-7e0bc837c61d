package com.lubansoft.msg.model.service.routing;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 路由引擎配置
 */
@Configuration
@ConfigurationProperties(prefix = "message.gateway.routing")
public class RoutingEngineConfig {
    
    /**
     * 是否启用缓存
     */
    private boolean cacheEnabled = true;
    
    /**
     * 缓存过期时间（秒）
     */
    private int cacheExpireSeconds = 300;
    
    /**
     * 是否启用统计
     */
    private boolean statisticsEnabled = true;
    
    /**
     * 规则评估超时时间（毫秒）
     */
    private long evaluationTimeoutMs = 1000;
    
    /**
     * 最大规则数量
     */
    private int maxRulesCount = 1000;
    
    /**
     * 是否启用规则预编译
     */
    private boolean precompileEnabled = true;
    
    /**
     * 表达式缓存大小
     */
    private int expressionCacheSize = 500;
    
    /**
     * 正则表达式缓存大小
     */
    private int patternCacheSize = 200;
    
    // Getters and Setters
    public boolean isCacheEnabled() {
        return cacheEnabled;
    }
    
    public void setCacheEnabled(boolean cacheEnabled) {
        this.cacheEnabled = cacheEnabled;
    }
    
    public int getCacheExpireSeconds() {
        return cacheExpireSeconds;
    }
    
    public void setCacheExpireSeconds(int cacheExpireSeconds) {
        this.cacheExpireSeconds = cacheExpireSeconds;
    }
    
    public boolean isStatisticsEnabled() {
        return statisticsEnabled;
    }
    
    public void setStatisticsEnabled(boolean statisticsEnabled) {
        this.statisticsEnabled = statisticsEnabled;
    }
    
    public long getEvaluationTimeoutMs() {
        return evaluationTimeoutMs;
    }
    
    public void setEvaluationTimeoutMs(long evaluationTimeoutMs) {
        this.evaluationTimeoutMs = evaluationTimeoutMs;
    }
    
    public int getMaxRulesCount() {
        return maxRulesCount;
    }
    
    public void setMaxRulesCount(int maxRulesCount) {
        this.maxRulesCount = maxRulesCount;
    }
    
    public boolean isPrecompileEnabled() {
        return precompileEnabled;
    }
    
    public void setPrecompileEnabled(boolean precompileEnabled) {
        this.precompileEnabled = precompileEnabled;
    }
    
    public int getExpressionCacheSize() {
        return expressionCacheSize;
    }
    
    public void setExpressionCacheSize(int expressionCacheSize) {
        this.expressionCacheSize = expressionCacheSize;
    }
    
    public int getPatternCacheSize() {
        return patternCacheSize;
    }
    
    public void setPatternCacheSize(int patternCacheSize) {
        this.patternCacheSize = patternCacheSize;
    }
}