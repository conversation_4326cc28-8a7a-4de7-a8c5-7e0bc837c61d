package com.lubansoft.msg.model.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 渠道消息发送请求模型
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ChannelMessageRequestDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 请求ID
     */
    private String requestId;

    /**
     * callBackToken
     */
    private String callBackToken;

    /**
     * 模板ID不能为空
     */
    private String templateId;

    /**
     * 模板全局参数
     */
    private Map<String, Object> templateParam;

    /**
     * 全局参数（从模板映射配置的extraParam中获取）
     */
    private Map<String, Object> globalParam;

    /**
     * 接收者列表不能为空
     */
    private List<MessageReceiverDTO> receivers;

}