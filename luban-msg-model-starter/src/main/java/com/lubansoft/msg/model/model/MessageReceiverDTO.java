package com.lubansoft.msg.model.model;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 消息接收者模型
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MessageReceiverDTO {
    
    @Schema(description = "接收人用户id")
    @NotBlank(message = "接收人用户id不能为空")
    private String receiver;
    
    @Schema(description = "接受者联系方式")
    private String contact;
    
}