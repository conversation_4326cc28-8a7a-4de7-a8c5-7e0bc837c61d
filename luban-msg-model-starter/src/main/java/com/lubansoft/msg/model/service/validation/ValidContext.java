package com.lubansoft.msg.model.service.validation;

import lombok.*;

import java.util.Collection;
import java.util.Map;

/**
 * 校验上下文
 */
@Getter
@Builder
public class ValidContext {

    /**
     * appid
     */
    private String appId;

    /**
     * 模板id
     */
    private String templateId;

    /**
     * 参数
     */
    private Collection<Map<String, Object>> params;

    public static interface ValidHandle {

        ValidResult valid(ValidContext validContext);
    }
}
