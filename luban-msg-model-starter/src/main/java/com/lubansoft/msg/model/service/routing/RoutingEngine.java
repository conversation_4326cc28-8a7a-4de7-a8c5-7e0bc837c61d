package com.lubansoft.msg.model.service.routing;

import com.lubansoft.msg.model.model.MessageRequestDTO;
import com.lubansoft.msg.common.model.RoutingResult;
import com.lubansoft.msg.model.repository.entity.RoutingRule;

import java.util.List;

/**
 * 路由引擎接口
 */
public interface RoutingEngine {
    
    /**
     * 路由单个消息请求
     * 
     * @param request 消息请求
     * @param context 路由上下文
     * @return 路由结果
     */
    RoutingResult route(MessageRequestDTO request, RoutingContext context);
    
    /**
     * 批量路由消息请求
     * 
     * @param requests 消息请求列表
     * @param context 路由上下文
     * @return 路由结果列表
     */
    List<RoutingResult> routeBatch(List<MessageRequestDTO> requests, RoutingContext context);
    
    /**
     * 重新加载路由规则
     */
    void reloadRules();
    
    /**
     * 获取启用的路由规则
     * 
     * @return 路由规则列表
     */
    List<RoutingRule> getEnabledRules();
    

}