package com.lubansoft.msg.model.service;

import com.lubansoft.msg.model.model.MessageRequestDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 队列选择器
 * 根据消息属性智能选择合适的队列类型
 */
@Component
public class QueueSelector {
    
    private static final Logger logger = LoggerFactory.getLogger(QueueSelector.class);
    
    /**
     * 根据消息属性选择队列类型
     * 
     * @param messageRequest 消息请求
     * @param properties 消息属性
     * @return 队列类型
     */
    public QueueConfigManager.QueueType selectQueueType(MessageRequestDTO messageRequest,
                                                        Map<String, Object> properties) {
        logger.debug("开始选择队列类型: messageId={}", messageRequest.getMessageId());
        
        try {
            // 1. 检查是否为延时消息
            if (isDelayMessage(properties)) {
                logger.debug("选择延时队列: messageId={}", messageRequest.getMessageId());
                return QueueConfigManager.QueueType.DELAY;
            }
            
            // 2. 检查是否为高优先级消息
            if (isHighPriorityMessage(messageRequest, properties)) {
                logger.debug("选择高优先级队列: messageId={}", messageRequest.getMessageId());
                return QueueConfigManager.QueueType.HIGH_PRIORITY;
            }
            
            // 3. 检查是否为死信消息（重试次数过多）
            if (isDlqMessage(properties)) {
                logger.debug("选择死信队列: messageId={}", messageRequest.getMessageId());
                return QueueConfigManager.QueueType.DLQ;
            }
            
            // 4. 默认使用普通队列
            logger.debug("选择普通队列: messageId={}", messageRequest.getMessageId());
            return QueueConfigManager.QueueType.NORMAL;
            
        } catch (Exception e) {
            logger.error("队列类型选择异常: messageId={}, error={}", 
                        messageRequest.getMessageId(), e.getMessage(), e);
            // 异常情况下使用普通队列
            return QueueConfigManager.QueueType.NORMAL;
        }
    }
    
    /**
     * 根据消息属性和定时时间选择队列类型
     * 
     * @param messageRequest 消息请求
     * @param scheduleTime 定时时间
     * @param properties 消息属性
     * @return 队列类型
     */
    public QueueConfigManager.QueueType selectQueueType(MessageRequestDTO messageRequest, 
                                                       LocalDateTime scheduleTime,
                                                       Map<String, Object> properties) {
        logger.debug("开始选择定时消息队列类型: messageId={}, scheduleTime={}", 
                    messageRequest.getMessageId(), scheduleTime);
        
        try {
            // 定时消息统一使用延时队列
            if (scheduleTime != null && scheduleTime.isAfter(LocalDateTime.now())) {
                logger.debug("选择延时队列（定时消息）: messageId={}", messageRequest.getMessageId());
                return QueueConfigManager.QueueType.DELAY;
            }
            
            // 如果定时时间已过，按普通消息处理
            return selectQueueType(messageRequest, properties);
            
        } catch (Exception e) {
            logger.error("定时消息队列类型选择异常: messageId={}, error={}", 
                        messageRequest.getMessageId(), e.getMessage(), e);
            return QueueConfigManager.QueueType.NORMAL;
        }
    }
    
    /**
     * 检查是否为延时消息
     */
    private boolean isDelayMessage(Map<String, Object> properties) {
        if (properties == null) {
            return false;
        }
        
        // 检查延时时间属性
        Object delayTime = properties.get("delayTime");
        if (delayTime != null) {
            if (delayTime instanceof Number) {
                return ((Number) delayTime).longValue() > 0;
            }
            if (delayTime instanceof String) {
                try {
                    return Long.parseLong((String) delayTime) > 0;
                } catch (NumberFormatException e) {
                    logger.info("无效的延时时间格式: {}", delayTime);
                }
            }
        }
        
        // 检查TTL属性
        Object ttl = properties.get("x-message-ttl");
        if (ttl != null) {
            if (ttl instanceof Number) {
                return ((Number) ttl).longValue() > 0;
            }
        }
        
        // 检查延时标识
        Object isDelay = properties.get("isDelay");
        if (isDelay != null) {
            if (isDelay instanceof Boolean) {
                return (Boolean) isDelay;
            }
            if (isDelay instanceof String) {
                return "true".equalsIgnoreCase((String) isDelay);
            }
        }
        
        return false;
    }
    
    /**
     * 检查是否为高优先级消息
     */
    private boolean isHighPriorityMessage(MessageRequestDTO messageRequest, Map<String, Object> properties) {
        // 1. 检查消息属性中的优先级
        if (properties != null) {
            Object priority = properties.get("priority");
            if (priority != null) {
                if (priority instanceof Number) {
                    return ((Number) priority).intValue() >= 8; // 优先级8及以上为高优先级
                }
                if (priority instanceof String) {
                    try {
                        return Integer.parseInt((String) priority) >= 8;
                    } catch (NumberFormatException e) {
                        logger.info("无效的优先级格式: {}", priority);
                    }
                }
            }
            
            // 检查高优先级标识
            Object isHighPriority = properties.get("isHighPriority");
            if (isHighPriority != null) {
                if (isHighPriority instanceof Boolean) {
                    return (Boolean) isHighPriority;
                }
                if (isHighPriority instanceof String) {
                    return "true".equalsIgnoreCase((String) isHighPriority);
                }
            }
        }
        
        // 2. 检查消息模板类型（某些重要模板默认高优先级）
        if (messageRequest != null && messageRequest.getTemplateId() != null) {
            String templateId = messageRequest.getTemplateId();
            // 紧急通知、安全警告等模板默认高优先级
            if (templateId.contains("urgent") || templateId.contains("security") || 
                templateId.contains("alert") || templateId.contains("emergency")) {
                return true;
            }
        }
        
        // 3. 检查消息模板ID中的关键词
        if (messageRequest != null && messageRequest.getTemplateId() != null) {
            String templateId = messageRequest.getTemplateId().toLowerCase();
            if (templateId.contains("紧急") || templateId.contains("urgent") ||
                templateId.contains("重要") || templateId.contains("important") ||
                templateId.contains("警告") || templateId.contains("warning")) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 检查是否为死信消息
     */
    private boolean isDlqMessage(Map<String, Object> properties) {
        if (properties == null) {
            return false;
        }
        
        // 检查重试次数
        Object retryCount = properties.get("retryCount");
        if (retryCount != null) {
            if (retryCount instanceof Number) {
                return ((Number) retryCount).intValue() >= 3; // 重试3次及以上进入死信队列
            }
            if (retryCount instanceof String) {
                try {
                    return Integer.parseInt((String) retryCount) >= 3;
                } catch (NumberFormatException e) {
                    logger.info("无效的重试次数格式: {}", retryCount);
                }
            }
        }
        
        // 检查死信标识
        Object isDlq = properties.get("isDlq");
        if (isDlq != null) {
            if (isDlq instanceof Boolean) {
                return (Boolean) isDlq;
            }
            if (isDlq instanceof String) {
                return "true".equalsIgnoreCase((String) isDlq);
            }
        }
        
        // 检查是否来自死信交换机
        Object fromDlx = properties.get("x-death");
        return fromDlx != null;
    }
    
}
