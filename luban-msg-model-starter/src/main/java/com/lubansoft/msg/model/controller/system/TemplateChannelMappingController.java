//package com.lubansoft.msg.model.controller.system;
//
//import com.baomidou.mybatisplus.core.metadata.IPage;
//import com.lubansoft.base.common.model.ResponseEntity;
//import com.lubansoft.msg.model.model.dto.TemplateChannelMappingDTO;
//import com.lubansoft.msg.model.model.vo.TemplateChannelMappingVO;
//import com.lubansoft.msg.model.repository.entity.TemplateChannelMapping;
//import com.lubansoft.msg.model.service.TemplateChannelMappingService;
//import io.swagger.v3.oas.annotations.Operation;
//import io.swagger.v3.oas.annotations.Parameter;
//import io.swagger.v3.oas.annotations.tags.Tag;
//import jakarta.validation.Valid;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.beans.BeanUtils;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.web.bind.annotation.*;
//
//import java.util.List;
//import java.util.Map;
//import java.util.Optional;
//import java.util.stream.Collectors;
//
///**
// * 模板渠道映射配置控制器
// */
//@RestController
//@RequestMapping("/template-channel-mappings")
//@Tag(name = "模板渠道映射配置", description = "模板渠道映射配置管理接口")
//public class TemplateChannelMappingController {
//    
//    private static final Logger logger = LoggerFactory.getLogger(TemplateChannelMappingController.class);
//    
//    @Autowired
//    private TemplateChannelMappingService mappingService;
//    
//    @Operation(summary = "创建模板渠道映射配置", description = "创建新的模板渠道映射配置")
//    @PostMapping
//    public ResponseEntity<TemplateChannelMappingVO> createMapping(
//            @Parameter(description = "映射配置信息") @Valid @RequestBody TemplateChannelMappingDTO.CreateRequest request) {
//        
//        logger.info("创建模板渠道映射配置: templateId={}, channelId={}, channelType={}", 
//                   request.getTemplateId(), request.getChannelId(), request.getChannelType());
//        
//        try {
//            // 转换DTO到实体
//            TemplateChannelMapping mapping = convertCreateRequestToEntity(request);
//            
//            // 创建映射配置
//            TemplateChannelMapping created = mappingService.createMapping(mapping);
//            
//            // 转换实体到VO
//            TemplateChannelMappingVO vo = convertEntityToVO(created);
//            
//            logger.info("模板渠道映射配置创建成功: mappingId={}", created.getMappingId());
//            
//            return ResponseEntity.success(vo);
//            
//        } catch (IllegalArgumentException e) {
//            logger.info("创建模板渠道映射配置参数错误: {}", e.getMessage());
//            return ResponseEntity.fail(400, e.getMessage());
//            
//        } catch (Exception e) {
//            logger.error("创建模板渠道映射配置失败", e);
//            return ResponseEntity.fail(500, "创建模板渠道映射配置失败");
//        }
//    }
//    
//    @Operation(summary = "更新模板渠道映射配置", description = "更新现有的模板渠道映射配置")
//    @PutMapping("/{mappingId}")
//    public ResponseEntity<TemplateChannelMappingVO> updateMapping(
//            @Parameter(description = "映射配置ID") @PathVariable String mappingId,
//            @Parameter(description = "映射配置信息") @Valid @RequestBody TemplateChannelMappingDTO.UpdateRequest request) {
//        
//        logger.info("更新模板渠道映射配置: mappingId={}", mappingId);
//        
//        try {
//            // 转换DTO到实体
//            TemplateChannelMapping mapping = convertUpdateRequestToEntity(request);
//            
//            // 更新映射配置
//            TemplateChannelMapping updated = mappingService.updateMapping(mappingId, mapping);
//            
//            // 转换实体到VO
//            TemplateChannelMappingVO vo = convertEntityToVO(updated);
//            
//            logger.info("模板渠道映射配置更新成功: mappingId={}", mappingId);
//            
//            return ResponseEntity.success(vo);
//            
//        } catch (IllegalArgumentException e) {
//            logger.info("更新模板渠道映射配置参数错误: {}", e.getMessage());
//            return ResponseEntity.fail(400, e.getMessage());
//            
//        } catch (Exception e) {
//            logger.error("更新模板渠道映射配置失败: mappingId={}", mappingId, e);
//            return ResponseEntity.fail(500, "更新模板渠道映射配置失败");
//        }
//    }
//    
//    @Operation(summary = "删除模板渠道映射配置", description = "删除指定的模板渠道映射配置")
//    @DeleteMapping("/{mappingId}")
//    public ResponseEntity<Void> deleteMapping(
//            @Parameter(description = "映射配置ID") @PathVariable String mappingId) {
//        
//        logger.info("删除模板渠道映射配置: mappingId={}", mappingId);
//        
//        try {
//            boolean deleted = mappingService.removeById(mappingId);
//            if (!deleted) {
//                logger.info("映射配置不存在: {}", mappingId);
//                return ResponseEntity.fail(404, "映射配置不存在");
//            }
//            
//            logger.info("模板渠道映射配置删除成功: mappingId={}", mappingId);
//            
//            return ResponseEntity.success();
//            
//        } catch (Exception e) {
//            logger.error("删除模板渠道映射配置失败: mappingId={}", mappingId, e);
//            return ResponseEntity.fail(500, "删除模板渠道映射配置失败");
//        }
//    }
//    
//    @Operation(summary = "获取模板渠道映射配置", description = "根据映射配置ID获取详细信息")
//    @GetMapping("/{mappingId}")
//    public ResponseEntity<TemplateChannelMappingVO> getMapping(
//            @Parameter(description = "映射配置ID") @PathVariable String mappingId) {
//        
//        logger.info("获取模板渠道映射配置: mappingId={}", mappingId);
//        
//        try {
//            TemplateChannelMapping mapping = mappingService.getById(mappingId);
//            if (mapping == null) {
//                logger.info("映射配置不存在: {}", mappingId);
//                return ResponseEntity.fail(404, "映射配置不存在");
//            }
//            
//            TemplateChannelMappingVO vo = convertEntityToVO(mapping);
//            
//            return ResponseEntity.success(vo);
//            
//        } catch (Exception e) {
//            logger.error("获取模板渠道映射配置失败: mappingId={}", mappingId, e);
//            return ResponseEntity.fail(500, "获取模板渠道映射配置失败");
//        }
//    }
//    
//    @Operation(summary = "分页查询模板渠道映射配置", description = "根据条件分页查询映射配置")
//    @GetMapping
//    public ResponseEntity<IPage<TemplateChannelMappingVO.Simple>> getMappingsWithPaging(
//            @Parameter(description = "查询条件") @ModelAttribute TemplateChannelMappingDTO.QueryCondition condition) {
//        
//        logger.info("分页查询模板渠道映射配置: pageNum={}, pageSize={}, enabled={}", 
//                   condition.getPageNum(), condition.getPageSize(), condition.getEnabled());
//        
//        try {
//            IPage<TemplateChannelMapping> page = mappingService.getMappingsWithPaging(
//                condition.getPageNum(), condition.getPageSize(), condition.getEnabled());
//            
//            // 转换为VO
//            IPage<TemplateChannelMappingVO.Simple> voPage = page.convert(this::convertEntityToSimpleVO);
//            
//            logger.info("分页查询模板渠道映射配置成功: total={}, size={}", 
//                       voPage.getTotal(), voPage.getRecords().size());
//            
//            return ResponseEntity.success(voPage);
//            
//        } catch (Exception e) {
//            logger.error("分页查询模板渠道映射配置失败", e);
//            return ResponseEntity.fail(500, "分页查询模板渠道映射配置失败");
//        }
//    }
//    
//    @Operation(summary = "应用参数映射", description = "根据模板和渠道配置，将模板参数转换为渠道参数")
//    @PostMapping("/apply-parameter-mapping")
//    public ResponseEntity<TemplateChannelMappingDTO.ParameterMappingResponse> applyParameterMapping(
//            @Parameter(description = "参数映射请求") @Valid @RequestBody TemplateChannelMappingDTO.ParameterMappingRequest request) {
//        
//        logger.info("应用参数映射: templateId={}, channelId={}", request.getTemplateId(), request.getChannelId());
//        
//        try {
//            // 应用参数映射
//            Map<String, Object> channelParams = mappingService.applyParameterMapping(
//                request.getTemplateId(), request.getChannelId(), request.getTemplateParams());
//            
//            // 获取映射配置信息
//            Optional<TemplateChannelMapping> mappingOpt = mappingService.getMappingByTemplateAndChannel(
//                request.getTemplateId(), request.getChannelId());
//            
//            // 构建响应
//            TemplateChannelMappingDTO.ParameterMappingResponse response = 
//                new TemplateChannelMappingDTO.ParameterMappingResponse();
//            response.setChannelParams(channelParams);
//            response.setMappingApplied(mappingOpt.isPresent());
//            
//            if (mappingOpt.isPresent()) {
//                TemplateChannelMapping mapping = mappingOpt.get();
//                response.setMappingId(mapping.getMappingId());
//                response.setChannelTemplateContent(mapping.getEffectiveTemplateContent());
//            }
//            
//            logger.info("参数映射应用成功: templateId={}, channelId={}, mappingApplied={}", 
//                       request.getTemplateId(), request.getChannelId(), response.getMappingApplied());
//            
//            return ResponseEntity.success(response);
//            
//        } catch (Exception e) {
//            logger.error("应用参数映射失败: templateId={}, channelId={}", 
//                        request.getTemplateId(), request.getChannelId(), e);
//            return ResponseEntity.fail(500, "应用参数映射失败");
//        }
//    }
//    
//    /**
//     * 转换创建请求DTO到实体
//     */
//    private TemplateChannelMapping convertCreateRequestToEntity(TemplateChannelMappingDTO.CreateRequest request) {
//        TemplateChannelMapping mapping = new TemplateChannelMapping();
//        BeanUtils.copyProperties(request, mapping);
//        mapping.setParameterMapping(request.getParameterMapping());
//        mapping.setExtendedConfig(request.getExtendedConfig());
//        return mapping;
//    }
//    
//    /**
//     * 转换更新请求DTO到实体
//     */
//    private TemplateChannelMapping convertUpdateRequestToEntity(TemplateChannelMappingDTO.UpdateRequest request) {
//        TemplateChannelMapping mapping = new TemplateChannelMapping();
//        BeanUtils.copyProperties(request, mapping);
//        mapping.setParameterMapping(request.getParameterMapping());
//        mapping.setExtendedConfig(request.getExtendedConfig());
//        return mapping;
//    }
//    
//    /**
//     * 转换实体到VO
//     */
//    private TemplateChannelMappingVO convertEntityToVO(TemplateChannelMapping entity) {
//        TemplateChannelMappingVO vo = new TemplateChannelMappingVO();
//        BeanUtils.copyProperties(entity, vo);
//        vo.setParameterMapping(entity.getParameterMapping());
//        vo.setExtendedConfig(entity.getExtendedConfig());
//        return vo;
//    }
//    
//    /**
//     * 转换实体到简化VO
//     */
//    private TemplateChannelMappingVO.Simple convertEntityToSimpleVO(TemplateChannelMapping entity) {
//        TemplateChannelMappingVO.Simple vo = new TemplateChannelMappingVO.Simple();
//        BeanUtils.copyProperties(entity, vo);
//        return vo;
//    }
//
//    @Operation(summary = "批量操作映射配置", description = "批量启用、禁用或删除映射配置")
//    @PostMapping("/batch-operation")
//    public ResponseEntity<TemplateChannelMappingVO.BatchOperationResult> batchOperation(
//            @Parameter(description = "批量操作请求") @Valid @RequestBody TemplateChannelMappingDTO.BatchOperationRequest request) {
//
//        logger.info("批量操作映射配置: operation={}, count={}", request.getOperation(), request.getMappingIds().size());
//
//        try {
//            TemplateChannelMappingVO.BatchOperationResult result = new TemplateChannelMappingVO.BatchOperationResult();
//            result.setOperation(request.getOperation());
//            result.setTotalCount(request.getMappingIds().size());
//
//            int successCount = 0;
//
//            switch (request.getOperation().toLowerCase()) {
//                case "enable":
//                    successCount = mappingService.batchUpdateEnabled(request.getMappingIds(), true, "system");
//                    break;
//                case "disable":
//                    successCount = mappingService.batchUpdateEnabled(request.getMappingIds(), false, "system");
//                    break;
//                case "delete":
//                    for (String mappingId : request.getMappingIds()) {
//                        if (mappingService.removeById(mappingId)) {
//                            successCount++;
//                        }
//                    }
//                    break;
//                default:
//                    return ResponseEntity.fail(400, "不支持的操作类型: " + request.getOperation());
//            }
//
//            result.setSuccessCount(successCount);
//            result.setFailureCount(result.getTotalCount() - successCount);
//
//            logger.info("批量操作映射配置完成: operation={}, successCount={}, failureCount={}",
//                       request.getOperation(), successCount, result.getFailureCount());
//
//            return ResponseEntity.success(result);
//
//        } catch (Exception e) {
//            logger.error("批量操作映射配置失败: operation={}", request.getOperation(), e);
//            return ResponseEntity.fail(500, "批量操作映射配置失败");
//        }
//    }
//
//    @Operation(summary = "搜索映射配置", description = "根据映射名称搜索映射配置")
//    @GetMapping("/search")
//    public ResponseEntity<List<TemplateChannelMappingVO.Simple>> searchMappings(
//            @Parameter(description = "搜索关键字") @RequestParam String keyword) {
//
//        logger.info("搜索映射配置: keyword={}", keyword);
//
//        try {
//            List<TemplateChannelMapping> mappings = mappingService.searchMappingsByName(keyword);
//
//            List<TemplateChannelMappingVO.Simple> vos = mappings.stream()
//                .map(this::convertEntityToSimpleVO)
//                .collect(Collectors.toList());
//
//            logger.info("搜索映射配置成功: keyword={}, count={}", keyword, vos.size());
//
//            return ResponseEntity.success(vos);
//
//        } catch (Exception e) {
//            logger.error("搜索映射配置失败: keyword={}", keyword, e);
//            return ResponseEntity.fail(500, "搜索映射配置失败");
//        }
//    }
//
//    @Operation(summary = "复制映射配置", description = "将源模板的映射配置复制到目标模板")
//    @PostMapping("/copy/{sourceTemplateId}/to/{targetTemplateId}")
//    public ResponseEntity<Integer> copyMappings(
//            @Parameter(description = "源模板ID") @PathVariable String sourceTemplateId,
//            @Parameter(description = "目标模板ID") @PathVariable String targetTemplateId) {
//
//        logger.info("复制映射配置: sourceTemplateId={}, targetTemplateId={}", sourceTemplateId, targetTemplateId);
//
//        try {
//            int copiedCount = mappingService.copyMappingsToTemplate(sourceTemplateId, targetTemplateId, "system");
//
//            logger.info("复制映射配置成功: sourceTemplateId={}, targetTemplateId={}, copiedCount={}",
//                       sourceTemplateId, targetTemplateId, copiedCount);
//
//            return ResponseEntity.success(copiedCount);
//
//        } catch (Exception e) {
//            logger.error("复制映射配置失败: sourceTemplateId={}, targetTemplateId={}",
//                        sourceTemplateId, targetTemplateId, e);
//            return ResponseEntity.fail(500, "复制映射配置失败");
//        }
//    }
//
//    @Operation(summary = "获取映射统计信息", description = "获取系统映射配置的统计信息")
//    @GetMapping("/statistics")
//    public ResponseEntity<Map<String, Object>> getMappingStatistics() {
//
//        logger.info("获取映射统计信息");
//
//        try {
//            int totalMappings = (int) mappingService.count();
//            int enabledMappings = mappingService.countEnabledMappings();
//
//            Map<String, Object> statistics = Map.of(
//                "totalMappings", totalMappings,
//                "enabledMappings", enabledMappings,
//                "disabledMappings", totalMappings - enabledMappings
//            );
//
//            logger.info("获取映射统计信息成功: {}", statistics);
//
//            return ResponseEntity.success(statistics);
//
//        } catch (Exception e) {
//            logger.error("获取映射统计信息失败", e);
//            return ResponseEntity.fail(500, "获取映射统计信息失败");
//        }
//    }
//}
