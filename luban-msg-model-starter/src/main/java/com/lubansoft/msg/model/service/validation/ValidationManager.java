package com.lubansoft.msg.model.service.validation;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class ValidationManager {

    @Autowired
    private List<ValidContext.ValidHandle> validHandleList;

    public ValidResult valid(ValidContext validContext) {
        for(ValidContext.ValidHandle validHandle: validHandleList) {
            ValidResult result = validHandle.valid(validContext);
            if(!result.getValid()) {
                return result;
            }
        }
        return ValidResult.success();
    }
}
