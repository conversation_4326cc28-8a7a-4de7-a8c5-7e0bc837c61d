package com.lubansoft.msg.model.controller.system;

import com.lubansoft.msg.model.model.ReceiverSettingDTO;
import com.lubansoft.msg.model.repository.entity.ReceiverDnd;
import com.lubansoft.msg.model.repository.entity.ReceiverSub;
import com.lubansoft.msg.model.service.ReceiverDndService;
import com.lubansoft.msg.model.service.ReceiverSubService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/receiver")
@Tag(name = "用户消息设置", description = "用户收信渠道联系方式设置")
public class ReceiverController {
    
    @Autowired
    private ReceiverSubService receiverSubService;
    
    @Autowired
    private ReceiverDndService receiverDndService;
    
    /**
     * 查询用户接收设置详情
     * 
     * @param receiverId 用户ID
     * @return 用户接收设置信息
     */
    @GetMapping("/{receiverId}")
    @Operation(summary = "查询用户接收设置详情", description = "根据用户ID查询该用户的渠道订阅设置和免打扰设置")
    public ReceiverSettingDTO getReceiverSettings(
            @Parameter(description = "用户ID") @PathVariable String receiverId) {
        ReceiverSettingDTO settingDTO = new ReceiverSettingDTO();
        settingDTO.setReceiverId(receiverId);

        // 查询渠道订阅设置
        List<ReceiverSub> subscriptions = receiverSubService.findByReceiverId(receiverId);
        List<ReceiverSettingDTO.ChannelSubscription> channelSubscriptions = subscriptions.stream()
                .map(ReceiverSettingDTO.ChannelSubscription::new)
                .collect(Collectors.toList());
        settingDTO.setSubscriptions(channelSubscriptions);

        // 查询免打扰设置
        List<ReceiverDnd> dndSettings = receiverDndService.findByReceiverId(receiverId);
        List<ReceiverSettingDTO.DoNotDisturbSetting> doNotDisturbSettings = dndSettings.stream()
                .map(ReceiverSettingDTO.DoNotDisturbSetting::new)
                .collect(Collectors.toList());
        settingDTO.setDoNotDisturbSettings(doNotDisturbSettings);
        return settingDTO;
    }
    
    /**
     * 更新用户渠道订阅设置
     * 
     * @param receiverId 用户ID
     * @param subscription 渠道订阅信息
     * @return 操作结果
     */
    @PostMapping("/{receiverId}/subscription")
    @Operation(summary = "更新用户渠道订阅设置", description = "更新指定用户的某个渠道订阅设置")
    public ReceiverSettingDTO.ChannelSubscription updateSubscription(
            @Parameter(description = "用户ID") @PathVariable String receiverId,
            @Parameter(description = "渠道订阅信息") @RequestBody ReceiverSettingDTO.ChannelSubscription subscription) {
        // 查找是否已存在该用户的渠道订阅设置
        ReceiverSub receiverSub = receiverSubService.findByReceiverIdAndChannel(receiverId, subscription.getChannel());

        if (receiverSub == null) {
            // 不存在则创建新的订阅设置
            receiverSub = new ReceiverSub();
            receiverSub.setReceiverId(receiverId);
            receiverSub.setChannel(subscription.getChannel());
            receiverSub.setIsSub(subscription.getIsSub());
            receiverSub.setContact(subscription.getContact());
            receiverSub.setCreatedAt(LocalDateTime.now());
            receiverSub.setUpdatedAt(LocalDateTime.now());
            receiverSubService.save(receiverSub);
        } else {
            // 存在则更新订阅设置
            receiverSub.setIsSub(subscription.getIsSub());
            receiverSub.setContact(subscription.getContact());
            receiverSub.setUpdatedAt(LocalDateTime.now());
            receiverSubService.update(receiverSub);
        }

        return new ReceiverSettingDTO.ChannelSubscription(receiverSub);
    }
    
//    /**
//     * 批量更新用户渠道订阅设置
//     *
//     * @param receiverId 用户ID
//     * @param subscriptions 渠道订阅信息列表
//     * @return 操作结果
//     */
//    @PostMapping("/{receiverId}/subscriptions")
//    @Operation(summary = "批量更新用户渠道订阅设置", description = "批量更新指定用户的渠道订阅设置")
//    public ResponseEntity<List<ReceiverSettingDTO.ChannelSubscription>> updateSubscriptions(
//            @Parameter(description = "用户ID") @PathVariable String receiverId,
//            @Parameter(description = "渠道订阅信息列表") @RequestBody List<ReceiverSettingDTO.ChannelSubscription> subscriptions) {
//        List<ReceiverSettingDTO.ChannelSubscription> results = new ArrayList<>();
//
//        for (ReceiverSettingDTO.ChannelSubscription subscription : subscriptions) {
//            // 查找是否已存在该用户的渠道订阅设置
//            ReceiverSub receiverSub = receiverSubService.findByReceiverIdAndChannel(receiverId, subscription.getChannel());
//
//            if (receiverSub == null) {
//                // 不存在则创建新的订阅设置
//                receiverSub = new ReceiverSub();
//                receiverSub.setReceiverId(receiverId);
//                receiverSub.setChannel(subscription.getChannel());
//                receiverSub.setIsSub(subscription.getIsSub());
//                receiverSub.setContact(subscription.getContact());
//                receiverSub.setCreatedAt(LocalDateTime.now());
//                receiverSub.setUpdatedAt(LocalDateTime.now());
//                receiverSubService.save(receiverSub);
//            } else {
//                // 存在则更新订阅设置
//                receiverSub.setIsSub(subscription.getIsSub());
//                receiverSub.setContact(subscription.getContact());
//                receiverSub.setUpdatedAt(LocalDateTime.now());
//                receiverSubService.update(receiverSub);
//            }
//
//            results.add(new ReceiverSettingDTO.ChannelSubscription(receiverSub));
//        }
//        return ResponseEntity.success(results);
//    }
//
//    /**
//     * 更新用户免打扰设置
//     *
//     * @param receiverId 用户ID
//     * @param dndSetting 免打扰设置信息
//     * @return 操作结果
//     */
//    @PostMapping("/{receiverId}/dnd")
//    @Operation(summary = "更新用户免打扰设置", description = "更新指定用户的免打扰设置")
//    public ResponseEntity<ReceiverSettingDTO.DoNotDisturbSetting> updateDoNotDisturbSetting(
//            @Parameter(description = "用户ID") @PathVariable String receiverId,
//            @Parameter(description = "免打扰设置信息") @RequestBody ReceiverSettingDTO.DoNotDisturbSetting dndSetting) {
//        ReceiverDnd receiverDnd;
//
//        if (dndSetting.getId() != null) {
//            // 如果ID存在，则更新现有设置
//            receiverDnd = receiverDndService.findById(dndSetting.getId());
//            if (receiverDnd == null) {
//                return ResponseEntity.fail(404, "未找到指定的免打扰设置");
//            }
//            if (!receiverId.equals(receiverDnd.getReceiverId())) {
//                return ResponseEntity.fail(403, "无权修改其他用户的设置");
//            }
//        } else {
//            // 如果ID不存在，则创建新的设置
//            receiverDnd = new ReceiverDnd();
//            receiverDnd.setReceiverId(receiverId);
//            receiverDnd.setPolicy(dndSetting.getPolicy());
//            receiverDnd.setCreatedAt(LocalDateTime.now());
//        }
//
//        // 更新设置信息
//        receiverDnd.setPolicy(dndSetting.getPolicy());
//        receiverDnd.setStartTime(dndSetting.getStartTime());
//        receiverDnd.setEndTime(dndSetting.getEndTime());
//        receiverDnd.setDays(dndSetting.getDays());
//        receiverDnd.setUpdatedAt(LocalDateTime.now());
//
//        if (dndSetting.getId() != null) {
//            receiverDndService.update(receiverDnd);
//        } else {
//            receiverDndService.save(receiverDnd);
//        }
//
//        ReceiverSettingDTO.DoNotDisturbSetting result = new ReceiverSettingDTO.DoNotDisturbSetting(receiverDnd);
//        return ResponseEntity.success(result);
//    }
//
//    /**
//     * 删除用户免打扰设置
//     *
//     * @param receiverId 用户ID
//     * @param dndId 免打扰设置ID
//     * @return 操作结果
//     */
//    @DeleteMapping("/{receiverId}/dnd/{dndId}")
//    @Operation(summary = "删除用户免打扰设置", description = "删除指定用户的免打扰设置")
//    public ResponseEntity<Boolean> deleteDoNotDisturbSetting(
//            @Parameter(description = "用户ID") @PathVariable String receiverId,
//            @Parameter(description = "免打扰设置ID") @PathVariable Long dndId) {
//        // 检查设置是否存在且属于该用户
//        ReceiverDnd receiverDnd = receiverDndService.findById(dndId);
//        if (receiverDnd == null) {
//            return ResponseEntity.fail(404, "未找到指定的免打扰设置");
//        }
//        if (!receiverId.equals(receiverDnd.getReceiverId())) {
//            return ResponseEntity.fail(403, "无权删除其他用户的设置");
//        }
//
//        // 执行删除操作
//        boolean result = receiverDndService.deleteById(dndId);
//        if (result) {
//            return ResponseEntity.success(true);
//        } else {
//            return ResponseEntity.fail(500, "删除用户免打扰设置失败");
//        }
//    }
//
//    /**
//     * 分页查询用户免打扰设置
//     *
//     * @param receiverId 用户ID
//     * @return 免打扰设置分页结果
//     */
//    @GetMapping("/{receiverId}/dnd")
//    @Operation(summary = "分页查询用户免打扰设置", description = "分页查询指定用户的免打扰设置")
//    public ResponseEntity<ReceiverSettingDTO.DoNotDisturbSetting> getDoNotDisturbSettings(
//            @Parameter(description = "用户ID") @PathVariable String receiverId) {
//        IPage<ReceiverDnd> pageResult = receiverDndService.findByReceiverId(receiverId);
//        ReceiverSettingDTO.DoNotDisturbSetting result = pageResult.convert(ReceiverSettingDTO.DoNotDisturbSetting::new);
//        return ResponseEntity.success(result);
//    }
}