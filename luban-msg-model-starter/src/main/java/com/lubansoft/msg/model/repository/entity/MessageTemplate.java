package com.lubansoft.msg.model.repository.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.databind.JsonNode;
import com.lubansoft.msg.common.util.JsonUtils;
import com.lubansoft.msg.model.repository.typehandle.JsonTypeHandle;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

/**
 * 消息模板实体
 */
@TableName("message_template")
public class MessageTemplate {
    
    private static final Logger logger = LoggerFactory.getLogger(MessageTemplate.class);
    
    /**
     * 模板ID
     */
    @TableId(value = "template_id", type = IdType.ASSIGN_ID)
    private String templateId;
    
    /**
     * 模板名称
     */
    @NotBlank(message = "模板名称不能为空")
    @Size(max = 255, message = "模板名称长度不能超过255个字符")
    @TableField("template_name")
    private String templateName;
    
    /**
     * 模板内容
     */
    @NotBlank(message = "模板内容不能为空")
    @TableField("template_content")
    private String templateContent;
    
    /**
     * 模板类型
     */
    @NotBlank(message = "模板类型不能为空")
    @TableField("template_type")
    private String templateType;
    
    /**
     * 模板描述
     */
    @Size(max = 500, message = "模板描述长度不能超过500个字符")
    @TableField("description")
    private String description;
    
    /**
     * 模板变量列表（JSON SCHEMA格式存储）
     */
    @TableField(value = "template_variables", typeHandler = JsonTypeHandle.class)
    private String templateVariablesJson;
    
    /**
     * 是否启用
     */
    @TableField("enabled")
    private boolean enabled = true;
    
    /**
     * 创建时间
     */
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;
    
    // Constructors
    public MessageTemplate() {}
    
    public MessageTemplate(String templateId, String templateName, String templateContent, String templateType) {
        this.templateId = templateId;
        this.templateName = templateName;
        this.templateContent = templateContent;
        this.templateType = templateType;
    }
    
    // 模板变量的便捷方法
    public List<String> getTemplateVariables() {
        if (templateVariablesJson == null || templateVariablesJson.trim().isEmpty()) {
            return List.of();
        }
        
        try {
            // 首先尝试解析为JSON Schema格式（对象格式）
            JsonNode schemaNode = JsonUtils.fromJson(templateVariablesJson, JsonNode.class);
            if (schemaNode != null && schemaNode.has("properties")) {
                // 如果是JSON Schema格式，提取properties中的属性名
                JsonNode propertiesNode = schemaNode.get("properties");
                if (propertiesNode != null && propertiesNode.isObject()) {
                    List<String> variables = new ArrayList<>();
                    Iterator<String> fieldNames = propertiesNode.fieldNames();
                    while (fieldNames.hasNext()) {
                        variables.add(fieldNames.next());
                    }
                    return variables;
                }
            }
            
            // 如果不是JSON Schema格式，尝试解析为字符串列表
            return JsonUtils.fromJsonToList(templateVariablesJson, String.class);
        } catch (Exception e) {
            logger.warn("解析模板变量失败，使用空列表: templateId={}, error={}", this.templateId, e.getMessage());
            return List.of();
        }
    }
    
    public void setTemplateVariables(List<String> templateVariables) {
        // 构建JSON Schema格式的模板变量
        if (templateVariables != null && !templateVariables.isEmpty()) {
            Map<String, Object> schema = new HashMap<>();
            schema.put("type", "object");
            
            // 添加required字段
            schema.put("required", templateVariables);
            
            // 构建properties字段
            Map<String, Map<String, String>> properties = new HashMap<>();
            for (String variable : templateVariables) {
                Map<String, String> property = new HashMap<>();
                property.put("type", "string");
                property.put("title", variable);
                properties.put(variable, property);
            }
            schema.put("properties", properties);
            
            this.templateVariablesJson = JsonUtils.toJson(schema);
        } else {
            this.templateVariablesJson = null;
        }
    }
    
    // Getters and Setters
    public String getTemplateId() {
        return templateId;
    }
    
    public void setTemplateId(String templateId) {
        this.templateId = templateId;
    }
    
    public String getTemplateName() {
        return templateName;
    }
    
    public void setTemplateName(String templateName) {
        this.templateName = templateName;
    }
    
    public String getTemplateContent() {
        return templateContent;
    }
    
    public void setTemplateContent(String templateContent) {
        this.templateContent = templateContent;
    }
    
    public String getTemplateType() {
        return templateType;
    }
    
    public void setTemplateType(String templateType) {
        this.templateType = templateType;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public String getTemplateVariablesJson() {
        return templateVariablesJson;
    }
    
    public void setTemplateVariablesJson(String templateVariablesJson) {
        this.templateVariablesJson = templateVariablesJson;
    }
    
    public boolean isEnabled() {
        return enabled;
    }
    
    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }
    
    /**
     * 获取模板内容（别名方法）
     */
    public String getContent() {
        return templateContent;
    }
    
    /**
     * 获取渠道类型（从模板类型推导）
     */
    public String getChannelType() {
        return templateType;
    }
    
    /**
     * 获取启用状态（别名方法）
     */
    public Boolean getEnabled() {
        return enabled;
    }
    
    /**
     * 设置内容（别名方法）
     */
    public void setContent(String content) {
        this.templateContent = content;
    }
    
    /**
     * 获取变量列表（别名方法）
     */
    public List<String> getVariables() {
        return getTemplateVariables();
    }
    
    /**
     * 设置变量列表（别名方法）
     */
    public void setVariables(List<String> variables) {
        setTemplateVariables(variables);
    }
    
    /**
     * 获取版本号（模拟字段）
     */
    public int getVersion() {
        return 1; // 简化实现，实际项目中可能需要真实的版本字段
    }
    
    /**
     * 设置版本号（模拟字段）
     */
    public void setVersion(int version) {
        // 简化实现，实际项目中可能需要真实的版本字段
    }
    
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
    
    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }
    
    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }
    
    @Override
    public String toString() {
        return "MessageTemplate{" +
                "templateId='" + templateId + '\'' +
                ", templateName='" + templateName + '\'' +
                ", templateContent='" + templateContent + '\'' +
                ", templateType='" + templateType + '\'' +
                ", description='" + description + '\'' +
                ", templateVariablesJson='" + templateVariablesJson + '\'' +
                ", enabled=" + enabled +
                ", createdAt=" + createdAt +
                ", updatedAt=" + updatedAt +
                '}';
    }
}