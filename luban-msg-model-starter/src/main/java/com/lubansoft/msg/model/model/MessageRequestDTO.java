package com.lubansoft.msg.model.model;

import com.lubansoft.msg.common.model.SendConfig;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;
import java.util.Map;

/**
 * 消息发送请求模型
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class MessageRequestDTO {

    /**
     * APPID
     */
    private String appId;

    /**
     * 请求ID
     */
    private String requestId;

    /**
     * 消息ID
     */
    private String messageId;

    /**
     * 模板ID不能为空
     */
    private String templateId;

    /**
     * 全局模板参数，当接收者级别参数存在相同键时，将被接收者参数替换
     */
    private Map<String, Object> globalParam;

    /**
     * 接收者列表不能为空
     */
    private List<String> receivers;

    /**
     * 优先级 1-10，数字越大优先级越高
     */
    private Integer priority = 5;

    /**
     * 强制发送
     */
    private Boolean forceSend = false;

    /**
     * 额外参数
     */
    private Map<String, Object> extraParam;

    public MessageRequestDTO(String appId, String requestId, String messageId, String templateId, Map<String, Object> globalParam, List<String> receivers, Integer priority, SendConfig sendConfig) {
        this.appId = appId;
        this.requestId = requestId;
        this.messageId = messageId;
        this.templateId = templateId;
        this.globalParam = globalParam;
        this.receivers = receivers;
        this.priority = priority;
        if(sendConfig != null && sendConfig.getForceSend() != null) {
            this.forceSend = sendConfig.getForceSend();
        }else {
            this.forceSend = false;
        }
    }
}