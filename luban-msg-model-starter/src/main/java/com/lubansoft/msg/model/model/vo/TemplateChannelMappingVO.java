package com.lubansoft.msg.model.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 模板渠道映射配置VO
 */
@Data
@Schema(description = "模板渠道映射配置视图对象")
public class TemplateChannelMappingVO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    @Schema(description = "映射配置ID")
    private String mappingId;
    
    @Schema(description = "模板ID")
    private String templateId;
    
    @Schema(description = "模板名称")
    private String templateName;
    
    @Schema(description = "渠道ID")
    private String channelId;
    
    @Schema(description = "渠道名称")
    private String channelName;
    
    @Schema(description = "渠道类型")
    private String channelType;
    
    @Schema(description = "映射名称")
    private String mappingName;
    
    @Schema(description = "参数映射配置")
    private Map<String, String> parameterMapping;
    
    @Schema(description = "渠道特定的模板内容")
    private String channelTemplateContent;
    
    @Schema(description = "渠道特定的模板变量")
    private String channelTemplateVariables;
    
    @Schema(description = "是否启用")
    private Boolean enabled;
    
    @Schema(description = "优先级")
    private Integer priority;
    
    @Schema(description = "映射描述")
    private String description;
    
    @Schema(description = "扩展配置")
    private Map<String, Object> extendedConfig;
    
    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;
    
    @Schema(description = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;
    
    @Schema(description = "创建者")
    private String createdBy;
    
    @Schema(description = "更新者")
    private String updatedBy;
    
    /**
     * 简化的映射配置VO（用于列表显示）
     */
    @Data
    @Schema(description = "简化的模板渠道映射配置")
    public static class Simple implements Serializable {
        
        private static final long serialVersionUID = 1L;
        
        @Schema(description = "映射配置ID")
        private String mappingId;
        
        @Schema(description = "模板ID")
        private String templateId;
        
        @Schema(description = "模板名称")
        private String templateName;
        
        @Schema(description = "渠道ID")
        private String channelId;
        
        @Schema(description = "渠道名称")
        private String channelName;
        
        @Schema(description = "渠道类型")
        private String channelType;
        
        @Schema(description = "映射名称")
        private String mappingName;
        
        @Schema(description = "是否启用")
        private Boolean enabled;
        
        @Schema(description = "优先级")
        private Integer priority;
        
        @Schema(description = "创建时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime createdAt;
    }
    
    /**
     * 模板的渠道映射统计VO
     */
    @Data
    @Schema(description = "模板的渠道映射统计")
    public static class TemplateChannelStats implements Serializable {
        
        private static final long serialVersionUID = 1L;
        
        @Schema(description = "模板ID")
        private String templateId;
        
        @Schema(description = "模板名称")
        private String templateName;
        
        @Schema(description = "模板类型")
        private String templateType;
        
        @Schema(description = "总映射数量")
        private Integer totalMappings;
        
        @Schema(description = "启用的映射数量")
        private Integer enabledMappings;
        
        @Schema(description = "支持的渠道类型列表")
        private List<String> supportedChannelTypes;
        
        @Schema(description = "映射配置列表")
        private List<TemplateChannelMappingVO.Simple> mappings;
    }
    
    /**
     * 渠道的模板映射统计VO
     */
    @Data
    @Schema(description = "渠道的模板映射统计")
    public static class ChannelTemplateStats implements Serializable {
        
        private static final long serialVersionUID = 1L;
        
        @Schema(description = "渠道ID")
        private String channelId;
        
        @Schema(description = "渠道名称")
        private String channelName;
        
        @Schema(description = "渠道类型")
        private String channelType;
        
        @Schema(description = "总映射数量")
        private Integer totalMappings;
        
        @Schema(description = "启用的映射数量")
        private Integer enabledMappings;
        
        @Schema(description = "支持的模板类型列表")
        private List<String> supportedTemplateTypes;
        
        @Schema(description = "映射配置列表")
        private List<TemplateChannelMappingVO.Simple> mappings;
    }
    
    /**
     * 映射配置详情VO（包含关联信息）
     */
    @Data
    @Schema(description = "映射配置详情")
    public static class Detail implements Serializable {
        
        private static final long serialVersionUID = 1L;
        
        @Schema(description = "映射配置基本信息")
        private TemplateChannelMappingVO mapping;
        
        @Schema(description = "关联的模板信息")
        private TemplateInfo template;
        
        @Schema(description = "关联的渠道信息")
        private ChannelInfo channel;
        
        @Data
        @Schema(description = "模板信息")
        public static class TemplateInfo implements Serializable {
            private static final long serialVersionUID = 1L;
            
            @Schema(description = "模板ID")
            private String templateId;
            
            @Schema(description = "模板名称")
            private String templateName;
            
            @Schema(description = "模板类型")
            private String templateType;
            
            @Schema(description = "模板内容")
            private String templateContent;
            
            @Schema(description = "模板变量")
            private List<String> templateVariables;
            
            @Schema(description = "是否启用")
            private Boolean enabled;
        }
        
        @Data
        @Schema(description = "渠道信息")
        public static class ChannelInfo implements Serializable {
            private static final long serialVersionUID = 1L;
            
            @Schema(description = "渠道ID")
            private String channelId;
            
            @Schema(description = "渠道名称")
            private String channelName;
            
            @Schema(description = "渠道类型")
            private String channelType;
            
            @Schema(description = "是否启用")
            private Boolean enabled;
        }
    }
    
    /**
     * 参数映射预览VO
     */
    @Data
    @Schema(description = "参数映射预览")
    public static class ParameterMappingPreview implements Serializable {
        
        private static final long serialVersionUID = 1L;
        
        @Schema(description = "映射配置ID")
        private String mappingId;
        
        @Schema(description = "模板参数")
        private Map<String, Object> templateParams;
        
        @Schema(description = "映射规则")
        private Map<String, String> mappingRules;
        
        @Schema(description = "转换后的渠道参数")
        private Map<String, Object> channelParams;
        
        @Schema(description = "未映射的参数")
        private List<String> unmappedParams;
        
        @Schema(description = "映射冲突的参数")
        private List<String> conflictParams;
    }
    
    /**
     * 批量操作结果VO
     */
    @Data
    @Schema(description = "批量操作结果")
    public static class BatchOperationResult implements Serializable {
        
        private static final long serialVersionUID = 1L;
        
        @Schema(description = "操作类型")
        private String operation;
        
        @Schema(description = "总数量")
        private Integer totalCount;
        
        @Schema(description = "成功数量")
        private Integer successCount;
        
        @Schema(description = "失败数量")
        private Integer failureCount;
        
        @Schema(description = "成功的映射ID列表")
        private List<String> successMappingIds;
        
        @Schema(description = "失败的映射ID列表")
        private List<String> failureMappingIds;
        
        @Schema(description = "错误信息列表")
        private List<String> errorMessages;
    }
}
