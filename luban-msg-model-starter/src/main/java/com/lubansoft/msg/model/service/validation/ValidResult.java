package com.lubansoft.msg.model.service.validation;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 校验结果
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ValidResult {

    /**
     * 是否通过校验
     */
    private Boolean valid;

    /**
     * 错误信息
     */
    private String errorMessage;

    public static ValidResult success() {
        return new ValidResult(true, null);
    }

    public static ValidResult fail(String errorMessage) {
        return new ValidResult(false, errorMessage);
    }
}
