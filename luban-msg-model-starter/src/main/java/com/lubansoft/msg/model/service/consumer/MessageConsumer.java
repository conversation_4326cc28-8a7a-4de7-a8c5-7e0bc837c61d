package com.lubansoft.msg.model.service.consumer;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lubansoft.msg.common.enums.MessageStatus;
import com.lubansoft.msg.model.model.MessageReceiverDTO;
import com.lubansoft.msg.model.model.MessageRequestDTO;
import com.lubansoft.msg.model.repository.repo.MessageRecordRepo;
import com.lubansoft.msg.model.service.ChannelSelector;
import com.lubansoft.msg.model.service.QueueConfigManager;
import com.rabbitmq.client.Channel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.*;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 消息消费者
 * 处理来自预定义队列的消息，使用渠道选择器确定发送渠道
 */
@Component
public class MessageConsumer {
    
    private static final Logger logger = LoggerFactory.getLogger(MessageConsumer.class);
    
    @Autowired
    private ChannelSelector channelSelector;
    
    @Autowired
    private MessageChannelHandler messageChannelHandler;

    @Autowired
    private ObjectMapper objectMapper;
    
    @Autowired
    private MessageRecordRepo messageRecordRepo;
    
    /**
     * 处理高优先级队列消息
     */
    @RabbitListener(bindings = @QueueBinding(
            value = @Queue(value = QueueConfigManager.HIGH_PRIORITY_QUEUE, durable = "true"),
            exchange = @Exchange(name = QueueConfigManager.MESSAGE_EXCHANGE, type = "topic")
    ))
    public void handleHighPriorityMessage(@Payload String messageStr, Message message, Channel channel, @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag) throws JsonProcessingException {
        logger.info("收到高优先级消息: messageId={}", messageStr);
        MessageRequestDTO messageRequest = objectMapper.readValue(messageStr, MessageRequestDTO.class);
        try {
            processMessage(messageRequest, message, QueueConfigManager.QueueType.HIGH_PRIORITY);
            channel.basicAck(deliveryTag, false);
        } catch (Exception e) {
            logger.error("高优先级消息处理异常: messageId={}", messageStr, e);
            handleMessageError(channel, deliveryTag, messageRequest, e);
        }
    }

    /**
     * 处理普通队列消息
     */
    @RabbitListener(bindings = @QueueBinding(
            value = @Queue(value = QueueConfigManager.NORMAL_QUEUE, durable = "true"),
            exchange = @Exchange(name = QueueConfigManager.MESSAGE_EXCHANGE, type = "topic")
    ))
    public void handleNormalMessage(@Payload String messageStr, Message message, Channel channel, @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag) throws JsonProcessingException {
        logger.info("收到普通消息: messageId={}", messageStr);
        MessageRequestDTO messageRequest = objectMapper.readValue(messageStr, MessageRequestDTO.class);
        try {
            processMessage(messageRequest, message, QueueConfigManager.QueueType.NORMAL);
            channel.basicAck(deliveryTag, false);
        } catch (Exception e) {
            logger.error("普通消息处理异常: messageId={}", messageStr, e);
            handleMessageError(channel, deliveryTag, messageRequest, e);
        }
    }
    
    /**
     * 处理延时队列消息
     */
    @RabbitListener(queues = QueueConfigManager.DELAY_QUEUE)
    public void handleDelayMessage(@Payload MessageRequestDTO messageRequest,
                                  Message message,
                                  Channel channel,
                                  @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag) {
        logger.info("收到延时消息: messageId={}", messageRequest);
        try {
//            processMessage(messageRequest, message, QueueConfigManager.QueueType.DELAY);

            // 手动确认消息
            channel.basicAck(deliveryTag, false);
            logger.debug("延时消息处理完成: messageId={}", messageRequest);

        } catch (Exception e) {
            logger.error("延时消息处理异常: messageId={}", messageRequest, e);
//            handleMessageError(channel, deliveryTag, messageRequest, e);
        }
    }

    /**
     * 处理死信队列消息
     */
    @RabbitListener(queues = QueueConfigManager.DLQ_QUEUE)
    public void handleDlqMessage(@Payload MessageRequestDTO messageRequest,
                                Message message,
                                Channel channel,
                                @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag) {
        logger.warn("收到死信消息: messageId={}", messageRequest);

        try {
            // 死信消息需要特殊处理，可能需要人工干预或记录
//            processDlqMessage(messageRequest, message);

            // 手动确认消息
            channel.basicAck(deliveryTag, false);
            logger.debug("死信消息处理完成: messageId={}", messageRequest);

        } catch (Exception e) {
            logger.error("死信消息处理异常: messageId={}", messageRequest, e);
            // 死信消息处理失败，直接拒绝不重新入队
            try {
                channel.basicReject(deliveryTag, false);
            } catch (Exception rejectException) {
                logger.error("拒绝死信消息失败: messageId={}", messageRequest, rejectException);
            }
        }
    }
    
    /**
     * 处理消息的核心逻辑
     */
    private void processMessage(MessageRequestDTO messageRequest, Message message, 
                               QueueConfigManager.QueueType queueType) {
        try {
            // 1. 提取消息属性
            Map<String, Object> messageProperties = extractMessageProperties(message);

            // 2. 检查是否包含按渠道分组的接收者信息
            Map<String, List<MessageReceiverDTO>> channelReceivers = null;
            if (messageProperties != null && messageProperties.containsKey("channelReceivers")) {
                channelReceivers = (Map<String, List<MessageReceiverDTO>>) messageProperties.get("channelReceivers");
                logger.debug("使用按渠道分组的接收者: channels={}", channelReceivers.keySet());
            }
            
            // 3. 使用渠道选择器选择发送渠道
            List<String> targetChannels;
            if (channelReceivers != null) {
                // 如果有按渠道分组的接收者，直接使用这些渠道
                targetChannels = new ArrayList<>(channelReceivers.keySet());
            } else {
                // 否则使用渠道选择器选择发送渠道
                targetChannels = channelSelector.selectChannels(messageRequest, messageProperties);
            }
            
            if (targetChannels == null || targetChannels.isEmpty()) {
                logger.warn("未选择到任何渠道: messageId={}", messageRequest.getMessageId());
                return;
            }

            // 4. 验证渠道可用性
            List<String> validChannels = channelSelector.validateChannels(targetChannels);

            if (validChannels.isEmpty()) {
                logger.warn("所有渠道都不可用: messageId={}, originalChannels={}",
                           messageRequest.getMessageId(), targetChannels);
                return;
            }
            
            logger.info("选择的发送渠道: messageId={}, channels={}", 
                       messageRequest.getMessageId(), validChannels);
            
            // 5. 提取渠道特定参数结果（如果存在）
            Map<String, com.lubansoft.msg.model.service.consumer.ChannelParameterResult> channelParameterResults = null;
            if (messageProperties != null && messageProperties.containsKey("channelParameterResults")) {
                channelParameterResults = (Map<String, com.lubansoft.msg.model.service.consumer.ChannelParameterResult>)
                    messageProperties.get("channelParameterResults");
                logger.debug("发现预设的渠道参数结果: channels={}", channelParameterResults.keySet());
            }

            // 6. 通过渠道处理器发送消息
            for (String channel : validChannels) {
                if (channelReceivers != null && channelReceivers.containsKey(channel)) {
                    List<MessageReceiverDTO> receivers = channelReceivers.get(channel);
                    logger.debug("使用渠道 {} 的特定接收者列表，数量: {}", channel, receivers.size());

                    // 获取该渠道的参数结果
                    ChannelParameterResult parameterResult = null;
                    if (channelParameterResults != null && channelParameterResults.containsKey(channel)) {
                        parameterResult = channelParameterResults.get(channel);
                        logger.debug("使用渠道 {} 的预设参数结果", channel);
                    }

                    // 发送消息，传递渠道参数结果
                    messageChannelHandler.handleMessage(messageRequest, channel, receivers);
                }
            }
            
        } catch (Exception e) {
            logger.error("消息处理异常: messageId={}", messageRequest.getMessageId(), e);
            throw e;
        }
    }
    
    /**
     * 提取消息属性
     */
    @SuppressWarnings("unchecked")
    private Map<String, Object> extractMessageProperties(Message message) {
        Map<String, Object> properties = message.getMessageProperties().getHeaders();
        
        // 添加一些基础属性
        properties.put("receivedTime", System.currentTimeMillis());
        properties.put("messageId", message.getMessageProperties().getMessageId());
        
        return properties;
    }
    
    /**
     * 处理消息错误
     */
    private void handleMessageError(Channel channel, long deliveryTag, 
                                   MessageRequestDTO messageRequest, Exception error) {
        try {
            // 更新消息状态为失败
            messageRecordRepo.updateStatusAndError(messageRequest.getRequestId(),
                    MessageStatus.FAILED,
                    error.getMessage());
            channel.basicReject(deliveryTag, false);
        } catch (Exception e) {
            logger.error("处理消息错误时异常: messageId={}", messageRequest.getMessageId(), e);
        }
    }
    
}
