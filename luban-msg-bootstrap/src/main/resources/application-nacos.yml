nacos:
  config:
    server-addr: ${NACOS_HOST}:${NACOS_PORT}
    username: ${NACOS_USER}
    password: ${NACOS_PASSWORD}
    namespace: ${NACOS_NAMESPACE}
    #配置自动刷新
    auto-refresh: true
    #配置分组，默认没有也可以
    group: ${spring.application.name}
    type: yaml
    bootstrap:
      enable: true
    data-ids: 配置
    remote-first: true
    ext-config:
      - group: DEFAULT_GROUP
        data-id: 额外配置
        type: yaml
        auto-refresh: true