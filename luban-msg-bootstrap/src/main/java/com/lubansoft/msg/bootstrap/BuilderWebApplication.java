package com.lubansoft.msg.bootstrap;

import com.lubansoft.base.core.helper.handle.LubanGlobalExceptionHandler;
import org.mybatis.spring.annotation.MapperScan;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.rabbit.annotation.EnableRabbit;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.security.servlet.SecurityAutoConfiguration;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import org.springframework.web.bind.annotation.RestControllerAdvice;

@EnableRabbit
@EnableTransactionManagement
@SpringBootApplication(scanBasePackages = {"com.lubansoft.msg", "com.lubansoft.base"} , exclude = {SecurityAutoConfiguration.class})
@EnableCaching
@EnableAspectJAutoProxy
@RestControllerAdvice
@MapperScan("com.lubansoft.msg.model.repository.repo")
public class BuilderWebApplication extends LubanGlobalExceptionHandler {

    private static Logger logger = LoggerFactory.getLogger(BuilderWebApplication.class);

    public static void main(String[] args) {
        SpringApplication.run(BuilderWebApplication.class, args);
        logger.error("【服务启动成功！！！！】");
    }
}