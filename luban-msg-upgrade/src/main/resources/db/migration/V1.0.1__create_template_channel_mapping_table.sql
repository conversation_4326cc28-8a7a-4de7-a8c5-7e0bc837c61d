-- 创建模板渠道映射配置表
-- 用于实现消息模板与渠道的多对多关系，支持参数映射配置

CREATE TABLE IF NOT EXISTS template_channel_mapping (
    -- 主键ID
    mapping_id VARCHAR(64) PRIMARY KEY,

    -- 模板ID，关联message_template表
    template_id VARCHAR(64) NOT NULL,

    -- 渠道类型
    channel_type VARCHAR(50) NOT NULL,

    -- 参数映射配置（JSON格式）
    -- 示例：{"code":"channelCode", "name":"channelName", "phone":"mobile"}
    parameter_mapping TEXT,

    -- 渠道特定的模板内容（可选，用于覆盖原模板内容）
    channel_template_content TEXT,

    -- 渠道特定的模板变量（JSON Schema格式，可选）
    channel_template_variables TEXT,

    -- 扩展配置（JSON格式，用于存储渠道特定的额外配置）
    -- 示例：{"timeout": 5000, "retryCount": 3, "template": {"subject": "邮件主题"}}
    extended_config TEXT,

    -- 创建时间
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,

    -- 更新时间
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,

    -- 创建者
    created_by VARCHAR(64),

    -- 更新者
    updated_by VARCHAR(64)
);

-- 创建索引
-- 模板ID索引（用于查询某个模板的所有渠道映射）
CREATE INDEX IF NOT EXISTS idx_template_channel_mapping_template_id
ON template_channel_mapping(template_id);

-- 渠道类型索引（用于按渠道类型查询）
CREATE INDEX IF NOT EXISTS idx_template_channel_mapping_channel_type
ON template_channel_mapping(channel_type);

-- 复合索引：模板ID + 渠道类型（用于查询特定的映射关系，确保唯一性）
CREATE UNIQUE INDEX IF NOT EXISTS uk_template_channel_mapping_template_channel
ON template_channel_mapping(template_id, channel_type);

-- 添加表注释
COMMENT ON TABLE template_channel_mapping IS '模板渠道映射配置表，用于实现消息模板与渠道的多对多关系';
COMMENT ON COLUMN template_channel_mapping.mapping_id IS '映射配置ID，主键';
COMMENT ON COLUMN template_channel_mapping.template_id IS '模板ID，关联message_template表';
COMMENT ON COLUMN template_channel_mapping.channel_type IS '渠道类型';
COMMENT ON COLUMN template_channel_mapping.parameter_mapping IS '参数映射配置，JSON格式存储字段映射关系';
COMMENT ON COLUMN template_channel_mapping.channel_template_content IS '渠道特定模板内容，可覆盖原模板内容';
COMMENT ON COLUMN template_channel_mapping.channel_template_variables IS '渠道特定模板变量，JSON Schema格式';
COMMENT ON COLUMN template_channel_mapping.extended_config IS '扩展配置，JSON格式存储渠道特定配置';
COMMENT ON COLUMN template_channel_mapping.created_at IS '创建时间';
COMMENT ON COLUMN template_channel_mapping.updated_at IS '更新时间';
COMMENT ON COLUMN template_channel_mapping.created_by IS '创建者';
COMMENT ON COLUMN template_channel_mapping.updated_by IS '更新者';
